<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.biosino.lf.pds.article.mapper.JournalMapper">

    <resultMap type="org.biosino.lf.pds.article.custbean.vo.SelectJournalVO" id="SelectJournalVOResult">
        <result property="journalId" column="journal_id"/>
        <result property="journalName" column="title"/>
        <result property="issnPrint" column="issn_print"/>
        <result property="issnElectronic" column="issn_electronic"/>
        <result property="issnElectronic" column="issn_electronic"/>
        <result property="publisherId" column="publisher_id"/>
        <result property="publisher" column="publisher_name"/>
        <result property="scriptId" column="script_id"/>
        <result property="scriptName" column="script_name"/>
    </resultMap>

    <select id="findToSelectJournal" resultMap="SelectJournalVOResult">
        SELECT DISTINCT
        j.id AS journal_id,
        j.title,
        j.issn_print,
        j.issn_electronic,
        j.publisher_id,
        p.name AS publisher_name,
        s.id AS script_id,
        s.script_name
        FROM
        tb_dds_journal j
        LEFT JOIN
        tb_dds_publisher p ON j.publisher_id = p.id
        LEFT JOIN
        tb_dds_journal_script s ON j.script_id = s.id
        LEFT JOIN
        tb_dds_scriptlabel_journal lj ON (j.id = lj.journal_id AND lj.scriptlabell_id = #{labelId})
        <where>
            ((j.script_id IS NULL) OR (lj.id IS NULL AND s.type @> array[#{scriptType}]::varchar[]))
            <if test="publisher != null and publisher.size() > 0">
                AND j.publisher_id IN
                <foreach collection="publisher" item="publisherId" open="(" separator="," close=")">
                    #{publisherId}
                </foreach>
            </if>
            <if test="journalName != null and journalName != ''">
                AND j.title ILIKE concat('%', #{journalName}, '%')
            </if>
            <if test="issnPrint != null and issnPrint != ''">
                AND j.issn_print ILIKE concat('%', #{issnPrint}, '%')
            </if>
            <if test="issnElectronic != null and issnElectronic != ''">
                AND j.issn_electronic ILIKE concat('%', #{issnElectronic}, '%')
            </if>
            <if test="hasScript == 'yes'">
                AND j.script_id IS NOT NULL
            </if>
            <if test="hasScript == 'no'">
                AND j.script_id IS NULL
            </if>
            <if test="scriptName != null and scriptName != ''">
                AND s.script_name ILIKE concat('%', #{scriptName}, '%')
            </if>
        </where>
        ORDER BY p.name, j.title
    </select>

    <select id="findAssignedJournalList" resultMap="SelectJournalVOResult">
        SELECT DISTINCT
        j.id AS journal_id,
        j.title,
        j.issn_print,
        j.issn_electronic,
        j.publisher_id,
        p.name AS publisher_name,
        s.id AS script_id,
        s.script_name,
        lj.create_time
        FROM
        tb_dds_journal j
        LEFT JOIN
        tb_dds_publisher p ON j.publisher_id = p.id
        LEFT JOIN
        tb_dds_journal_script s ON j.script_id = s.id
        INNER JOIN
        tb_dds_scriptlabel_journal lj ON (j.id = lj.journal_id AND lj.scriptlabell_id = #{labelId})
        <where>
            j.script_id IS NOT NULL
            <if test="publisher != null and publisher.size() > 0">
                AND j.publisher_id IN
                <foreach collection="publisher" item="publisherId" open="(" separator="," close=")">
                    #{publisherId}
                </foreach>
            </if>
            <if test="journalName != null and journalName != ''">
                AND j.title ILIKE concat('%', #{journalName}, '%')
            </if>
            <if test="issnPrint != null and issnPrint != ''">
                AND EXISTS (SELECT 1 FROM unnest(j.issn_print) AS issn WHERE issn ILIKE concat('%', #{issnPrint}, '%'))
            </if>
            <if test="issnElectronic != null and issnElectronic != ''">
                AND EXISTS (SELECT 1 FROM unnest(j.issn_electronic) AS issn WHERE issn ILIKE concat('%', #{issnElectronic}, '%'))
            </if>
            <if test="scriptName != null and scriptName != ''">
                AND s.script_name ILIKE concat('%', #{scriptName}, '%')
            </if>
        </where>
        ORDER BY lj.create_time DESC
    </select>

    <select id="findUnApplyJournalIds" resultType="java.lang.Long">
        SELECT DISTINCT
        j.id
        FROM
        tb_dds_journal j
        LEFT JOIN
        tb_dds_scriptlabel_journal lj ON (j.id = lj.journal_id AND lj.scriptlabell_id = #{labelId})
        WHERE
        j.id IN
        <foreach collection="journalIds" item="journalId" open="(" separator="," close=")">
            #{journalId}
        </foreach>
        AND j.script_id IS NOT NULL
        AND lj.id IS NULL
    </select>

    <!-- 在selectJournalList方法前添加 -->
    <resultMap type="org.biosino.lf.pds.article.domain.Journal" id="JournalResult">
        <result property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="isoabbreviation" column="isoabbreviation"/>
        <result property="publisherName" column="publisher_name"/>
        <result property="issnPrint" column="issn_print"/>
        <result property="issnElectronic" column="issn_electronic"/>
        <result property="uniqueNlmId" column="unique_nlm_id"/>
        <result property="issnHistory" column="issn_history"
                typeHandler="org.biosino.lf.pds.article.config.StringListArrayTypeHandler"/>
        <result property="uniqueHistory" column="unique_history"
                typeHandler="org.biosino.lf.pds.article.config.StringListArrayTypeHandler"/>
        <result property="medlineTa" column="medline_ta"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <!-- 关键：为source字段指定TypeHandler -->
        <result property="source" column="source"
                typeHandler="org.biosino.lf.pds.article.config.StringListArrayTypeHandler"/>
        <result property="scriptId" column="script_id"/>
        <result property="scriptName" column="script_name"/>
    </resultMap>

    <select id="selectJournalList" resultMap="JournalResult">
        select
        j.id, j.title, j.isoabbreviation, j.issn_print, j.issn_electronic, j.unique_nlm_id,
        j.issn_history, j.unique_history, j.medline_ta, j.status, j.create_time,
        j.update_time, j.source, j.script_id,
        p.name as publisher_name, s.script_name
        from
        tb_dds_journal j
        left join
        tb_dds_publisher p ON j.publisher_id = p.id
        left join
        tb_dds_journal_script s on j.script_id = s.id
        <where>
            <if test="titleMultiple != null and titleMultiple != ''">
                and j.title in
                <foreach collection="titleMultiple" item="title" open="(" separator="," close=")">
                    #{title}
                </foreach>
            </if>
            <if test="title != null and title != ''">
                and j.title like concat ('%', #{title}, '%')
            </if>
            <if test="isoabbreviation != null and isoabbreviation != ''">
                and j.isoabbreviation like concat ('%', #{isoabbreviation}, '%')
            </if>
            <if test="publisherName != null ">
                and p.name = #{publisherName}
            </if>
            <if test=" issnPrint!= null ">
                and j.issn_print = #{issnPrint}
            </if>
            <if test="issnElectronic != null">
                and j.issn_electronic = #{issnElectronic}
            </if>
            <if test="uniqueNlmId != null">
                and j.unique_nlm_id = #{uniqueNlmId}
            </if>
            <if test="source != null">
                and j.source @> array[#{source}]::text[]
            </if>
            <if test="status != null">
                and j.status = #{status}
            </if>
        </where>
        order by j.create_time desc
    </select>

    <select id="selectJournalById" resultMap="JournalResult"
            parameterType="java.lang.Long">
        select j.id, j.title, j.isoabbreviation, j.issn_print, j.issn_electronic, j.unique_nlm_id,
               j.issn_history, j.unique_history, j.medline_ta, j.status, j.create_time,
               j.update_time, j.source, j.script_id,
               p.name as publisher_name
        from
        tb_dds_journal j
        left join
        tb_dds_publisher p ON j.publisher_id = p.id
        <where>
            j.id = #{id}
        </where>
    </select>

    <update id="batchUpdateScriptId">
        UPDATE tb_dds_journal
        SET script_id = #{scriptId},
        update_time = #{updateTime}
        WHERE id IN
        <foreach collection="journalIds" item="journalId" open="(" separator="," close=")">
            #{journalId}
        </foreach>
    </update>

    <update id="updatePublisherIdBatch" parameterType="java.util.List">
        update tb_dds_journal
        set publisher_id = #{targetId}
        where publisher_id in
        <foreach collection="sourceIds" item="sourceId" open="(" separator="," close=")">
        #{sourceId}
        </foreach>
    </update>

    <!-- ISSN uniqueness validation queries -->
    <select id="findConflictingJournalsByIssnPrint" resultMap="JournalResult">
        SELECT j.id, j.title, j.issn_print, j.issn_electronic, j.unique_nlm_id,
               j.issn_history, j.unique_history
        FROM tb_dds_journal j
        WHERE (j.issn_print = #{issnPrint}
               OR j.issn_electronic = #{issnPrint}
               OR j.unique_nlm_id = #{issnPrint}
               OR j.issn_history @> ARRAY[#{issnPrint}]::text[]
               OR j.unique_history @> ARRAY[#{issnPrint}]::text[])
        <if test="excludeIds != null and excludeIds.size() > 0">
            AND j.id NOT IN
            <foreach collection="excludeIds" item="excludeId" open="(" separator="," close=")">
                #{excludeId}
            </foreach>
        </if>
        LIMIT 1
    </select>

    <select id="findConflictingJournalsByIssnElectronic" resultMap="JournalResult">
        SELECT j.id, j.title, j.issn_print, j.issn_electronic, j.unique_nlm_id,
               j.issn_history, j.unique_history
        FROM tb_dds_journal j
        WHERE (j.issn_print = #{issnElectronic}
               OR j.issn_electronic = #{issnElectronic}
               OR j.unique_nlm_id = #{issnElectronic}
               OR j.issn_history @> ARRAY[#{issnElectronic}]::text[]
               OR j.unique_history @> ARRAY[#{issnElectronic}]::text[])
        <if test="excludeIds != null and excludeIds.size() > 0">
            AND j.id NOT IN
            <foreach collection="excludeIds" item="excludeId" open="(" separator="," close=")">
                #{excludeId}
            </foreach>
        </if>
        LIMIT 1
    </select>

    <select id="findConflictingJournalsByUniqueNlmId" resultMap="JournalResult">
        SELECT j.id, j.title, j.issn_print, j.issn_electronic, j.unique_nlm_id,
               j.issn_history, j.unique_history
        FROM tb_dds_journal j
        WHERE (j.issn_print = #{uniqueNlmId}
               OR j.issn_electronic = #{uniqueNlmId}
               OR j.unique_nlm_id = #{uniqueNlmId}
               OR j.issn_history @> ARRAY[#{uniqueNlmId}]::text[]
               OR j.unique_history @> ARRAY[#{uniqueNlmId}]::text[])
        <if test="excludeIds != null and excludeIds.size() > 0">
            AND j.id NOT IN
            <foreach collection="excludeIds" item="excludeId" open="(" separator="," close=")">
                #{excludeId}
            </foreach>
        </if>
        LIMIT 1
    </select>

    <select id="findConflictingJournalsByIssnHistory" resultMap="JournalResult">
        SELECT j.id, j.title, j.issn_print, j.issn_electronic, j.unique_nlm_id,
               j.issn_history, j.unique_history
        FROM tb_dds_journal j
        WHERE (j.issn_print = #{issnValue}
               OR j.issn_electronic = #{issnValue}
               OR j.unique_nlm_id = #{issnValue}
               OR j.issn_history @> ARRAY[#{issnValue}]::text[]
               OR j.unique_history @> ARRAY[#{issnValue}]::text[])
        <if test="excludeIds != null and excludeIds.size() > 0">
            AND j.id NOT IN
            <foreach collection="excludeIds" item="excludeId" open="(" separator="," close=")">
                #{excludeId}
            </foreach>
        </if>
        LIMIT 1
    </select>

    <select id="findConflictingJournalsByUniqueHistory" resultMap="JournalResult">
        SELECT j.id, j.title, j.issn_print, j.issn_electronic, j.unique_nlm_id,
               j.issn_history, j.unique_history
        FROM tb_dds_journal j
        WHERE (j.issn_print = #{uniqueValue}
               OR j.issn_electronic = #{uniqueValue}
               OR j.unique_nlm_id = #{uniqueValue}
               OR j.issn_history @> ARRAY[#{uniqueValue}]::text[]
               OR j.unique_history @> ARRAY[#{uniqueValue}]::text[])
        <if test="excludeIds != null and excludeIds.size() > 0">
            AND j.id NOT IN
            <foreach collection="excludeIds" item="excludeId" open="(" separator="," close=")">
                #{excludeId}
            </foreach>
        </if>
        LIMIT 1
    </select>


</mapper>
