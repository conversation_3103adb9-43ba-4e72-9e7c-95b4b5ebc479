# 测试环境配置
server:
  # 服务器的HTTP端口
  port: 8082
  servlet:
    # 应用的访问路径
    context-path: /

# 数据源配置
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: org.postgresql.Driver
    # 测试环境数据库连接 (请根据实际环境修改)
    url: **********************************************************************************************************************************************************************
    username: ${DB_USERNAME:plosp_test}
    password: ${DB_PASSWORD:test_password}
    druid:
      # 测试环境连接池配置
      initialSize: 5
      minIdle: 10
      maxActive: 50
      maxWait: 60000
      connectTimeout: 30000
      socketTimeout: 60000
      timeBetweenEvictionRunsMillis: 60000
      minEvictableIdleTimeMillis: 300000
      maxEvictableIdleTimeMillis: 900000
      validationQuery: SELECT version()
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        allow:
        url-pattern: /druid/*
        login-username: test
        login-password: test123
      filter:
        stat:
          enabled: true
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
  data:
    redis:
      # 测试环境Redis配置
      host: ${REDIS_HOST:test-redis-host}
      port: ${REDIS_PORT:6379}
      database: ${REDIS_DATABASE:14}
      password: ${REDIS_PASSWORD:}
      timeout: 10s
      lettuce:
        pool:
          min-idle: 2
          max-idle: 10
          max-active: 20
          max-wait: -1ms
  # 邮件配置 (测试环境)
  mail:
    host: ${MAIL_HOST:smtp.test-domain.com}
    username: ${MAIL_USERNAME:<EMAIL>}
    password: ${MAIL_PASSWORD:test_mail_password}
    port: ${MAIL_PORT:587}
    properties.mail.smtp:
      auth: true
      starttls:
        enable: true

# Elasticsearch配置
easy-es:
  enable: true
  address: ${ES_ADDRESS:test-es-host:9200}
  global-config:
    print-dsl: true

# 测试环境日志配置
logging:
  level:
    org.biosino.lf.plosp.portal: debug
    org.biosino.lf.pds: debug
    org.springframework.security: info
    org.springframework.web: info
    org.springframework: info
  file:
    name: logs/plosp-portal-test.log
    max-size: 50MB
    max-history: 7

# 应用配置
app:
  # 测试环境文件路径
  profile: ${APP_PROFILE_PATH:/tmp/plosp-portal-test/uploadPath}
  data-home: ${APP_DATA_HOME:/tmp/plosp-portal-test/data}

# 测试环境token配置
token:
  # 测试环境令牌有效期（单位分钟）
  expireTime: ${JWT_EXPIRE_TIME:720}

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,env
  endpoint:
    health:
      show-details: when-authorized
