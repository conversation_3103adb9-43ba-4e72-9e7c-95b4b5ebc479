# PDF附件迁移脚本说明

## 脚本功能

`pdf_attachment_transfer.py` 脚本用于将v1 MySQL数据库中的PDF附件数据迁移到v2 PostgreSQL数据库。

## 迁移逻辑

### 数据源和目标
- **源表**: v1 MySQL `tb_dds_article_attachment` (type='PDF')
- **目标表**: v2 PostgreSQL `tb_dds_file`

### 迁移范围
- 迁移条件: `pmid < 800000000000 OR pmid > 802000000000`
- 排除范围: `pmid >= 800000000000 AND pmid <= 802000000000` (这部分数据已由其他脚本处理)

### 字段映射关系

| v1字段 | v2字段 | 说明 |
|--------|--------|------|
| - | id | 使用雪花算法生成新ID |
| origin_name | file_name | 原始文件名 |
| file_suffix | content_type | 文件类型/后缀 |
| - | file_size | 通过文件路径计算文件大小 |
| file_md5 | md5 | 文件MD5值 |
| local_path | file_path | 文件存储路径 |
| create_time | create_time | 创建时间 |
| 'PDF' | type | 固定为PDF类型 |
| pmid | doc_id | 通过pmid查找对应的v2文章ID |
| source | source | 来源信息 |

### 文章ID查找逻辑

脚本会根据v1的pmid值在v2数据库中查找对应的文章ID：

1. **如果pmid以"90000"开头**: 在v2的`tb_dds_article.pmc_id`字段中查找
2. **其他情况**: 在v2的`tb_dds_article.pmid`字段中查找

如果找不到对应的文章，该附件记录会被跳过并记录警告日志。

## 使用方法

### 1. 环境准备
```bash
pip install pymysql psycopg2-binary
```

### 2. 配置数据库连接
编辑脚本中的数据库配置：
```python
V1_MYSQL_CONFIG = {
    'host': '************',
    'port': 32526,
    'user': 'root',
    'password': 'Lfgzs@2021',
    'database': 'plosp_online',
    'charset': 'utf8mb4'
}

V2_PGSQL_CONFIG = {
    'host': '************',
    'port': 31909,
    'user': 'postgres',
    'password': 'Biosino+2025',
    'database': 'plosp',
    'options': '-c search_path=public'
}
```

### 3. 执行迁移
```bash
python pdf_attachment_transfer.py
```

## 输出和日志

### 控制台输出
- 迁移进度信息
- 成功和失败统计
- 验证结果

### 日志文件
- 文件名: `pdf_migration.log`
- 包含详细的迁移过程和错误信息

## 验证功能

脚本执行完成后会自动进行验证：
1. 比较v1和v2中PDF附件的数量
2. 统计文件大小信息
3. 输出验证结果

## 注意事项

1. **文件大小计算**: 脚本会尝试根据文件路径计算实际文件大小，如果文件不存在会记录警告
2. **批量处理**: 使用批量插入提高性能，默认批次大小为100条记录
3. **错误处理**: 单条记录失败不会影响整体迁移，会记录详细错误信息
4. **事务安全**: 使用数据库事务确保数据一致性

## 故障排除

### 常见问题
1. **数据库连接失败**: 检查网络连接和数据库配置
2. **文章ID未找到**: 确保对应的文章已经迁移到v2数据库
3. **文件大小计算失败**: 检查文件路径是否正确，文件是否存在

### 日志分析
查看 `pdf_migration.log` 文件获取详细的错误信息和迁移过程。
