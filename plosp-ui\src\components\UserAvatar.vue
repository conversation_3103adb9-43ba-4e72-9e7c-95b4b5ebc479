<template>
  <div class="user-avatar-container">
    <!-- 未登录状态 -->
    <div v-if="!authStore.isLoggedIn" class="auth-buttons">
      <el-button
        type="primary"
        size="default"
        @click="goToLogin"
        class="login-btn"
      >
        登录
      </el-button>
      <el-button
        size="default"
        @click="goToRegister"
        class="register-btn"
      >
        注册
      </el-button>
    </div>

    <!-- 已登录状态 -->
    <el-dropdown v-else trigger="click" class="user-dropdown">
      <div class="user-info">
        <el-avatar
          :size="32"
          :src="userAvatar"
          class="user-avatar"
        >
          <el-icon><User /></el-icon>
        </el-avatar>
        <span class="user-name">{{ authStore.userName || '用户' }}</span>
        <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
      </div>

      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item @click="goToUserCenter">
            <el-icon><User /></el-icon>
            个人中心
          </el-dropdown-item>
          <el-dropdown-item @click="goToEditProfile">
            <el-icon><Edit /></el-icon>
            编辑资料
          </el-dropdown-item>
          <el-dropdown-item divided @click="handleLogout">
            <el-icon><SwitchButton /></el-icon>
            退出登录
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessageBox } from 'element-plus'
import { User, ArrowDown, Edit, SwitchButton } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'

// 路由
const router = useRouter()

// 认证状态管理
const authStore = useAuthStore()

// 用户头像 (可以根据实际需求从用户信息中获取)
const userAvatar = computed(() => {
  // 这里可以返回用户的真实头像URL
  // return authStore.userInfo?.avatar || ''
  return ''
})

// 跳转到登录页
const goToLogin = () => {
  router.push('/login')
}

// 跳转到注册页
const goToRegister = () => {
  router.push('/register')
}

// 跳转到用户中心
const goToUserCenter = () => {
  router.push('/user-center')
}

// 跳转到编辑资料页
const goToEditProfile = () => {
  router.push('/edit-profile')
}

// 处理退出登录
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要退出登录吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await authStore.logout()
    router.push('/')
  } catch (error) {
    // 用户取消退出
  }
}
</script>

<style lang="scss" scoped>
.user-avatar-container {
  display: flex;
  align-items: center;

  .auth-buttons {
    display: flex;
    gap: 12px;

    .login-btn {
      height: 36px;
      padding: 0 20px;
      border-radius: 18px;
      font-weight: 500;
      font-size: 14px;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
      }
    }

    .register-btn {
      height: 36px;
      padding: 0 20px;
      border-radius: 18px;
      font-weight: 500;
      font-size: 14px;
      border: 1px solid #409eff;
      color: #409eff;
      background: transparent;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-1px);
        background: #409eff;
        color: white;
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
      }
    }
  }

  .user-dropdown {
    cursor: pointer;

    .user-info {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 4px 8px;
      border-radius: 6px;
      transition: background-color 0.2s;

      &:hover {
        background-color: var(--el-fill-color-light);
      }

      .user-avatar {
        flex-shrink: 0;
      }

      .user-name {
        font-size: 14px;
        color: var(--el-text-color-primary);
        max-width: 100px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .dropdown-icon {
        font-size: 12px;
        color: var(--el-text-color-secondary);
        transition: transform 0.2s;
      }
    }

    &.is-active .user-info .dropdown-icon {
      transform: rotate(180deg);
    }
  }
}

:deep(.el-dropdown-menu__item) {
  display: flex;
  align-items: center;
  gap: 8px;

  .el-icon {
    font-size: 14px;
  }
}
</style>
