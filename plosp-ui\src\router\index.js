import { createRouter, createWebHistory } from 'vue-router'
import Home from '../views/Home.vue'
import { authGuard } from '@/utils/permission'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: Home,
      meta: {
        title: '首页 - PLOSP科学文献私人订制图书馆'
      }
    },
    {
      path: '/literature',
      name: 'literature',
      component: () => import('../views/Literature.vue'),
      meta: {
        title: '文献 - PLOSP科学文献私人订制图书馆'
      }
    },
    {
      path: '/literatureDetail',
      name: 'literatureDetail',
      component: () => import('../views/LiteratureDetail.vue'),
      meta: {
        title: '文献 - PLOSP科学文献私人订制图书馆'
      }
    },
    {
      path: '/data',
      name: 'data',
      component: () => import('../views/DataView.vue'),
      meta: {
        title: '获取数据 - PLOSP科学文献私人订制图书馆'
      }
    },
    {
      path: '/statistics',
      name: 'statistics',
      component: () => import('../views/Statistics.vue'),
      meta: {
        title: '文献统计 - PLOSP科学文献私人订制图书馆'
      }
    },
    {
      path: '/help',
      name: 'help',
      component: () => import('../views/Help.vue'),
      meta: {
        title: '帮助 - PLOSP科学文献私人订制图书馆'
      }
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/Login.vue'),
      meta: {
        title: '登录 - PLOSP科学文献私人订制图书馆'
      }
    },
    {
      path: '/register',
      name: 'register',
      component: () => import('../views/Register.vue'),
      meta: {
        title: '注册 - PLOSP科学文献私人订制图书馆'
      }
    },
    {
      path: '/forgot-password',
      name: 'forgot-password',
      component: () => import('../views/ForgotPassword.vue'),
      meta: {
        title: '忘记密码 - PLOSP科学文献私人订制图书馆'
      }
    },
    {
      path: '/account-activation',
      name: 'account-activation',
      component: () => import('../views/AccountActivation.vue'),
      meta: {
        title: '账户激活 - PLOSP科学文献私人订制图书馆'
      }
    },
    {
      path: '/user-center',
      name: 'user-center',
      component: () => import('../views/UserCenter/index.vue'),
      meta: {
        title: '用户中心 - PLOSP科学文献私人订制图书馆',
        requiresAuth: true
      }
    },
    {
      path: '/edit-profile',
      name: 'edit-profile',
      component: () => import('../views/UserCenter/components/EditProfile.vue'),
      meta: {
        title: '编辑个人资料 - PLOSP科学文献私人订制图书馆',
        requiresAuth: true
      }
    }
  ]
})

// 路由前置守卫
router.beforeEach(async (to, from, next) => {
  // 设置页面标题
  document.title = to.meta.title || 'PLOSP科学文献私人订制图书馆'

  // 权限检查
  await authGuard(to, from, next)
})

export default router
