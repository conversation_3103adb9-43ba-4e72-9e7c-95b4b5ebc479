package org.biosino.lf.pds.quartz.task.pds;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.pds.article.custbean.dto.SiteDTO;
import org.biosino.lf.pds.article.custbean.dto.email.SiteErrorDTO;
import org.biosino.lf.pds.article.custbean.vo.SiteVO;
import org.biosino.lf.pds.common.core.mail.MailService;
import org.biosino.lf.pds.common.core.mail.MailTemplateEnum;
import org.biosino.lf.pds.common.enums.StatusEnums;
import org.biosino.lf.pds.common.enums.task.SiteRunStatusEnum;
import org.biosino.lf.pds.system.service.ISysConfigService;
import org.biosino.lf.pds.task.service.ITbDdsSiteService;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 节点运行状态监控
 *
 * <AUTHOR>
 */
@Slf4j
@Component("siteMonitorTask")
@RequiredArgsConstructor
public class SiteMonitorTask {
    private final ITbDdsSiteService tbDdsSiteService;
    private final MailService mailService;
    private final ISysConfigService sysConfigService;

    /**
     * 每天凌晨1点执行
     * 0 0 1 * * ?
     */
    public void sendExceptionEmail() {
        final String email = sysConfigService.selectConfigByKey("site.admin.email");
        sendExceptionEmailByDays(1, email);
    }

    /**
     * 每个周日凌晨1:30执行
     * 0 30 1 ? * 1
     */
    public void sendExceptionFiveDayEmail() {
        final String email = sysConfigService.selectConfigByKey("site.observer.email");
        sendExceptionEmailByDays(5, email);
    }

    private void sendExceptionEmailByDays(final int days, final String email) {
        final SiteDTO siteDTO = new SiteDTO();
        siteDTO.setFindHandshakeFlag(true);
        siteDTO.setExceptionDays(days);
        final List<SiteVO> siteList = tbDdsSiteService.selectSiteList(siteDTO);
        if (CollUtil.isNotEmpty(siteList)) {
            final List<SiteErrorDTO> data = new ArrayList<>();
            for (SiteVO vo : siteList) {
                if (StatusEnums.ENABLE.getCode().toString().equals(vo.getStatus())) {
                    final String heartbeatSignal = vo.getHeartbeatSignal();
                    if (SiteRunStatusEnum.exception.getText().equals(heartbeatSignal) || SiteRunStatusEnum.disconnect.getText().equals(heartbeatSignal)) {
                        log.debug("节点异常: {}", vo.getSiteName());
                        final SiteErrorDTO dto = new SiteErrorDTO();
                        dto.setSiteId(vo.getId());
                        dto.setSiteName(vo.getSiteName());
                        dto.setRunStatus(heartbeatSignal);
//                    dto.setRemark();
                        data.add(dto);
                    }
                }
            }

            if (CollUtil.isNotEmpty(data)) {
                final Map<String, Object> params = new HashMap<>();
                params.put("siteErrorList", data);
                mailService.sendMailWithTemplate(email, MailTemplateEnum.SITE_ERROR, params);
            }
        }
    }
}
