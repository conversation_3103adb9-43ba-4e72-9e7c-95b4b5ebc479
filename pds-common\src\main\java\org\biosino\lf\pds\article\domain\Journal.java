package org.biosino.lf.pds.article.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.biosino.lf.pds.article.config.StringListArrayTypeHandler;

import java.util.Date;
import java.util.List;

/**
 * 期刊表
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "tb_dds_journal", autoResultMap = true)
public class Journal {
    /**
     * 文档ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 出版社ID
     */
    @TableField("publisher_id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long publisherId;

    @TableField(exist = false)
    private String publisherName;  // 出版社名称

    @TableField(value = "unique_nlm_id")
    private String uniqueNlmId;

    @TableField(value = "issn_print")
    private String issnPrint;

    @TableField(value = "issn_electronic")
    private String issnElectronic;

    @TableField("title")
    private String title;

    @TableField("isoabbreviation")
    private String isoabbreviation;

    @TableField("pmcabbreviation")
    private String pmcabbreviation;

    @TableField("jcrabbreviation")
    private String jcrabbreviation;

    @TableField(value = "issn_history", typeHandler = StringListArrayTypeHandler.class)
    private List<String> issnHistory;

    @TableField(value = "unique_history", typeHandler = StringListArrayTypeHandler.class)
    private List<String> uniqueHistory;

    @TableField("medline_ta")
    private String medlineTa;

    /**
     * 来源，PubMed、JCR、PMC等
     */
    @TableField(value = "source", typeHandler = StringListArrayTypeHandler.class)
    private List<String> source;

    /**
     * 来源类型，熊刚治理的：custom。XML插入时新增的：system
     */
    @TableField("source_type")
    private String sourceType;

    /**
     * 状态 正常0  停用1
     */
    @TableField("status")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * tb_dds_journal_script表脚本id
     */
    @TableField("script_id")
    private Integer scriptId;

    /**
     * 脚本名称
     */
    @TableField(exist = false)
    private String scriptName;  // 出版社名称
}
