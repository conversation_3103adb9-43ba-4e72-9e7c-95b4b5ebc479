package org.biosino.lf.pds.article.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HtmlUtil;
import org.biosino.lf.pds.article.domain.*;
import org.biosino.lf.pds.article.dto.ArticleInfoDTO;
import org.biosino.lf.pds.common.enums.JournalSourceTypeEnums;
import org.biosino.lf.pds.common.enums.SourceTypeEnums;
import org.biosino.lf.pds.common.exception.ServiceException;
import org.biosino.lf.pds.common.utils.MonthUtil;
import org.biosino.lf.pds.common.utils.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.parser.Parser;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * jatsxml解析器
 * <p>
 * BioRxiv, MedRxiv预印本平台的格式都是和PMC格式一样，所以都用PMC解析器
 *
 * <AUTHOR>
 * @date 2025/5/13
 */
@Service
public class PmcXmlParse implements XmlParseService {

    @Override
    public ArticleInfoDTO parse(String content, SourceTypeEnums typeEnums) {
        if (StrUtil.isBlank(content)) {
            throw new ServiceException("XML文件内容为空");
        }

//        long start = System.currentTimeMillis();
//        if (!XmlUtils.isWellFormedXML(content)) {
//            throw new ServiceException("XML格式不合法");
//        }
//        long end = System.currentTimeMillis();
//        System.out.println("doSomething 耗时: " + (end - start) + " ms");

        ArticleInfoDTO articleInfoDTO = new ArticleInfoDTO();
        Document doc = Jsoup.parse(content, "", Parser.xmlParser());

        // 解析基本信息
        Article article = parseBasicInfo(doc, typeEnums);
        articleInfoDTO.setArticle(article);

        // 解析期刊信息
        articleInfoDTO.setJournal(parseJournal(doc, typeEnums));

        // 解析出版社
        articleInfoDTO.setPublisher(parsePublisher(doc));

        // 解析发布类型
        articleInfoDTO.setPubTypes(parsePubTypes(doc));

        // 解析其他ID
        articleInfoDTO.setArticleOtherIds(parseArticleOtherId(doc));

        // 解析引用文献
        if (SourceTypeEnums.PMC.equals(typeEnums)) {
            articleInfoDTO.setReferences(parsePMCReferences(doc));
        } else {
            articleInfoDTO.setReferences(parseRxivReferences(doc));
        }

        // 作者信息
        articleInfoDTO.setArticleAuthors(parseArticleAuthor(doc, typeEnums));

        return articleInfoDTO;
    }

    private Journal parseJournal(Document doc, SourceTypeEnums typeEnums) {
        Journal journal = new Journal();
        journal.setSource(CollUtil.newArrayList(typeEnums.name()));
        journal.setSourceType(JournalSourceTypeEnums.system.name());

        Elements journalEles = doc.select("article > front > journal-meta");

        Elements journalIdEles = journalEles.select("journal-id[journal-id-type=nlm-journal-id]");
        if (StrUtil.isNotBlank(journalIdEles.text())) {
            journal.setUniqueNlmId(StrUtil.trimToNull(journalIdEles.text()));
        }
        Elements medlineTaEles = journalEles.select("journal-id[journal-id-type=nlm-ta]");
        if (StrUtil.isNotBlank(medlineTaEles.text())) {
            journal.setMedlineTa(StrUtil.trimToNull(medlineTaEles.text()));
        }

        Elements isoAbbrEles = journalEles.select("journal-id[journal-id-type=iso-abbrev]");
        if (StrUtil.isNotBlank(isoAbbrEles.text())) {
            journal.setIsoabbreviation(StrUtil.trimToNull(isoAbbrEles.text()));
        }

        Elements titleEles = journalEles.select("journal-title-group > journal-title");

        // 适配journal-title在journal-meta层级的期刊，样例：PMC1081509.xml
        if (StrUtil.isBlank(titleEles.text())) {
            titleEles = doc.select("journal-title");
        }
        journal.setTitle(StrUtil.trimToNull(titleEles.text()));

        if (StrUtil.isBlank(journal.getTitle())) {
            throw new ServiceException("期刊标题为空");
        }

        Elements issnEles = journalEles.select("issn");
        for (Element issn : issnEles) {
            String pubType = issn.attr("pub-type");
            if (StrUtil.isBlank(issn.text())) {
                continue;
            }
            if ("ppub".equals(pubType)) {
                journal.setIssnPrint(StrUtil.trimToNull(issn.text()));
            } else if ("epub".equals(pubType)) {
                journal.setIssnElectronic(StrUtil.trimToNull(issn.text()));
            }
        }

        return journal;
    }

    private Publisher parsePublisher(Document doc) {
        Publisher publisher = new Publisher();
        Elements publisherEles = doc.select("article > front > journal-meta > publisher > publisher-name");
        publisher.setName(StrUtil.trimToNull(publisherEles.text()));
        Elements publisherIocEles = doc.select("article > front > journal-meta > publisher > publisher-loc");
        publisher.setIoc(StrUtil.trimToNull(publisherIocEles.text()));

        if (StringUtils.isBlank(publisher.getName()) && StringUtils.isBlank(publisher.getIoc())) {
            return null;
        }
        return publisher;
    }

    private Article parseBasicInfo(Document doc, SourceTypeEnums typeEnums) {
        Article article = new Article();

        // 设置来源
        article.setSource(CollUtil.newArrayList(typeEnums.name()));

        // 解析PMID
        Elements pmidEles = doc.select("article > front > article-meta > article-id[pub-id-type=pmid]");
        String pmid = StrUtil.trimToNull(pmidEles.text());
        // 适配有可能PMID有2个的情况，如 PMC11719116.xml
        if (pmidEles.size() > 1) {
            pmid = StrUtil.trimToNull(pmidEles.get(0).text());
        }
        if (StrUtil.isNotBlank(pmid)) {
            if (StrUtil.isNumeric(pmid)) {
                article.setPmid(Long.parseLong(pmid));
            } else {
                throw new ServiceException("PMID 不是数字");
            }
        }

        if (SourceTypeEnums.PMC.equals(typeEnums)) {
            // 解析PMC ID
            Elements pmcidEles = doc.select("article > front > article-meta > article-id[pub-id-type=pmc]");
            String pmcid = StrUtil.trimToNull(pmcidEles.text());
            // 适配新版XML格式
            if (StrUtil.isBlank(pmcid)) {
                pmcidEles = doc.select("article > front > article-meta > article-id[pub-id-type=pmcaid]");
                pmcid = pmcidEles.text();
            }
            if (StrUtil.isNotBlank(pmcid)) {
                String pmc = pmcid.replace("PMC", "");
                if (StrUtil.isNumeric(pmc)) {
                    article.setPmcId(Long.parseLong(pmc));
                } else {
                    throw new ServiceException("PMCID 不是数字");
                }
            } else {
                throw new ServiceException("PMCID 为空");
            }
        }

        // DOI
        Elements doiEles = doc.select("article > front > article-meta > article-id[pub-id-type=doi]");
        article.setDoi(StrUtil.trimToNull(doiEles.text()));

        // 标题 (可能为空 PMC6113747)
        Elements titleEles = doc.select("article > front > article-meta > title-group > article-title");
        String title = titleEles.html();
        if (StrUtil.isNotBlank(title)) {
            // 可能包含非标题内容（PMC5273367）
            title = HtmlUtil.removeHtmlTag(titleEles.html(), "fn");
            title = title.replaceAll("\\s+", " ");
            article.setTitle(StrUtil.trimToNull(title));
        }

        // 作者列表
        Elements contribEles = doc.select("article > front > article-meta > contrib-group > contrib");
        if (CollUtil.isNotEmpty(contribEles)) {
            List<String> authors = new ArrayList<>();
            for (Element ele : contribEles) {
                // 处理团队作者
                if (StrUtil.isNotBlank(ele.select("collab").text())) {
                    Element cel = ele.clone();
                    cel.select("collab").select("contrib").remove();
                    cel.select("collab").select("contrib-group").remove();
                    authors.add(cel.select("collab").text());
                } else {
                    String foreName = ele.select("name > given-names").text();
                    String lastName = ele.select("name > surname").text();
                    String fullName = StringUtils.removeDot(foreName) + " " + lastName;
                    authors.add(fullName.trim());
                }
            }
            article.setAuthor(authors);
        }

        // 机构信息
        Elements affEles = doc.select("article > front > article-meta > contrib-group > aff > institution");
        if (CollUtil.isEmpty(affEles)) {
            affEles = doc.select("article > front > article-meta > contrib-group > aff > addr-line");
        }
        if (CollUtil.isNotEmpty(affEles)) {
            List<String> affiliations = new ArrayList<>();
            for (Element ele : affEles) {
                String affText = ele.text();
                if (StrUtil.isNotBlank(affText)) {
                    affiliations.add(affText.replaceAll("\\s+", " "));
                }
            }
            article.setAffiliation(affiliations);
        }

        // 发布日期 - 优先从印刷出版日期(ppub)获取，其次从电子出版日期(epub)获取
        Element printEle = doc.select("article > front > article-meta > pub-date[pub-type=ppub]").last();
        Element epubEle = doc.select("article > front > article-meta > pub-date[pub-type=epub]").last();

        // 初始化OtherDate列表
        if (article.getOtherDate() == null) {
            article.setOtherDate(new ArrayList<>());
        }

        boolean ppubNull = true;
        // 处理印刷出版日期(ppub)
        if (printEle != null) {
            PubMedPubDate printDate = new PubMedPubDate();
            printDate.setPubStatus("ppub");

            // 处理日期
            if (CollUtil.isNotEmpty(printEle.select("year"))) {
                String year = printEle.select("year").last().text();
                if (StrUtil.isNumeric(year)) {
                    ppubNull = false;
                    article.setPublishedYear(Integer.parseInt(year));
                    article.setYear(article.getPublishedYear());
                    printDate.setYear(year);
                }
            }
            if (CollUtil.isNotEmpty(printEle.select("month"))) {
                String month = printEle.select("month").last().text();
                Integer monthInt;
                if (StrUtil.isNumeric(month)) {
                    monthInt = Integer.parseInt(month);
                } else {
                    monthInt = MonthUtil.getMonth(month);
                }
                article.setPublishedMonth(monthInt);
                printDate.setMonth(month);
            }
            if (CollUtil.isNotEmpty(printEle.select("day"))) {
                String day = printEle.select("day").last().text();
                if (StrUtil.isNumeric(day)) {
                    article.setPublishedDay(Integer.parseInt(day));
                    printDate.setDay(day);
                }
            }

            // 添加到OtherDate
            article.getOtherDate().add(printDate);
        }

        // 如果印刷出版日期中没有值，则从电子出版日期获取
        if (epubEle != null && (article.getPublishedDay() == null || article.getPublishedMonth() == null || article.getPublishedYear() == null)) {
            PubMedPubDate epubDate = new PubMedPubDate();
            epubDate.setPubStatus("epub");

            // 处理日期
            if (CollUtil.isNotEmpty(epubEle.select("year"))) {
                String year = epubEle.select("year").last().text();
                if (StrUtil.isNumeric(year)) {
                    if (ppubNull) {
                        article.setPublishedYear(Integer.parseInt(year));
                    }
                    epubDate.setYear(year);
                    if (article.getYear() == null) {
                        article.setYear(Integer.parseInt(year));
                    }
                }
            }
            if (CollUtil.isNotEmpty(epubEle.select("month"))) {
                String month = epubEle.select("month").last().text();
                Integer monthInt;
                if (StrUtil.isNumeric(month)) {
                    monthInt = Integer.parseInt(month);
                } else {
                    monthInt = MonthUtil.getMonth(month);
                }
                if (ppubNull) {
                    article.setPublishedMonth(monthInt);
                }
                epubDate.setMonth(month);
            }
            if (CollUtil.isNotEmpty(epubEle.select("day"))) {
                String day = epubEle.select("day").last().text();
                if (StrUtil.isNumeric(day)) {
                    if (ppubNull) {
                        article.setPublishedDay(Integer.parseInt(day));
                    }
                    epubDate.setDay(day);
                }
            }

            // 添加到OtherDate
            article.getOtherDate().add(epubDate);
        }

        // 解析卷号
        Elements volumeEle = doc.select("article > front > article-meta > volume");
        article.setVolume(StrUtil.trimToNull(volumeEle.text()));

        // 解析期号
        Elements issueEles = doc.select("article > front > article-meta > issue");
        if (CollUtil.isNotEmpty(issueEles) && issueEles.text().length() < 300) {
            article.setIssue(StrUtil.trimToNull(issueEles.text()));
        }

        // 解析页码
        Elements fPageEles = doc.select("article > front > article-meta > fpage");
        Elements lPageEles = doc.select("article > front > article-meta > lpage");
        if (CollUtil.isNotEmpty(fPageEles) || CollUtil.isNotEmpty(lPageEles)) {
            StringBuilder page = new StringBuilder();
            if (CollUtil.isNotEmpty(fPageEles)) {
                page.append(fPageEles.text());
            }
            if (CollUtil.isNotEmpty(lPageEles)) {
                page.append("-").append(lPageEles.text());
            }
            article.setPage(page.toString());
        }

        // 解析版权信息
        Elements copyEles = doc.select("article > front > article-meta > permissions > copyright-statement");
        article.setCopyright(StrUtil.trimToNull(copyEles.text()));

        // 解析摘要
        Elements absEles = doc.select("article > front > article-meta > abstract");
        if (CollUtil.isNotEmpty(absEles)) {
            // 处理摘要，移除title标签及其内容
            Element abstractElement = absEles.get(0).clone();
            abstractElement.select("title").remove();
            String absHtml = abstractElement.html();
            if (StrUtil.isNotBlank(absHtml)) {
                // 使用正则表达式将多个连续空白字符（空格、制表符、换行符等）缩减为一个空格
                absHtml = absHtml.replaceAll("\\s+", " ");
                article.setArticleAbstract(StrUtil.trimToNull(absHtml));
            }

            // 其他摘要
            if (absEles.size() > 1) {
                Element otherAbstractElement = absEles.get(1).clone();
                otherAbstractElement.select("title").remove();

                String otherAbsHtml = otherAbstractElement.html();
                if (StrUtil.isNotBlank(otherAbsHtml)) {
                    // 使用正则表达式将多个连续空白字符（空格、制表符、换行符等）缩减为一个空格
                    otherAbsHtml = otherAbsHtml.replaceAll("\\s+", " ");
                    article.setOtherAbstract(StrUtil.trimToNull(otherAbsHtml));
                }
            }
        }

        // 解析关键词
        Elements kwdEles = doc.select("article > front > article-meta > kwd-group > kwd");
        if (CollUtil.isNotEmpty(kwdEles)) {
            List<String> keywords = new ArrayList<>();
            for (Element ele : kwdEles) {
                keywords.add(ele.text());
            }
            article.setKeywords(keywords);
        }

        return article;
    }

    /**
     * 解析发布类型
     */
    private Set<PubType> parsePubTypes(Document doc) {
        Set<PubType> pubTypes = new HashSet<>();
        Elements pubTypeEles = doc.select("article > front > article-meta > article-categories > subj-group > subject");

        for (Element ele : pubTypeEles) {
            PubType pubType = new PubType();
            pubType.setPubType(StrUtil.trimToNull(ele.text()));
            pubTypes.add(pubType);
        }

        return pubTypes;
    }

    /**
     * 解析其他ID
     */
    private Set<ArticleOtherId> parseArticleOtherId(Document doc) {
        Set<ArticleOtherId> results = new LinkedHashSet<>();

        // 解析文章ID
        Elements articleIdEles = doc.select("article > front > article-meta > article-id");
        if (CollUtil.isNotEmpty(articleIdEles)) {
            for (Element ele : articleIdEles) {
                if ("pmid".equals(ele.attr("pub-id-type"))) {
                    continue;
                }
                String idType = ele.attr("pub-id-type");
                String idValue = ele.text();

                if (StrUtil.isNotBlank(idType) && StrUtil.isNotBlank(idValue)) {
                    ArticleOtherId otherId = new ArticleOtherId();
                    otherId.setSource(StrUtil.trimToNull(idType));
                    otherId.setOtherId(StrUtil.trimToNull(idValue));
                    if (StrUtil.trimToNull(idValue).length() < 1000) {
                        results.add(otherId);
                    }
                }
            }
        }

        return results;
    }

    private Set<ArticleAuthor> parseArticleAuthor(Document doc, SourceTypeEnums typeEnums) {
        LinkedHashSet<ArticleAuthor> result = new LinkedHashSet<>();

        Map<String, String> outerAffElesMap = new LinkedHashMap<>();

        Elements outerAffEles = doc.select("article > front > article-meta > aff");
        for (Element affEle : outerAffEles) {
            String affId = affEle.attr("id");
            affEle.select("label").remove();
            String organization = affEle.text();
            outerAffElesMap.put(affId, organization);
        }

        Elements contribGroupEles = doc.select("article > front > article-meta > contrib-group");
        if (CollUtil.isNotEmpty(contribGroupEles)) {
            for (Element contribGroupEle : contribGroupEles) {
                Map<String, String> affIdToOrganizationMap = new LinkedHashMap<>();

                if (CollUtil.isNotEmpty(outerAffElesMap)) {
                    affIdToOrganizationMap.putAll(outerAffElesMap);
                }

                Elements contribEles = contribGroupEle.select("contrib");
                Elements affEles = contribGroupEle.select("aff");

                for (Element affEle : affEles) {
                    String affId = affEle.attr("id");

                    String organization = affEle.select("institution").text();
                    if (StrUtil.isBlank(organization)) {
                        organization = affEle.select("addr-line").text();
                    }
                    affIdToOrganizationMap.put(affId, organization);
                }

                int idx = 0;
                for (Element contribEle : contribEles) {
                    // 如果是editor就跳过
                    if (StrUtil.equalsIgnoreCase(contribEle.attr("contrib-type"), "editor")) {
                        continue;
                    }

                    ArticleAuthor articleAuthor = new ArticleAuthor();
                    Author author = new Author();
                    String authorCorrespond = StrUtil.trimToNull(contribEle.attr("corresp"));
                    String lastName = contribEle.select("name > surname").text();
                    String foreName = contribEle.select("name > given-names").text();

                    Set<Organization> organizations = new LinkedHashSet<>();
                    for (Element xrefEle : contribEle.select("xref")) {
                        String affId = xrefEle.attr("rid");
                        String organizationName = affIdToOrganizationMap.get(affId);
                        if (StrUtil.isBlank(organizationName)) {
                            continue;
                        }
                        Organization organization = new Organization();
                        organization.setName(organizationName);
                        organizations.add(organization);
                    }

                    if (CollUtil.isNotEmpty(organizations)) {
                        articleAuthor.setOrganizations(organizations);
                    }

                    author.setLastname(lastName);
                    author.setForename(StringUtils.removeDot(foreName));
                    author.setType("person");

                    articleAuthor.setAuthorOrder(idx++);
                    articleAuthor.setAuthorCorrespond(authorCorrespond);
                    articleAuthor.setAuthor(author);
                    result.add(articleAuthor);
                }
            }
        }

        return result;
    }

    /**
     * 解析预印本xml引用文献
     */
    private Set<Reference> parseRxivReferences(Document doc) {
        Set<Reference> references = new HashSet<>();
        Elements refEles = doc.select("article > back > ref-list > ref");

        for (Element ele : refEles) {
            Reference reference = new Reference();

            // 解析引用的文本内容
            Elements mixedCitationEles = ele.select("citation");
            if (CollUtil.isNotEmpty(mixedCitationEles)) {
                reference.setCitation(mixedCitationEles.text());
            }

            // 解析PMID
            Elements pmidEles = ele.select("pub-id[pub-id-type=pmid]");
            if (CollUtil.isNotEmpty(pmidEles)) {
                reference.setPmid(StrUtil.trimToNull(pmidEles.text()));
            }

            // 解析DOI
            Elements doiEles = ele.select("pub-id[pub-id-type=doi]");
            if (CollUtil.isNotEmpty(doiEles)) {
                reference.setDoi(StrUtil.trimToNull(doiEles.text()));
            }

            // 解析PMC ID
            Elements pmcidEles = ele.select("pub-id[pub-id-type=pmc]");
            if (CollUtil.isNotEmpty(pmcidEles)) {
                reference.setPmcid(StrUtil.trimToNull(pmcidEles.text()));
            }

            references.add(reference);
        }

        return references;
    }

    /**
     * 解析PMC引用文献
     */
    private Set<Reference> parsePMCReferences(Document doc) {
        Set<Reference> references = new HashSet<>();
        Elements refEles = doc.select("article > back > ref-list > ref");

        for (Element ele : refEles) {
            Reference reference = new Reference();

            // 解析引用的文本内容
            Elements mixedCitationEles = ele.select("mixed-citation");
            if (CollUtil.isNotEmpty(mixedCitationEles)) {
                Element mixedCitation = mixedCitationEles.get(0);

                // 格式化引用文本
                String formattedCitation = formatCitation(mixedCitation);
                reference.setCitation(formattedCitation);
            }

            // 解析PMID
            Elements pmidEles = ele.select("pub-id[pub-id-type=pmid]");
            if (CollUtil.isNotEmpty(pmidEles)) {
                reference.setPmid(StrUtil.trimToNull(pmidEles.text()));
            }

            // 解析DOI
            Elements doiEles = ele.select("pub-id[pub-id-type=doi]");
            if (CollUtil.isNotEmpty(doiEles)) {
                reference.setDoi(StrUtil.trimToNull(doiEles.text()));
            }

            // 解析PMC ID
            Elements pmcidEles = ele.select("pub-id[pub-id-type=pmc]");
            if (CollUtil.isNotEmpty(pmcidEles)) {
                reference.setPmcid(StrUtil.trimToNull(pmcidEles.text()));
            }

            references.add(reference);
        }

        return references;
    }

    /**
     * 格式化引用文本，确保年份、期刊名称等元素之间有适当的间隔
     *
     * @param mixedCitation 混合引用元素
     * @return 格式化后的引用文本
     */
    private String formatCitation(Element mixedCitation) {
        StringBuilder citation = new StringBuilder();

        // 处理作者部分
        Elements personGroup = mixedCitation.select("person-group");
        if (CollUtil.isNotEmpty(personGroup)) {
            Elements names = personGroup.select("name");
            if (CollUtil.isEmpty(names)) {
                names = personGroup.select("string-name");
            }
            for (int i = 0; i < names.size(); i++) {
                Element name = names.get(i);
                String surname = name.select("surname").text();
                String givenNames = name.select("given-names").text();

                citation.append(surname).append(" ").append(givenNames);

                // 如果不是最后一个作者，添加逗号和空格分隔
                if (i < names.size() - 1) {
                    citation.append(", ");
                }
            }
        }

        // 添加句点结束作者列表
        citation.append(".");

        // 处理年份
        Elements yearEles = mixedCitation.select("year");
        if (CollUtil.isNotEmpty(yearEles)) {
            // 确保年份前有空格，年份后有句点和空格
            citation.append(" ").append(yearEles.text()).append(". ");
        }

        // 处理文章标题
        Elements titleEles = mixedCitation.select("article-title");
        if (CollUtil.isNotEmpty(titleEles)) {
            citation.append(titleEles.text());

            // 检查标题是否已经有句点结尾，如果没有则添加
            String titleText = titleEles.text().trim();
            if (!titleText.endsWith(".")) {
                citation.append(".");
            }
        }

        // 处理期刊名称
        Elements sourceEles = mixedCitation.select("source");
        if (CollUtil.isNotEmpty(sourceEles)) {
            // 确保期刊名称前有空格
            citation.append(" ").append(sourceEles.text());
        }

        // 处理卷号
        Elements volumeEles = mixedCitation.select("volume");
        if (CollUtil.isNotEmpty(volumeEles)) {
            citation.append(" ").append(volumeEles.text());
        }

        // 处理页码
        Elements fpageEles = mixedCitation.select("fpage");
        Elements lpageEles = mixedCitation.select("lpage");
        if (CollUtil.isNotEmpty(fpageEles)) {
            citation.append(":").append(fpageEles.text());
            if (CollUtil.isNotEmpty(lpageEles)) {
                citation.append("–").append(lpageEles.text());
            }
        }

        // 处理DOI
        Elements doiEles = mixedCitation.select("pub-id[pub-id-type=doi]");
        if (CollUtil.isNotEmpty(doiEles)) {
            citation.append(". doi:").append(doiEles.text());
        }

        // 结束引用内容的句点
        if (!citation.toString().endsWith(".")) {
            citation.append(".");
        }

        // 不添加PMID到引用文本中，因为标准格式中没有包含

        return citation.toString();
    }
}
