<template>
  <div class="edit-profile">
    <div class="edit-profile-container container">

      <div class="profile-tabs-container">
        <el-tabs v-model="activeTab" class="profile-tabs">
          <!-- 个人资料 Tab -->
          <el-tab-pane label="个人资料" name="profile">
            <div class="tab-content">
              <el-form
                ref="profileFormRef"
                :model="profileForm"
                :rules="profileRules"
                label-width="90px"
                label-position="left"
                class="register-form"
              >
                <!-- 头像上传 -->
                <el-form-item label="头像" prop="avatar" class="form-group avatar-group">
                  <el-upload action="#" list-type="picture-card" :auto-upload="false">
                    <el-icon><Plus /></el-icon>
                  </el-upload>
                </el-form-item>

                <div class="form-row">
                    <el-form-item label="姓" prop="lastName" required class="form-group">
                      <el-input
                        v-model="profileForm.lastName"
                        placeholder="请输入姓"
                        clearable
                      />
                    </el-form-item>
                    <el-form-item label="名" prop="firstName" required class="form-group">
                      <el-input
                        v-model="profileForm.firstName"
                        placeholder="请输入名"
                        clearable
                      />
                    </el-form-item>
                </div>
                <div class="form-row">
                    <el-form-item label="邮箱" prop="email" required class="form-group">
                      <el-input
                          v-model="profileForm.email"
                          placeholder="请输入邮箱地址"
                          clearable
                          class="full-width"
                      >
                      </el-input>
                    </el-form-item>
                    <el-form-item label="机构" prop="organization" required class="form-group">
                      <el-select
                          v-model="profileForm.organization"
                          placeholder="请选择或输入机构名称"
                          filterable
                          allow-create
                          class="full-width"
                      >
                        <el-option
                            v-for="org in organizationOptions"
                            :key="org.value"
                            :label="org.label"
                            :value="org.value"
                        />
                      </el-select>
                    </el-form-item>
                </div>
                <div class="form-row">
                    <el-form-item label="部门" prop="department" class="form-group">
                      <el-input
                          v-model="profileForm.department"
                          placeholder="请输入部门名称"
                          clearable
                          class="full-width"
                      />
                    </el-form-item>
                    <el-form-item label="PI姓名" prop="piName" class="form-group">
                      <el-input
                          v-model="profileForm.piName"
                          placeholder="请输入PI姓名"
                          clearable
                          class="full-width"
                      />
                    </el-form-item>
                </div>

                <div class="form-row">
                 
                    <el-form-item label="职位" prop="position" class="form-group">
                      <el-input  v-model="profileForm.position"></el-input>
                    </el-form-item>
                    <el-form-item label="电话" prop="phone" class="form-group">
                      <el-input
                        v-model="profileForm.phone"
                        placeholder="请输入电话号码"
                        clearable
                      />
                    </el-form-item>
                </div>
                <div class="form-row">
                    <el-form-item label="国家/地区" prop="country" required class="form-group">
                      <el-select
                        v-model="profileForm.country"
                        placeholder="--请选择--"
                        class="full-width"
                      >
                        <el-option
                          v-for="country in countryOptions"
                          :key="country.value"
                          :label="country.label"
                          :value="country.value"
                        />
                      </el-select>
                    </el-form-item>
                    <el-form-item label="省/州" prop="province" class="form-group">
                      <el-input
                        v-model="profileForm.province"
                        placeholder="请输入省份或州"
                        clearable
                      />
                    </el-form-item>
                </div>
                <el-form-item label="城市" prop="city" class="form-group w-50">
                  <el-input
                      v-model="profileForm.city"
                      placeholder="请输入城市"
                      clearable
                  />
                </el-form-item>

                <div class="form-buttons">
                  <el-button type="primary" >保存</el-button>

                  <el-button>重置</el-button>
                </div>
              </el-form>
            </div>
          </el-tab-pane>

          <!-- 修改密码 Tab -->
          <el-tab-pane label="修改密码" name="password">
            <div class="tab-content">
              <el-form
                ref="passwordFormRef"
                :model="passwordForm"
                :rules="passwordRules"
                label-width="100px"
                class="register-form"
                label-position="left"
              >
                <el-form-item label="原密码" prop="oldPassword" required class="form-group">
                  <el-input
                    v-model="passwordForm.oldPassword"
                    type="password"
                    placeholder="请输入原密码"
                    show-password
                    clearable
                  />
                </el-form-item>

                <el-form-item label="新密码" prop="newPassword" required  class="form-group">
                  <el-input
                    v-model="passwordForm.newPassword"
                    type="password"
                    placeholder="请输入新密码"
                    show-password
                    clearable
                  />
                </el-form-item>

                <el-form-item label="确认密码" prop="confirmPassword" required  class="form-group">
                  <el-input
                    v-model="passwordForm.confirmPassword"
                    type="password"
                    placeholder="请再次输入新密码"
                    show-password
                    clearable
                  />
                </el-form-item>

                <div class="form-buttons">
                  <el-button type="primary" >修改密码</el-button>

                  <el-button >重置</el-button>
                </div>
              </el-form>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { Plus} from '@element-plus/icons-vue'

const activeTab = ref('profile')

// 表单引用
const profileFormRef = ref()
const passwordFormRef = ref()

// 个人资料表单数据（预填充当前用户信息）
const profileForm = reactive({
  lastName: '张',
  email:'<EMAIL>',
  firstName: '三',
  organization: '中科院生物物理研究所',
  department: '分子生物学实验室',
  piName: '李教授',
  position: '',
  phone: '13800138000',
  country: '中国',
  province: '北京市',
  city: '北京'
})

// 修改密码表单数据
const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})


// 机构选项
const organizationOptions = ref([
  { label: '中科院生物物理研究所', value: '中科院生物物理研究所' },
  { label: '清华大学', value: '清华大学' },
  { label: '北京大学', value: '北京大学' },
  { label: '中科院上海生科院', value: '中科院上海生科院' },
  { label: '复旦大学', value: '复旦大学' }
])

// 职位选项
const positionOptions = ref([
  { label: '教授', value: '教授' },
  { label: '副教授', value: '副教授' },
  { label: '讲师', value: '讲师' },
  { label: '研究员', value: '研究员' },
  { label: '副研究员', value: '副研究员' },
  { label: '助理研究员', value: '助理研究员' },
  { label: '博士后', value: '博士后' },
  { label: '博士生', value: '博士生' },
  { label: '硕士生', value: '硕士生' },
  { label: '其他', value: '其他' }
])

// 国家选项
const countryOptions = ref([
  { label: '中国', value: '中国' },
  { label: '美国', value: '美国' },
  { label: '英国', value: '英国' },
  { label: '德国', value: '德国' },
  { label: '法国', value: '法国' },
  { label: '日本', value: '日本' },
  { label: '韩国', value: '韩国' },
  { label: '澳大利亚', value: '澳大利亚' },
  { label: '加拿大', value: '加拿大' }
])

// 表单验证规则
const profileRules = {
  lastName: [
    { required: true, message: '请输入姓', trigger: 'blur' }
  ],
  firstName: [
    { required: true, message: '请输入名', trigger: 'blur' }
  ],
  organization: [
    { required: true, message: '请选择机构', trigger: 'change' }
  ],
  country: [
    { required: true, message: '请选择国家/地区', trigger: 'change' }
  ],
  phone: [
    {
      validator: (rule, value, callback) => {
        if (value && !/^1[3-9]\d{9}$/.test(value)) {
          callback(new Error('请输入正确的手机号码'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

const passwordRules = {
  oldPassword: [
    { required: true, message: '请输入原密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度应为6-20位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}


</script>

<style lang="scss" scoped>
@import "@/assets/styles/variables";

.edit-profile {
  background-color: #F5F5F5;
  padding: $spacing-xl 0;
  min-height: calc(100vh - 520px);
}

.page-header {
  display: flex;
  align-items: center;
  gap: $spacing-md;
  margin-bottom: $spacing-xl;

  .back-btn {
    font-size: $font-size-medium;
    color: #0066CD;

    &:hover {
      color: #0052A3;
    }
  }

  .page-title {
    font-size: $font-size-xlarge;
    font-weight: $font-weight-bold;
    color: $primary-color;
    margin: 0;
  }
}

.profile-tabs-container {
  background: $white;
  border-radius: $border-radius-lg;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.profile-tabs {
  :deep(.el-tabs__header) {
    margin: 0;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
  }

  :deep(.el-tabs__nav-wrap) {
    padding: 0 $spacing-lg;
  }

  :deep(.el-tabs__item) {
    font-size: $font-size-medium;
    font-weight: $font-weight-medium;
    color: $gray;
    padding: 0 $spacing-lg;
    height: 50px;
    line-height: 50px;

    &.is-active {
      color: #0066CD;
    }

    &:hover {
      color: #0066CD;
    }
  }

  :deep(.el-tabs__active-bar) {
    background-color: #0066CD;
  }
}

.tab-content {
  padding: $spacing-xl;
}
.register-form {
  :deep(.el-form-item) {
    margin-bottom: $spacing-lg;

    .el-form-item__label {
      font-size: 16px;
      color: #374151;
      font-weight: 500;
      padding: 0;
      justify-content: flex-end;
      margin-right: 10px;
    }

    .el-form-item__content {
      line-height: 1.4;
    }
  }

  .form-group {
      :deep(.el-input__wrapper),
      :deep(.el-select__wrapper) {
        border-radius: $border-radius-md;
        box-shadow: none;
        border: 1px solid #e1e5e9;

        &:hover {
          border-color: $primary-color;
        }

        &.is-focus {
          border-color: $primary-color;
          box-shadow: 0 0 0 2px rgba($primary-color, 0.1);
        }
      }
  }


  .form-row {
    display: flex;
    gap: $spacing-sm;
    .el-form-item{
      flex: 1;
    }

    @media (max-width: $breakpoint-md) {
      flex-direction: column;
      gap: 0;

      .form-group {
        width: 100% !important;
      }
    }
  }
  .register-button {
    width: 100%;
    height: 48px;
    background-color: $primary-color;
    border-color: $primary-color;
    border-radius: $border-radius-md;
    font-size: $font-size-medium;
    font-weight: $font-weight-medium;

    &:hover {
      background-color: darken($primary-color, 10%);
      border-color: darken($primary-color, 10%);
    }
  }
}
 .form-buttons {
   text-align: center;
   margin-top: 15px;
 justify-content: center;
}
// 响应式设计
@media (max-width: $breakpoint-md) {
  .edit-profile-container {
    padding: 0 $spacing-sm;
  }

  .tab-content {
    padding: $spacing-lg;
  }

  .profile-form,
  .password-form {
    .form-row {
      flex-direction: column;
      gap: 0;
    }
  }

  .form-buttons {
    flex-direction: column;
    .el-button {
      width: 100%;
      margin: 0;
      &:last-child{
        margin-top: $spacing-md;
      }
    }
  }
}
</style>
