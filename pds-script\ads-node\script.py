# coding=utf8
__author__ = 'yhju'

import importlib.util
import logging
import os
import threading
from collections import OrderedDict

import api
import util

logger = logging.getLogger()

JOURNAL_SCRIPT_KEY_START = "jur_"
SITE_SCRIPT_KEY_START = "sit_"


class ScriptContext(object):
    """
    脚本控制器，用于从服务器加载，选择、同步脚本
    """

    def __init__(self, site_id: int, api_invoker: api.ApiInvoker):
        self.__site_id = site_id
        self.__api_invoker = api_invoker

        # 初始化脚本所在目录
        script_dir = os.path.join(util.program_root_dir(), "script")
        if not os.path.exists(script_dir):
            os.makedirs(script_dir)
        self.__script_dir = script_dir

        # 存储脚本信息格式（分为两种脚本）：
        # 1，期刊脚本 <jur_journalId.pyscript, (md5, scriptInstance)>
        # 2，站点脚本 <sit_md5.pyscript, (md5, scriptInstance)>
        self.__init_script_module_context()
        logger.info("初始化脚本控制容器完成")

    def __init_script_module_context(self):
        """
        初始化 module context，并将script目录中的python文件加载进context中
        :return:
        """
        if hasattr(self, "__script_module_context"):
            return

        self.__script_module_context = OrderedDict()

        # 启动先删除本地脚本
        self.__remove_all_module(script_name_start_with=None)

        # 启动的时候不删除 而 采用加载本地文件？
        # if len(self.__script_module_context) > 0:
        #     raise ValueError("脚本容器中已有内容，无法初始化， 请检查代码")
        #
        # for file_name in os.listdir(self.__script_dir):
        #     script_file_path = os.path.join(self.__script_dir, file_name)
        #     module = self.__init_script_module(script_file_path)
        #     if module is None:
        #         continue
        #     self.__script_module_context[file_name] = module

    def __init_script_module(self, script_file_path: str) -> tuple:
        """
        将文件加载为模块（import）
        :param script_file_path:
        :return:
        """
        if util.empty_str(script_file_path):
            return None
        if not os.path.exists(script_file_path) or not os.path.isfile(script_file_path):
            return None
        if not script_file_path.lower().endswith(".py"):
            logger.warning("不以 .py 结尾的文件不作为脚本加载，{}".format(script_file_path))
            return None

        # 加载脚本文件
        script_file_name, module_spec, module = os.path.basename(script_file_path), None, None
        try:
            module_spec = importlib.util.spec_from_file_location(name=script_file_name.lower().replace(".pyscript", ""),
                                                                 location=script_file_path)

            module = importlib.util.module_from_spec(module_spec)
            module_spec.loader.exec_module(module)

            # 脚本中需要一个叫做 Script 的类，且需要继承 threading.Thread
            if not hasattr(module, "Script") or not issubclass(module.Script, threading.Thread):
                raise ImportError("脚本需要一个名为 Script 的类，且继承 threading.Thread, {}".format(script_file_path))

        except BaseException as e:
            # 出错先卸载模块
            if module is not None:
                del module
            if module_spec is not None:
                del module_spec
            # 抛出错误原因
            raise ImportError(
                "导入脚本模块失败 SITE: {} FILE: {},  原因：{}".format(self.__site_id, script_file_path, e))

        logger.info("脚本加载成功 {}".format(script_file_name))

        return util.md5_file(script_file_path), module,

    def __remove_script_module(self, script_file_name: str):
        """
        移除已加载的脚本模块，然后删除脚本文件
        :param script_file_name:
        :return:
        """
        if script_file_name is None or script_file_name.strip() == "":
            return

        # 先移除已加载的模块
        if script_file_name in self.__script_module_context.keys():
            # 先从缓存移除
            md5, script_instance = self.__script_module_context.pop(script_file_name)
            # 然后需要卸载 脚本的导入信息
            del md5, script_instance

        module_spec = self.__check_script_module(script_file_name.lower().replace(".pyscript", ""))
        if module_spec is not None:
            del module_spec

        # 移除文件
        script_file_path = os.path.join(self.__script_dir, script_file_name)
        try:
            if os.path.exists(script_file_path):
                os.remove(script_file_path)
        except PermissionError:
            logger.info("移除模块时，无权删除文件 {}".format(script_file_path))
        logger.info("脚本模块已被移除 {}".format(script_file_name))

    def __remove_all_module(self, script_name_start_with: str):
        for module_name in self.__script_module_context.keys():
            if not util.empty_str(script_name_start_with) and not str(module_name).startswith(script_name_start_with):
                continue
            self.__remove_script_module(module_name)
        for file_name in os.listdir(self.__script_dir):
            if not util.empty_str(script_name_start_with) and not str(file_name).startswith(script_name_start_with):
                continue
            path = os.path.join(self.__script_dir, file_name)
            try:
                if os.path.isfile(path):
                    os.remove(path)
            except PermissionError:
                logger.info("脚本 {} 没有写权限，无法删除".format(path))

    @staticmethod
    def __check_script_module(module_name: str):
        """
        检查模块是否可以被加载
        :param module_name:
        :return:
        """
        if util.empty_str(module_name):
            return None
        module_spec = importlib.util.find_spec(module_name)
        if module_spec is None:
            logger.info("Module :{} not found".format(module_name))
            return None
        else:
            logger.info("Module:{} can be imported!".format(module_name))
        return module_spec

    def iter_script_modules(self, task: dict):
        """
        获取任务脚本，返回可以执行这个任务的脚本模块的迭代器
        :param task:
        :return:
        """
        if task is None:
            return None

        # 仅期刊类型节点会返回脚本，直接使用任务中指定的脚本
        script = self.__get_task_script_module(task)
        if script is not None:
            yield script
            return

        # 非期刊类型任务中没有返回脚本，使用站点脚本
        yield from self.__get_site_script_module()

    def __get_site_script_module(self):
        """
        获取站点脚本
        :return:
        """
        site = self.__api_invoker.site_info()
        if site is None or "script" not in site.keys() or site["script"] is None or not isinstance(site["script"],
                                                                                                   list):
            logger.info("站点 {} 指定的 script 为空，卸载站点脚本...".format(self.__site_id))
            self.__remove_all_module(SITE_SCRIPT_KEY_START)
            return None

        for script in site["script"]:
            if not util.dict_has_value(script, "scriptServicePath") or not util.dict_has_value(script, "scriptMD5"):
                continue
            site_script_name = "{}{}.pyscript".format(SITE_SCRIPT_KEY_START, script["scriptMD5"]).lower()
            module_info = self.__refresh_script_module_from_remote(script["scriptServicePath"], script["scriptMD5"],
                                                                   site_script_name, script["scriptIdStr"])
            if module_info is None:
                logger.warning("站点 {} 未加载到脚本 {}".format(self.__site_id, site_script_name))
                continue
            yield module_info

    def __get_task_script_module(self, task):
        """
        获取任务执行脚本
        :param task:
        :return:
        """
        if task is None:
            return None
        if not util.dict_has_value(task, "scriptServicePath") or not util.dict_has_value(task, "scriptMD5"):
            return None

        # 期刊脚本在本地存储的名字
        journal_file_name = "{}{}.pyscript".format(JOURNAL_SCRIPT_KEY_START, task["journalId"]).lower()

        # 当任务没有指定脚本，则把对应期刊的脚本从缓存/本地删除
        if not util.dict_has_value(task, "scriptServicePath"):
            logger.info("服务器没有给期刊 {} 配置脚本，将把本地对应期刊脚本 {} 移除".format(task["journalId"],
                                                                                           journal_file_name))
            # self.__remove_script(journal_file_name)
            return None

        return self.__refresh_script_module_from_remote(task["scriptServicePath"], task["scriptMD5"], journal_file_name,
                                                        task["scriptIdStr"])

    def __refresh_script_module_from_remote(self, script_path: str, script_md5: str, module_name: str,
                                            script_id_str: str):
        """
        与服务器的脚本进行比较，判断是否需要重新加成模块，然后返回模块句柄
        :param script_path:
        :param script_md5:
        :return:
        """
        if util.empty_str(script_path) or util.empty_str(script_md5) or util.empty_str(module_name):
            return None
        if not script_path.lower().endswith(".pyscript"):
            logger.info("文件 {} 不以 .pyscript 结尾，跳过")
            return None

        # 从缓存获取 模块
        module_info = self.__script_module_context.get(module_name)

        # 脚本没变过
        if module_info is not None and module_info[0] == script_md5:
            return module_name, module_info[0], module_info[1]

        # 脚本变了，重新刷新脚本
        # 先卸载脚本
        self.__remove_script_module(module_name)
        # 下载脚本
        script_file = self.__download_script_file(script_service_path=script_path, script_md5=script_md5,
                                                  script_id_str=script_id_str)
        # 脚本重名名为 .py
        script_file_path = os.path.join(self.__script_dir, module_name).replace(".pyscript", ".py")
        os.rename(script_file, script_file_path)
        # 重新加载脚本
        md5, module = self.__init_script_module(script_file_path)
        self.__script_module_context[module_name] = (md5, module)
        logger.info("脚本已从服务器重新更新 {}".format(module_name))
        return module_name, md5, module

    def __download_script_file(self, script_service_path: str, script_md5: str, script_id_str: str):
        """
        下载脚本文件
        :param script_service_path:
        :param script_md5:
        :return:
        """
        if util.empty_str(script_service_path) or util.empty_str(script_md5):
            return None

        if not script_service_path.lower().endswith(".pyscript"):
            raise ImportError("脚本文件需要以 .pyscript 结尾 {}".format(script_service_path))

        script_file = os.path.join(self.__script_dir, os.path.basename(script_service_path))
        self.__api_invoker.get_task_script_file(script_id_str, script_file)

        # 没下载下来
        if not os.path.exists(script_file):
            raise ImportError("脚本文件没有从服务器下载下来 {}".format(script_service_path))

        if os.path.getsize(script_file) == 0:
            os.remove(script_file)
            raise ImportError("脚本文件下载的是空文件 {}".format(script_service_path))

        # 判断文件MD5
        file_md5 = util.md5_file(script_file)
        if script_md5.strip().lower() != file_md5.lower():
            os.remove(script_file)
            raise ImportError(
                "更新脚本模块出错，下载到的脚本文件MD5 与从服务器获取到的不太一致，{} 本地 {} 服务器 {}".format(
                    script_service_path, file_md5, script_md5))

        return script_file
