package org.biosino.lf.pds.article.dto;

import lombok.Data;
import org.biosino.lf.pds.article.domain.Publisher;

/**
 * 出版社验证结果DTO
 *
 * <AUTHOR>
 */
@Data
public class PublisherValidationResult {
    
    /**
     * 是否验证通过
     */
    private boolean valid;
    
    /**
     * 冲突的字段名称
     */
    private String conflictField;
    
    /**
     * 冲突的值
     */
    private String conflictValue;
    
    /**
     * 冲突的出版社记录
     */
    private Publisher conflictPublisher;
    
    /**
     * 错误消息
     */
    private String errorMessage;
    
    /**
     * 创建验证通过的结果
     */
    public static PublisherValidationResult valid() {
        PublisherValidationResult result = new PublisherValidationResult();
        result.setValid(true);
        return result;
    }
    
    /**
     * 创建验证失败的结果
     */
    public static PublisherValidationResult invalid(String conflictField, String conflictValue, 
                                                    Publisher conflictPublisher, String errorMessage) {
        PublisherValidationResult result = new PublisherValidationResult();
        result.setValid(false);
        result.setConflictField(conflictField);
        result.setConflictValue(conflictValue);
        result.setConflictPublisher(conflictPublisher);
        result.setErrorMessage(errorMessage);
        return result;
    }
}
