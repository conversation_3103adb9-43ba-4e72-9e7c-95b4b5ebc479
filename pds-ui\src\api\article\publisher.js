import request from '@/utils/request';

// 查询出版社列表
export function listPublisher(query) {
  return request({
    url: '/publisher/list',
    method: 'get',
    params: query,
  });
}

// 查询出版社详细
export function getPublisher(id) {
  return request({
    url: '/publisher/' + id,
    method: 'get',
  });
}

// 修改出版社
export function updatePublisher(data) {
  return request({
    url: '/publisher',
    method: 'put',
    data: data,
  });
}

// 合并出版社
export function mergePublisher(data) {
  return request({
    url: '/publisher/merge',
    method: 'post',
    data: data,
  });
}

// 修改出版社状态
export function changePublisherStatus(id, status) {
  return request({
    url: '/publisher/changeStatus',
    method: 'post',
    params: {
      id: id,
      status: status,
    },
  });
}
