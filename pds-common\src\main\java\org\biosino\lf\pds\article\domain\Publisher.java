package org.biosino.lf.pds.article.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.biosino.lf.pds.article.config.StringListArrayTypeHandler;

import java.util.Date;
import java.util.List;

/**
 * 出版社表
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "tb_dds_publisher", autoResultMap = true)
public class Publisher {
    /**
     * 文档ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 出版社名称
     */
    @TableField("name")
    private String name;

    /**
     * 地址
     */
    @TableField("ioc")
    private String ioc;

    /**
     * 别名
     */
    @TableField(value = "alias", typeHandler = StringListArrayTypeHandler.class)
    private List<String> alias;

    /**
     * 来源
     */
    @TableField("source_type")
    private String sourceType;

    /**
     * 状态 正常0  停用1
     */
    @TableField("status")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 期刊数量（非数据库字段）
     */
    @TableField(exist = false)
    private Long journalCount;

    /**
     * 非数据库字段，判断是新增/编辑出版社
     */
    @TableField(exist = false)
    private boolean insert;

}
