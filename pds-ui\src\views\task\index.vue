<template>
  <div class="app-container box-card">
    <el-form
      ref="taskFormRef"
      :model="taskForm"
      :rules="rules"
      label-width="140px"
    >
      <!-- 任务描述 -->
      <el-form-item label="任务描述" prop="taskDesc">
        <el-input
          v-model="taskForm.taskDesc"
          placeholder="请输入任务描述"
          class="standard-width"
        />
      </el-form-item>

      <!-- 优先级 -->
      <el-form-item label="优先级" prop="priority">
        <div class="priority-container">
          <el-radio-group
            v-model="taskForm.priorityType"
            @change="handlePriorityChange"
          >
            <el-radio :value="2">普通</el-radio>
            <el-radio v-hasPermi="['task:priority:adv']" :value="3">
              高
            </el-radio>
            <el-radio
              v-hasPermi="['task:priority:cust']"
              :value="1"
              class="custom-priority-radio"
            >
              自定义
              <el-input-number
                v-if="taskForm.priorityType === 1"
                v-model="taskForm.customPriority"
                :min="0"
                :max="99"
                placeholder="0~99"
                class="custom-priority-input"
                size="small"
              />
            </el-radio>
          </el-radio-group>
        </div>
        <div class="form-item-tip">
          高优先级任务将会优先被执行，普通为默认级别，自定义可以设置0-99的优先级值
        </div>
      </el-form-item>

      <!-- 支持的节点类型 -->
      <el-form-item label="支持的节点类型" prop="nodeTypes">
        <el-checkbox-group
          v-model="taskForm.nodeTypes"
          @change="handleNodeTypesChange"
        >
          <el-checkbox value="1"> 批次 </el-checkbox>
          <el-checkbox value="2"> 源刊 </el-checkbox>
          <el-checkbox v-hasPermi="['task:site_type:school']" value="3">
            高校
          </el-checkbox>
        </el-checkbox-group>
        <div class="form-item-tip">
          选择任务可以分发到哪些类型的节点执行，默认全选(执行顺序：批次->源刊->高校)
        </div>
      </el-form-item>

      <!-- 是否测试任务 -->
      <el-form-item v-hasPermi="['task:test']" label="测试任务" prop="testFlag">
        <el-radio-group
          v-model="taskForm.testFlag"
          @change="handleTestFlagChange"
        >
          <el-radio :value="0">否</el-radio>
          <el-radio :value="1">是</el-radio>
        </el-radio-group>
        <div class="form-item-tip">测试任务下载成功的PDF不会入库到数据库中</div>
      </el-form-item>

      <!-- 站点选择（仅测试任务显示） -->
      <el-form-item
        v-if="taskForm.testFlag === 1"
        v-hasPermi="['task:test']"
        label="指定站点"
        prop="siteId"
      >
        <el-select
          v-model="taskForm.siteId"
          placeholder="请选择站点（可选）"
          clearable
          class="standard-width"
          @change="handleSiteIdChange"
        >
          <el-option
            v-for="site in siteOptions"
            :key="`st-id-${site.id}`"
            :label="site.text"
            :value="site.id"
          />
        </el-select>
        <div class="form-item-tip">
          可选择指定站点执行测试任务，不选择则按正常流程分配
        </div>
      </el-form-item>

      <!-- 下载模式 -->
      <el-form-item label="下载模式" prop="downloadMode">
        <el-radio-group v-model="taskForm.downloadMode">
          <el-radio value="speed">速度优先</el-radio>
          <el-radio
            v-hasPermi="['task:download_mode:complete']"
            value="complete"
          >
            完整度优先
          </el-radio>
        </el-radio-group>
        <div class="form-item-tip">
          速度优先模式会优先分配给节点少的分组缩短下载时间，完整度优先模式会优先分配给节点多的分组增加成功率
        </div>
      </el-form-item>

      <!-- 重试间隔 -->
      <el-form-item label="重试间隔" prop="retryInterval">
        <el-input-number
          v-model="taskForm.retryInterval"
          :min="0"
          :max="120"
          placeholder="请输入重试间隔时间"
          class="standard-width"
        />
        <div class="form-item-tip">
          单位为秒，下载失败后等待多长时间进行重试，推荐5秒
        </div>
      </el-form-item>

      <!-- 文献提交方式 -->
      <el-form-item label="文献提交方式">
        <div class="literature-input-container">
          <!-- Excel模板下载与上传 -->
          <div class="excel-actions">
            <div class="excel-actions-left">
              <el-button type="primary" @click="downloadExcelTemplate">
                <el-icon><Download /></el-icon>下载Excel模板
              </el-button>
              <el-upload
                ref="excelUploader"
                class="excel-upload"
                action="#"
                :auto-upload="false"
                :on-change="handleExcelUpload"
                :limit="1"
                :show-file-list="false"
                accept=".xlsx,.xls"
              >
                <el-button type="success">
                  <el-icon><Upload /></el-icon>上传Excel文件
                </el-button>
              </el-upload>
            </div>
            <div class="excel-actions-right">
              <el-button type="warning" @click="handleLiteratureValidation">
                <el-icon><DocumentChecked /></el-icon>文献校验
              </el-button>
            </div>
          </div>
          <div class="form-item-tip">
            填写需要获取全文的PMID/PMCID/DOI，3列中仅需填写任意一种ID即可
          </div>
          <!-- 在线表格 -->
          <div ref="hotTableContainer" class="hot-container"></div>
        </div>
      </el-form-item>

      <!-- 提交按钮 -->
      <el-form-item>
        <el-button type="primary" @click="submitTask">发布任务</el-button>
        <el-button @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 任务列表区域 可以在这里添加已发布的任务列表展示 -->

    <!-- 文献校验上传对话框 -->
    <el-dialog
      v-model="validationUpload.open"
      :title="validationUpload.title"
      width="500px"
      append-to-body
    >
      <div v-if="!validationUpload.validationCompleted">
        <div
          v-loading="validationUpload.isUploading"
          element-loading-text="文献校验中，请稍候..."
        >
          <el-upload
            ref="validationUploadRef"
            :limit="1"
            accept=".xlsx,.xls"
            :headers="validationUpload.headers"
            :action="validationUpload.url"
            :disabled="validationUpload.isUploading"
            :on-progress="handleValidationFileUploadProgress"
            :on-success="handleValidationFileSuccess"
            :on-error="handleValidationFileError"
            :auto-upload="false"
            drag
          >
            <el-icon class="el-icon--upload">
              <upload-filled />
            </el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip text-center">
                <div style="margin-bottom: 8px">
                  <span
                    >格式说明：请上传包含PMID、PMCID、DOI列的Excel文件，每行数据只能填写其中一列</span
                  >
                </div>
                <div style="color: #f56c6c; font-size: 12px">
                  <span>数据限制：最多支持5000条数据</span>
                </div>
              </div>
            </template>
          </el-upload>
        </div>
      </div>

      <div v-else class="validation-result">
        <el-alert
          title="校验完成"
          type="success"
          :description="`共校验 ${validationUpload.resultSummary.total} 条数据，其中 ${validationUpload.resultSummary.valid} 条有效，${validationUpload.resultSummary.invalid} 条无效`"
          show-icon
          :closable="false"
        />
        <div class="download-section">
          <p>请下载校验结果文件查看详细信息：</p>
          <el-button type="success" @click="downloadValidationResult">
            <el-icon><Download /></el-icon>下载Excel
          </el-button>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button
            v-if="!validationUpload.validationCompleted"
            type="primary"
            :loading="validationUpload.isUploading"
            :disabled="validationUpload.isUploading"
            @click="submitValidationFileForm"
          >
            {{ validationUpload.isUploading ? '校验中...' : '确 定' }}
          </el-button>
          <el-button
            :disabled="validationUpload.isUploading"
            @click="closeValidationDialog"
          >
            {{ validationUpload.validationCompleted ? '关 闭' : '取 消' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import {
    onMounted,
    onUnmounted,
    reactive,
    ref,
    getCurrentInstance,
    nextTick,
  } from 'vue';
  import { ElLoading, ElMessage } from 'element-plus';
  import {
    Download,
    Upload,
    DocumentChecked,
    UploadFilled,
  } from '@element-plus/icons-vue';
  import { registerAllModules } from 'handsontable/registry';
  import Handsontable from 'handsontable';
  import 'handsontable/dist/handsontable.full.min.css';
  import * as XLSX from 'xlsx';
  import { publishTask, getAllPdsSites } from '@/api/task/task.js';
  import { trimStr } from '@/utils/index.js';
  import { getToken } from '@/utils/auth.js';
  import request from '@/utils/request.js';

  const { proxy } = getCurrentInstance();
  // const { script_type } = proxy.useDict('script_type');

  // 注册所有Handsontable模块
  registerAllModules();

  const excelUploader = ref();
  const validationUploadRef = ref();

  // 文献校验上传参数
  const validationUpload = reactive({
    // 是否显示弹出层
    open: false,
    // 弹出层标题
    title: '',
    // 是否禁用上传
    isUploading: false,
    // 设置上传的请求头部
    headers: { Authorization: 'Bearer ' + getToken() },
    // 上传的地址
    url: import.meta.env.VITE_APP_BASE_API + '/task/validateLiterature',
    // 校验是否完成
    validationCompleted: false,
    // 校验结果摘要
    resultSummary: {
      total: 0,
      valid: 0,
      invalid: 0,
    },
    // 校验结果文件下载地址
    resultFileUrl: '',
  });

  // 任务表单数据
  const taskForm = reactive({
    taskDesc: '',
    priorityType: 2, // 默认普通优先级
    customPriority: 50, // 默认自定义优先级值
    nodeTypes: ['1', '2'], // 默认选中批次和源刊，高校需要高级用户或管理员
    testFlag: 0, // 默认非测试任务
    downloadMode: 'speed', // 默认速度优先
    retryInterval: 5, // 默认重试间隔
    siteId: null, // 指定站点ID（测试任务用）
  });

  const taskFormInit = proxy.$_.cloneDeep(toRaw(taskForm));

  // 站点选项数据
  const siteOptions = ref([]);

  // 文件内容和预览
  const fileContent = ref('');
  const filePreview = ref('');

  // 表单验证规则
  const rules = {
    taskDesc: [{ required: true, message: '请输入任务描述', trigger: 'blur' }],
    nodeTypes: [
      {
        type: 'array',
        required: true,
        message: '请至少选择一种节点类型',
        trigger: 'change',
      },
    ],
    retryInterval: [
      { required: true, message: '请输入重试间隔', trigger: 'blur' },
    ],
  };

  const taskFormRef = ref(null);
  const hotTableContainer = ref(null);
  const tableRowCount = ref(0);
  let hotInstance = null;

  // 表格初始数据
  const initialTableData = [
    ['', '', ''],
    ['', '', ''],
    ['', '', ''],
    ['', '', ''],
    ['', '', ''],
    ['', '', ''],
    ['', '', ''],
    ['', '', ''],
    ['', '', ''],
    ['', '', ''],
    ['', '', ''],
  ];

  const initialTableDataReset = proxy.$_.cloneDeep(initialTableData);

  // 初始化表格
  const initTable = () => {
    if (hotTableContainer.value) {
      hotInstance = new Handsontable(hotTableContainer.value, {
        data: initialTableData,
        rowHeaders: true,
        colHeaders: ['PMID', 'PMCID', 'DOI'],
        columns: [{ type: 'text' }, { type: 'text' }, { type: 'text' }],
        width: '100%',
        height: 300,
        licenseKey: 'non-commercial-and-evaluation',
        stretchH: 'all',
        contextMenu: true,
        afterChange: () => {
          updateRowCount();
        },
      });
      updateRowCount();
    }
  };

  // 更新行数计数
  const updateRowCount = () => {
    if (hotInstance) {
      const data = hotInstance.getData();
      const validRows = data.filter(row =>
        row.some(cell => cell && trimStr(cell) !== ''),
      );
      tableRowCount.value = validRows.length;
    }
  };

  // 添加新行
  const addRow = () => {
    if (hotInstance) {
      hotInstance.alter('insert_row', hotInstance.countRows());
      updateRowCount();
    }
  };

  // 下载Excel模板
  const downloadExcelTemplate = () => {
    const templateData = [
      ['PMID', 'PMCID', 'DOI'],
      ['12345678', '', ''],
      ['', 'PMC7654321', ''],
      ['', '', '10.1016/j.cell.2020.01.001'],
      ['', '', ''],
    ];

    const ws = XLSX.utils.aoa_to_sheet(templateData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, '任务发布-文献标识符模板');

    XLSX.writeFile(wb, '任务发布-文献标识符模板.xlsx');
  };

  // 处理Excel上传
  const handleExcelUpload = file => {
    console.log(123, file);
    // 显示加载中提示
    const loading = ElLoading.service({
      lock: true,
      text: 'Excel文件解析中...',
      background: 'rgba(0, 0, 0, 0.7)',
    });

    const reader = new FileReader();
    reader.onload = e => {
      try {
        const data = new Uint8Array(e.target.result);
        const workbook = XLSX.read(data, { type: 'array' });

        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];

        // 转换为二维数组
        const excelData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

        // 验证行数限制
        if (excelData.length > 5000) {
          loading.close();
          ElMessage.error('Excel文件行数不能超过5000行');
          return;
        }

        // 如果第一行是表头，从第二行开始加载数据
        const tableData =
          excelData.length > 1 &&
          (excelData[0][0] === 'PMID' ||
            excelData[0][0] === 'PMCID' ||
            excelData[0][0] === 'DOI')
            ? excelData.slice(1)
            : excelData;

        console.log(tableData);
        // 加载数据到表格
        if (hotInstance && tableData.length > 0) {
          hotInstance.loadData(tableData);
          updateRowCount();
          loading.close();
          ElMessage.success('Excel文件导入成功');
        } else {
          loading.close();
          ElMessage.warning('Excel文件中没有有效数据');
        }
      } catch (error) {
        loading.close();
        console.error('Excel导入错误:', error);
        ElMessage.error('Excel文件导入失败，请检查文件格式');
      }
    };
    reader.readAsArrayBuffer(file.raw);

    nextTick(() => {
      excelUploader.value.clearFiles();
    });
  };

  // 处理文献校验
  const handleLiteratureValidation = () => {
    // 重置校验状态
    validationUpload.validationCompleted = false;
    validationUpload.resultSummary = { total: 0, valid: 0, invalid: 0 };
    validationUpload.resultFileUrl = '';

    validationUpload.title = '文献校验';
    validationUpload.open = true;
  };

  // 文献校验文件上传中处理
  const handleValidationFileUploadProgress = () => {
    validationUpload.isUploading = true;
  };

  // 文献校验文件上传成功处理
  const handleValidationFileSuccess = (response, file) => {
    validationUpload.isUploading = false;
    validationUploadRef.value.handleRemove(file);

    if (response.code === 200) {
      ElMessage.success('文献校验完成');

      // 更新校验状态和结果
      validationUpload.validationCompleted = true;
      validationUpload.resultSummary = response.data.summary || {
        total: 0,
        valid: 0,
        invalid: 0,
      };
      validationUpload.resultFileUrl = response.data.fileUrl || '';
    } else {
      ElMessage.error(response.msg || '文献校验失败');
    }
  };

  // 文献校验文件上传失败处理
  const handleValidationFileError = (error, file) => {
    validationUpload.isUploading = false;
    validationUploadRef.value.handleRemove(file);
    console.error('文献校验上传失败:', error);
    ElMessage.error('文献校验失败，请重试');
  };

  // 提交文献校验文件
  const submitValidationFileForm = () => {
    validationUploadRef.value.submit();
  };

  // 下载校验结果
  const downloadValidationResult = async () => {
    if (!validationUpload.resultFileUrl) {
      ElMessage.error('下载地址不存在');
      return;
    }

    try {
      // 从URL中提取文件路径参数
      const url = new URL(
        validationUpload.resultFileUrl,
        window.location.origin,
      );
      const filePath = url.searchParams.get('filePath');

      if (!filePath) {
        ElMessage.error('文件路径参数不存在');
        return;
      }

      // 使用request下载文件
      const response = await request({
        url: `/task/downloadValidationResult`,
        method: 'get',
        params: { filePath },
        responseType: 'blob',
      });

      // 创建blob URL并下载
      const blob = new Blob([response], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });

      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = filePath;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);

      ElMessage.success('文件下载成功');
    } catch (error) {
      console.error('下载失败:', error);
      ElMessage.error('文件下载失败');
    }
  };

  // 关闭校验对话框
  const closeValidationDialog = () => {
    validationUpload.open = false;
    // 重置状态
    validationUpload.isUploading = false;
    validationUpload.validationCompleted = false;
    validationUpload.resultSummary = { total: 0, valid: 0, invalid: 0 };
    validationUpload.resultFileUrl = '';
  };

  // 处理优先级变更
  const handlePriorityChange = value => {
    if (value !== 1) {
      taskForm.customPriority = null;
    } else {
      taskForm.customPriority = 50;
    }
  };

  // 处理测试任务标志变更
  const handleTestFlagChange = value => {
    if (value === 1) {
      // 当选择测试任务时，加载站点列表
      loadSiteOptions();
    } else {
      // 当取消测试任务时，清空站点选择
      taskForm.siteId = null;
    }
  };

  const handleNodeTypesChange = value => {
    // console.log(value);
    taskForm.siteId = null;
  };

  const handleSiteIdChange = value => {
    const siteType = value.split('_')[1];
    taskForm.nodeTypes = [];
    taskForm.nodeTypes.push(siteType);
  };

  // 加载站点选项
  const loadSiteOptions = async () => {
    try {
      const response = await getAllPdsSites();
      if (response.code === 200) {
        siteOptions.value = response.data || [];
      } else {
        ElMessage.error('获取站点列表失败');
      }
    } catch (error) {
      console.error('获取站点列表错误:', error);
      ElMessage.error('获取站点列表失败');
    }
  };

  // 提交任务
  const submitTask = () => {
    taskFormRef.value.validate(valid => {
      if (valid) {
        // 从表格获取文献标识符数据
        const tableData = hotInstance ? hotInstance.getData() : [];
        const literatureData = tableData.filter(row =>
          row.some(cell => !!trimStr(cell)),
        );

        if (literatureData.length === 0) {
          ElMessage.warning('请添加至少一条文献标识符数据');
          return;
        }

        // 验证行数限制
        if (literatureData.length > 5000) {
          ElMessage.error('文献标识符数据不能超过5000行');
          return;
        }

        // 构建提交数据
        const formData = {
          ...taskForm,
          priority:
            taskForm.priorityType === 1
              ? taskForm.customPriority
              : taskForm.priorityType,
          literatureData: literatureData,
          siteId: taskForm.testFlag === 1 ? taskForm.siteId : null, // 只有测试任务才传递siteId
        };

        // 显示加载中提示
        const loading = ElLoading.service({
          lock: true,
          text: '任务提交中...',
          background: 'rgba(0, 0, 0, 0.7)',
        });

        // 调用API提交任务
        publishTask(formData)
          .then(response => {
            loading.close();
            if (response.code === 200) {
              ElMessage.success('任务发布成功！');
              resetForm();
            } else {
              ElMessage.error(response.msg || '任务发布失败');
            }
          })
          .catch(error => {
            loading.close();
            console.error('任务提交错误:', error);
            ElMessage.error('任务发布失败，请检查网络连接');
          });
      } else {
        return false;
      }
    });
  };

  // 重置表单
  const resetForm = () => {
    taskFormRef.value.resetFields();
    fileContent.value = '';
    filePreview.value = '';

    // 使用taskFormInit中的值重置taskForm
    Object.keys(taskFormInit).forEach(key => {
      taskForm[key] = taskFormInit[key];
    });

    // 重置站点选择
    siteOptions.value = [];

    // 重置表格
    if (hotInstance) {
      hotInstance.loadData(initialTableDataReset);
      updateRowCount();
    }
  };

  onMounted(() => {
    console.log('任务分发模块已加载', taskFormInit);
    initTable();
  });

  onUnmounted(() => {
    // 销毁表格实例
    if (hotInstance) {
      hotInstance.destroy();
      hotInstance = null;
    }
  });
</script>

<style lang="scss" scoped>
  .box-card {
    margin-left: 20%;
    margin-bottom: 20px;
    margin-top: 30px;
    width: 800px;
    padding: 20px;
    padding-top: 30px;
    border-radius: 8px;
    background-color: #fff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }

  .standard-width {
    width: 400px !important;
  }

  .standard-width-full {
    width: 100% !important;
  }

  .submit-tabs {
    width: 100%;
  }

  .literature-upload {
    width: 100%;
    margin-bottom: 15px;
  }

  .file-preview {
    margin-top: 15px;
    padding: 15px;
    border: 1px dashed #d9d9d9;
    border-radius: 4px;

    .preview-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
      font-weight: bold;
    }
  }

  .form-item-tip {
    width: 100%;
    font-size: 12px;
    color: #909399;
    line-height: 1.4;
    margin-top: 6px;
    padding-left: 2px;
  }

  // 优先级自定义部分的特殊样式
  .priority-container {
    display: flex;
    align-items: center;
  }

  .custom-priority-radio {
    display: flex !important;
    align-items: center !important;
  }

  .custom-priority-input {
    margin-left: 8px;
    width: 120px !important;
  }

  // 文献表格相关样式
  .literature-input-container {
    width: 100%;
  }

  .excel-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1px;

    .excel-actions-left {
      display: flex;
      gap: 16px;
    }

    .excel-actions-right {
      display: flex;
    }

    .el-button {
      display: flex;
      align-items: center;

      .el-icon {
        margin-right: 4px;
      }
    }
  }

  .hot-container {
    width: 100%;
    margin-bottom: 12px;
    border-radius: 4px;
    overflow: hidden;
  }

  .table-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 8px;
    font-size: 14px;
    color: #606266;
  }

  // 文献校验结果样式
  .validation-result {
    .download-section {
      margin-top: 20px;
      text-align: center;

      p {
        margin-bottom: 15px;
        color: #606266;
        font-size: 14px;
      }
    }
  }
</style>
