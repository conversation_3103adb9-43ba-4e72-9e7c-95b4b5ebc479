package org.biosino.lf.pds.article.mapper;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.biosino.lf.pds.article.custbean.dto.SelectJournalDTO;
import org.biosino.lf.pds.article.custbean.vo.SelectJournalVO;
import org.biosino.lf.pds.article.domain.Journal;
import org.biosino.lf.pds.article.dto.JournalQueryDTO;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 期刊表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 */
@Mapper
public interface JournalMapper extends CommonMapper<Journal> {

    /**
     * 查询待分配期刊列表
     *
     * @param dto 查询参数
     * @return 期刊列表
     */
    List<SelectJournalVO> findToSelectJournal(SelectJournalDTO dto);

    /**
     * 查询已分配期刊列表
     *
     * @param dto 查询参数
     * @return 期刊列表
     */
    List<SelectJournalVO> findAssignedJournalList(SelectJournalDTO dto);

    /**
     * 批量更新期刊脚本ID
     *
     * @param journalIds 期刊ID列表
     * @param scriptId   脚本ID
     * @param updateTime 更新时间
     * @return 更新的记录数
     */
    int batchUpdateScriptId(@Param("journalIds") List<Long> journalIds, @Param("scriptId") Integer scriptId, @Param("updateTime") Date updateTime);

    /**
     * 查询待应用的期刊id列表
     *
     * @param journalIds 期刊ID列表
     * @param labelId    标签ID
     */
    List<Long> findUnApplyJournalIds(@Param("journalIds") List<Long> journalIds, @Param("labelId") Integer labelId);


    List<Journal> selectJournalList(JournalQueryDTO queryDTO);

    Journal selectJournalById(Long id);

    void updatePublisherIdBatch(@NotNull Long targetId, @NotNull @NotEmpty List<Long> sourceIds);

    /**
     * 查找与指定ISSN Print值冲突的期刊
     * @param issnPrint ISSN Print值
     * @param excludeIds 要排除的期刊ID列表
     * @return 冲突的期刊列表
     */
    List<Journal> findConflictingJournalsByIssnPrint(@Param("issnPrint") String issnPrint, @Param("excludeIds") List<Long> excludeIds);

    /**
     * 查找与指定ISSN Electronic值冲突的期刊
     * @param issnElectronic ISSN Electronic值
     * @param excludeIds 要排除的期刊ID列表
     * @return 冲突的期刊列表
     */
    List<Journal> findConflictingJournalsByIssnElectronic(@Param("issnElectronic") String issnElectronic, @Param("excludeIds") List<Long> excludeIds);

    /**
     * 查找与指定Unique NLM ID值冲突的期刊
     * @param uniqueNlmId Unique NLM ID值
     * @param excludeIds 要排除的期刊ID列表
     * @return 冲突的期刊列表
     */
    List<Journal> findConflictingJournalsByUniqueNlmId(@Param("uniqueNlmId") String uniqueNlmId, @Param("excludeIds") List<Long> excludeIds);

    /**
     * 查找与指定ISSN History值冲突的期刊
     * @param issnValue ISSN History中的值
     * @param excludeIds 要排除的期刊ID列表
     * @return 冲突的期刊列表
     */
    List<Journal> findConflictingJournalsByIssnHistory(@Param("issnValue") String issnValue, @Param("excludeIds") List<Long> excludeIds);

    /**
     * 查找与指定Unique History值冲突的期刊
     * @param uniqueValue Unique History中的值
     * @param excludeIds 要排除的期刊ID列表
     * @return 冲突的期刊列表
     */
    List<Journal> findConflictingJournalsByUniqueHistory(@Param("uniqueValue") String uniqueValue, @Param("excludeIds") List<Long> excludeIds);

}
