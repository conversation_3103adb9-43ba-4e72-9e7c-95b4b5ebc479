package org.biosino.lf.pds.article.service;

import org.biosino.lf.pds.article.dto.JournalMergeDTO;
import org.biosino.lf.pds.article.dto.JournalUpdateDTO;
import org.biosino.lf.pds.article.dto.JournalValidationResult;

import java.util.List;

/**
 * 期刊验证服务接口
 *
 * <AUTHOR>
 */
public interface JournalValidationService {

    /**
     * 验证期刊更新时的ISSN字段唯一性
     *
     * @param journalUpdateDTO 期刊更新DTO
     * @return 验证结果
     */
    JournalValidationResult validateJournalUpdate(JournalUpdateDTO journalUpdateDTO);

    /**
     * 验证期刊合并时的ISSN字段唯一性
     *
     * @param journalMergeDTO 期刊合并DTO
     * @return 验证结果
     */
    JournalValidationResult validateJournalMerge(JournalMergeDTO journalMergeDTO);

    /**
     * 验证单个ISSN值的唯一性
     *
     * @param fieldName  字段名称
     * @param value      要验证的值
     * @param excludeIds 要排除的期刊ID列表
     * @return 验证结果
     */
    JournalValidationResult validateSingleIssnValue(String fieldName, String value, List<Long> excludeIds);
}
