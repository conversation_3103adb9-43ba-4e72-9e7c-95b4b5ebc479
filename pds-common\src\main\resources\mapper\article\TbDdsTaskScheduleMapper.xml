<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.biosino.lf.pds.article.mapper.TbDdsTaskScheduleMapper">

    <update id="updateNotCompleteTaskScheduleStatusByDocId">
        UPDATE tb_dds_task_schedule t
        SET status = #{changeToStatus} FROM tb_dds_task_paper p
        WHERE t.paper_id = p.id
        AND p.doc_id = #{docId}
        AND t.status IN
        <foreach collection="notCompleteStatus" item="statusVal" open="(" separator="," close=")">
            #{statusVal}
        </foreach>
    </update>

    <select id="getTaskScheduleByPaperId" resultType="org.biosino.lf.pds.article.domain.TbDdsTaskSchedule">
        SELECT sc.*, s.site_name, s.site_type
        FROM tb_dds_task_schedule sc
                 INNER JOIN tb_dds_site s ON sc.site_id = s.id
                 INNER JOIN tb_dds_task_paper p ON sc.paper_id = p.id
        WHERE sc.paper_id = #{paperId}
    </select>

    <delete id="deleteByTaskId">
        delete
        from tb_dds_task_schedule
        where paper_id in (select id from tb_dds_task_paper where task_id = #{taskId})
    </delete>

    <select id="countGroupByStatusOfSite" resultType="org.biosino.lf.pds.article.custbean.vo.StatInfoVO">
        select h.status, count(h.id) num
        from tb_dds_task_schedule h
        where h.site_id = #{siteId}
        group by h.status
    </select>

    <select id="countGroupByStatusOfSites" resultType="org.biosino.lf.pds.article.custbean.vo.StatInfoVO">
        select h.site_id, h.status, count(h.id) num
        from tb_dds_task_schedule h
        where h.site_id in
        <foreach item="siteId" collection="siteIds" separator="," open="(" close=")" index="">
            #{siteId}
        </foreach>
        group by h.site_id, h.status
    </select>

</mapper>