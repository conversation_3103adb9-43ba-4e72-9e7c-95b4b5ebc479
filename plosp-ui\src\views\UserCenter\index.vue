<template>
  <div class="user-center">
    <section class="content-section">
      <div class="container">
        <!-- 用户信息卡片 -->
        <div class="user-info-card vip-user-card">
          <div class="user-info-background">
            <!-- 装饰元素 -->
            <div class="decoration-dots">
              <div class="dot"></div>
              <div class="dot"></div>
              <div class="dot"></div>
            </div>
            <div class="decoration-lines"></div>

            <div class="user-info-content">
              <div class="user-avatar">
                <div class="avatar-placeholder">
                  <img src="@/assets/images/user-img.png"/>
                </div>
              </div>
              <div class="user-details">
                <div class="user-name-section">
                  <h2 class="user-name">{{ authStore.userName || '用户' }}</h2>
                  <div class="vip-badge">
<!--                    <el-icon class="vip-icon"><Crown /></el-icon>-->
                    <span class="vip-text">VIP</span>
                  </div>
                </div>
                <div class="user-meta">
                  <div class="user-email">
                    <el-icon><Star /></el-icon>
                    <span>积分：{{ userInfo?.points || 0 }}</span>
                  </div>
                  <div class="user-email">
                    <el-icon><Message /></el-icon>
                    <span>{{ authStore.userEmail || '未设置邮箱' }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Tab导航区域 -->
        <div class="user-tabs-container">
          <el-tabs v-model="activeTab" class="user-tabs">
            <el-tab-pane label="主页" name="home">
              <HomePage />
            </el-tab-pane>
            <el-tab-pane label="专题" name="topics">
              <div class="tab-content-placeholder">专题内容开发中...</div>
            </el-tab-pane>
            <el-tab-pane label="收藏" name="favorites">
              <Collect/>
            </el-tab-pane>
            <el-tab-pane label="文献传递" name="delivery">
              <ArticleTransmit/>
            </el-tab-pane>
            <el-tab-pane label="我的上传" name="uploads">
              <div class="tab-content-placeholder">内容开发中...</div>
            </el-tab-pane>
            <el-tab-pane label="我的纠错" name="corrections">
              <ErrorCorrection/>
            </el-tab-pane>
            <el-tab-pane label="浏览历史" name="history">
             <ArticleHistory/>
            </el-tab-pane>
            <el-tab-pane label="API密钥" name="api">
              <API/>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Star, Message, User } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import HomePage from './components/HomePage.vue'
import Collect from './components/Collect.vue'
import ArticleTransmit from './components/ArticleTransmit.vue'
import Upload from './components/Upload.vue'
import API from './components/API.vue'
import ArticleHistory from './components/ArticleHistory.vue'
import ErrorCorrection from './components/ErrorCorrection.vue'

const router = useRouter()
const authStore = useAuthStore()

// 当前激活的Tab
const activeTab = ref('home')

// 用户信息
const userInfo = computed(() => authStore.userInfo)

// 组件挂载时检查登录状态
onMounted(async () => {
  if (!authStore.isLoggedIn) {
    router.push('/login')
    return
  }

  // 确保用户信息是最新的
  await authStore.fetchUserInfo()
})
</script>

<style lang="scss" scoped>
@import "@/assets/styles/variables";

.user-center {
  background-color: #F5F5F5;
  padding: $spacing-xxl 0;
}

.content-section {
  .container {
    max-width: 1536px;
    margin: 0 auto;
    padding: 0 $spacing-md;
  }
}

// 用户信息卡片
.user-info-card {
  margin-bottom: $spacing-xl;
  position: relative;

  // VIP用户特殊光晕效果
  &.vip-user-card {
    &::before {
      content: '';
      position: absolute;
      top: -3px;
      left: -3px;
      right: -3px;
      bottom: -3px;
      background: linear-gradient(135deg,
        rgba(255, 215, 0, 0.1),
        rgba(248, 250, 252, 0.3),
        rgba(255, 215, 0, 0.1));
      border-radius: calc(#{$border-radius-xl} + 3px);
      z-index: -1;
      opacity: 0.6;
      filter: blur(12px);
      //animation: vipCardGlow 3s ease-in-out infinite alternate;
    }
  }

  // 普通用户光晕效果
  &:not(.vip-user-card)::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, rgba(226, 232, 240, 0.2), rgba(241, 245, 249, 0.15));
    border-radius: calc(#{$border-radius-xl} + 2px);
    z-index: -1;
    opacity: 0.4;
    filter: blur(8px);
  }
}

.user-info-background {
  background-image: linear-gradient(180deg, #F0F5F8 0%, #FFFFFF 100%);
  border: 1px solid #004b912e;
  border-radius: $border-radius-xl;
  box-shadow:6px 6px 54px 0px rgba(0, 0, 0, 0.05);
  padding: $spacing-xl;
  position: relative;
  overflow: hidden;
  //border: 1px solid rgba(226, 232, 240, 0.8);
  transition: all 0.3s ease;
  backdrop-filter: blur(20px);

  // 主装饰圆形
  &::before {
    content: '';
    position: absolute;
    top: -50px;
    right: -50px;
    width: 180px;
    height: 180px;
    background: linear-gradient(45deg, rgba(194, 205, 217, 0.2), rgba(241, 245, 249, 0.1));
    border-radius: 50%;
    backdrop-filter: blur(10px);
  }

  // 次要装饰圆形
  &::after {
    content: '';
    position: absolute;
    bottom: -30px;
    left: -30px;
    width: 120px;
    height: 120px;
    background: linear-gradient(225deg, rgba(203, 213, 225, 0.25), rgba(226, 232, 240, 0.15));
    border-radius: 50%;
    backdrop-filter: blur(5px);
  }

  // 额外的装饰元素
  .decoration-dots {
    position: absolute;
    top: 20px;
    left: 20px;
    display: flex;
    gap: 8px;

    .dot {
      width: 8px;
      height: 8px;
      background: rgba(148, 163, 184, 0.4);
      border-radius: 50%;
      animation: pulse 2s infinite;

      &:nth-child(2) {
        animation-delay: 0.5s;
      }

      &:nth-child(3) {
        animation-delay: 1s;
      }
    }
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.2);
  }
}



.user-info-content {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  gap: $spacing-lg;

  @media (max-width: $breakpoint-md) {
    flex-direction: column;
    text-align: center;
    gap: $spacing-md;
  }
}

.user-avatar {
  position: relative;

  img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    border: 3px solid rgba(255, 255, 255, 0.4);
    object-fit: cover;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;

    &:hover {
      transform: scale(1.05);
      box-shadow: 0 6px 25px rgba(0, 0, 0, 0.15);
    }
  }

  // 头像光环效果
  &::after {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    border-radius: 50%;
    background: linear-gradient(45deg, rgba(203, 213, 225, 0.4), transparent, rgba(203, 213, 225, 0.4));
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
  }

  &:hover::after {
    opacity: 1;
  }
}

.user-details {
  flex: 1;
  color: $white;
}

// 用户名区域
.user-name-section {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  margin-bottom: $spacing-md;

  @media (max-width: $breakpoint-md) {
    flex-direction: column;
    align-items: flex-start;
    gap: $spacing-xs;
  }
}

.user-name {
  font-size: $font-size-xlarge;
  font-weight: $font-weight-bold;
  margin: 0;
  color: #0C4778;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
  letter-spacing: 0.5px;
}

// VIP徽章样式
.vip-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
  color: #8B4513;
  padding: 2px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: $font-weight-bold;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 1px solid rgba(255, 215, 0, 0.5);
  position: relative;
  overflow: hidden;

  .vip-icon {
    font-size: 14px;
    color: #8B4513;
    filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.2));
  }

  .vip-text {
    font-weight: $font-weight-extrabold;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
    font-size: 14px;
  }
}

.user-meta {
  display: flex;
  gap: $spacing-xl;

  @media (max-width: $breakpoint-md) {
    flex-direction: column;
    gap: $spacing-sm;
  }
}

.user-points,
.user-email {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  font-size: $font-size-medium;
  color: #64748b;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.8));
  padding: $spacing-xs $spacing-sm;
  border-radius: $border-radius-lg;
  backdrop-filter: blur(20px);
  //border: 1px solid rgba(203, 213, 225, 0.3);
  transition: all 0.3s ease;
  //box-shadow:
  //  0 1px 3px rgba(0, 0, 0, 0.02),
  //  inset 0 1px 0 rgba(255, 255, 255, 0.7);

  &:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.9));
    transform: translateY(-1px);
    box-shadow:
      0 2px 6px rgba(0, 0, 0, 0.04),
      inset 0 1px 0 rgba(255, 255, 255, 0.8);
    border-color: rgba(148, 163, 184, 0.4);
    color: #475569;
  }

  .el-icon {
    font-size: $font-size-large;
    color: #94a3b8;
  }

  span {
    font-weight: $font-weight-medium;
    text-shadow: none;
    color: #475569;
  }
}

// 邮箱卡片特殊样式
.user-email {
  background: linear-gradient(135deg, rgba(239, 246, 255, 0.6), rgba(245, 250, 255, 0.8));
  border: 1px solid rgba(59, 130, 246, 0.15);
  box-shadow:
    0 1px 3px rgba(59, 130, 246, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.7);

  .el-icon {
    color: #004E97;
    transition: all 0.3s ease;
  }

  span {
    color: #1e40af;
  }

  &:hover {
    background: linear-gradient(135deg, rgba(239, 246, 255, 0.8), rgba(245, 250, 255, 0.9));
    border-color: rgba(59, 130, 246, 0.25);
    box-shadow:
      0 2px 6px rgba(59, 130, 246, 0.12),
      inset 0 1px 0 rgba(255, 255, 255, 0.8);

    .el-icon {
      transform: scale(1.1);
      color: #2563eb;
    }
  }
}

// Tab导航区域
.user-tabs-container {
  background-color: $white;
  border-radius: $border-radius-xl;
  box-shadow: $box-shadow;
  padding: $spacing-lg;
}

.tab-content-placeholder {
  padding: $spacing-xxl;
  text-align: center;
  color: $gray;
  font-size: $font-size-medium;
}

// Element Plus Tab样式覆盖
:deep(.user-tabs) {
  .el-tabs__header {
    margin: 0 0 $spacing-lg 0;
  }

  .el-tabs__nav-wrap {
    &::after {
      display: none;
    }
  }

  .el-tabs__nav {
    border: none;
  }

  .el-tabs__item {
    font-size: $font-size-medium;
    font-weight: $font-weight-medium;
    color: $gray;
    border: none;
    padding: 0 $spacing-lg;
    height: 50px;
    line-height: 50px;

    &.is-active {
      color: $primary-color;
      font-weight: $font-weight-bold;
    }

    &:hover {
      color: $primary-color;
    }
  }

  .el-tabs__active-bar {
    background-color: $primary-color;
    height: 3px;
  }

  .el-tabs__content {
    padding: 0;
  }
}

// 响应式调整
@media (max-width: $breakpoint-md) {
  .user-center {
    padding: $spacing-lg 0;
  }

  .user-info-background {
    padding: $spacing-lg;

    .decoration-dots {
      top: 15px;
      left: 15px;

      .dot {
        width: 6px;
        height: 6px;
      }
    }

    .decoration-lines {
      top: 20px;
      right: 20px;
      width: 40px;
      height: 40px;

      &::before {
        width: 25px;
        top: 15px;
      }

      &::after {
        width: 15px;
        top: 25px;
        right: 5px;
      }
    }
  }

  .user-avatar {
    .avatar-placeholder {
      width: 70px;
      height: 70px;
      font-size: 28px;
    }

    img {
      width: 70px;
      height: 70px;
    }
  }

  .user-name {
    font-size: $font-size-large;
  }

  .vip-badge {
    font-size: 10px;
    padding: 3px 8px;

    .vip-icon {
      font-size: 12px;
    }
  }

  .user-points,
  .user-email {
    font-size: $font-size-small;
    padding: 6px $spacing-xs;

    .el-icon {
      font-size: $font-size-medium;
    }
  }

  .vip-points-bonus {
    font-size: 8px;
    padding: 1px 4px;
    top: -5px;
    right: -5px;
  }

  .user-tabs-container {
    padding: $spacing-md;
  }

  :deep(.user-tabs) {
    .el-tabs__item {
      font-size: $font-size-small;
      padding: 0 $spacing-md;
    }
  }
}
</style>
