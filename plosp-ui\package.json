{"name": "plosp-app", "version": "0.1.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "dependencies": {"axios": "^1.4.0", "echarts": "^5.6.0", "element-plus": "^2.3.9", "pinia": "^2.1.6", "swiper": "^11.2.8", "vue": "^3.3.4", "vue-router": "^4.2.4"}, "devDependencies": {"@vitejs/plugin-vue": "^4.2.3", "eslint": "^8.46.0", "eslint-plugin-vue": "^9.16.1", "sass": "^1.64.2", "vite": "^4.4.9"}}