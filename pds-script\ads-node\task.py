# coding=utf8
__author__ = 'yhju'

import logging
import random
import threading
import time
import queue
from collections import defaultdict
from concurrent.futures import Thread<PERSON>oolExecutor
from enum import Enum

import api
import db
import util
from task_executor import TaskExecutor

logger = logging.getLogger()


class DocTaskStatus(Enum):
    """文档任务状态枚举"""
    PENDING = "pending"      # 待处理
    PROCESSING = "processing"  # 处理中
    SUCCESS = "success"      # 成功
    FAILED = "failed"        # 失败（在所有脚本中都失败过一次）


class DocTaskInfo:
    """文档任务信息类"""
    def __init__(self, task_info):
        self.task_info = task_info
        self.doc_id = task_info.get('docId', 'Unknown')
        self.task_id = task_info.get('taskId', 'Unknown')
        self.pmid = task_info.get('pmid', '')
        self.status = DocTaskStatus.PENDING
        self.script_attempts = {}  # 记录每个脚本的尝试状态 {script_id: (success, error_msg)}
        self.result_dir_path = None
        self.lock = threading.Lock()

    def mark_script_attempt(self, script_id, success, error_msg=None):
        """标记脚本尝试结果"""
        with self.lock:
            self.script_attempts[script_id] = (success, error_msg)
            if success:
                self.status = DocTaskStatus.SUCCESS

    def has_script_attempted(self, script_id):
        """检查脚本是否已经尝试过"""
        with self.lock:
            return script_id in self.script_attempts

    def get_failed_scripts(self, available_script_ids):
        """获取失败的脚本ID列表（排除已成功的）"""
        with self.lock:
            if self.status == DocTaskStatus.SUCCESS:
                return []

            failed_scripts = []
            for script_id in available_script_ids:
                if script_id in self.script_attempts:
                    success, _ = self.script_attempts[script_id]
                    if not success:
                        failed_scripts.append(script_id)
            return failed_scripts

    def is_all_scripts_failed(self, available_script_ids):
        """检查是否所有脚本都失败了"""
        with self.lock:
            if self.status == DocTaskStatus.SUCCESS:
                return False

            for script_id in available_script_ids:
                if script_id not in self.script_attempts:
                    return False  # 还有脚本没尝试
                success, _ = self.script_attempts[script_id]
                if success:
                    return False  # 有成功的脚本

            # 所有脚本都尝试过且都失败了
            self.status = DocTaskStatus.FAILED
            return True


class TaskProcessor(threading.Thread, TaskExecutor):
    """
    主运行类，任务调度线程
    1，获取任务
    2，通过脚本控制器获取下载脚本
    3，多脚本协作执行：第一个脚本处理当前doc_id，其他脚本获取新doc_id，完成后互相重试失败任务
    """

    def __init__(self, site_id: int, db_file: db.DbFile, api_invoker: api.ApiInvoker):
        threading.Thread.__init__(self, daemon=True)
        TaskExecutor.__init__(self, site_id, db_file, api_invoker)
        self.__stop = False
        self.__task_lock = threading.Lock()

        # 脚本并行执行相关
        self.__max_script_workers = 5  # 最多5个脚本并发执行
        self.__script_executor = ThreadPoolExecutor(max_workers=self.__max_script_workers, thread_name_prefix="ScriptWorker")
        self.__active_script_ids = set()  # 当前正在执行的脚本ID集合
        self.__active_script_ids_lock = threading.Lock()  # 保护active_script_ids的锁
        self.__script_sleep_times = {}  # 记录每个脚本ID的睡眠结束时间
        self.__script_sleep_lock = threading.Lock()  # 保护script_sleep_times的锁

        # 文档任务状态管理
        self.__doc_tasks = {}  # 存储所有文档任务 {doc_id: DocTaskInfo}
        self.__doc_tasks_lock = threading.Lock()  # 保护doc_tasks的锁
        self.__available_script_ids = []  # 当前站点可用的脚本ID列表

    def run(self) -> None:
        while True:
            if self.__stop:
                logger.info("任务调度线程已停止, 站点: {}".format(self._site_id))
                return

            # 获取下一个任务
            if self.__task_lock.locked():
                sleep_time = 5
                logger.info("有其它线程正在处理任务,休眠{}秒后重试".format(sleep_time))
                self.safe_sleep(sleep_time)
                continue

            try:
                # 调用 api 获取下一个任务（单线程模式不传递活跃脚本ID）
                logger.info("获取下一个任务")
                task_info = self._api_invoker.next_task_info()
                # 解析任务状态
                task_status = task_info.get("status", "")
                # 没有任务了就休眠一会儿
                if "none" == task_status:
                    sleep_time = 30
                    logger.info("服务端没有任务，休眠{}秒后重试".format(sleep_time))
                    self.safe_sleep(sleep_time)
                    continue

                # 获取任务失败了
                if str(task_status).strip().lower() != "success":
                    logger.info(f"获取任务失败 站点：{self._site_id} Task: {task_info}")
                    continue

                # 验证任务信息
                is_valid, error_msg = self.validate_task_info(task_info)
                if not is_valid:
                    logger.warning(f"获取任务失败 {error_msg}, Task: {task_info}")
                    continue

                # 执行任务
                self.__execute_task(task=task_info)
            except BaseException as e:
                if 'task_info' in locals():
                    self.handle_task_error(task_info, e, "单线程")
            finally:
                self.__task_end(task_info)

    def __is_script_sleeping(self, script_id):
        """检查脚本ID是否还在睡眠期"""
        with self.__script_sleep_lock:
            if script_id in self.__script_sleep_times:
                return time.time() < self.__script_sleep_times[script_id]
            return False

    def __get_script_sleep_remaining(self, script_id):
        """获取脚本ID剩余睡眠时间"""
        with self.__script_sleep_lock:
            if script_id in self.__script_sleep_times:
                remaining = self.__script_sleep_times[script_id] - time.time()
                return max(0, remaining)
            return 0

    def __set_script_sleep_time(self, script_id, sleep_seconds):
        """设置脚本ID的睡眠时间"""
        with self.__script_sleep_lock:
            self.__script_sleep_times[script_id] = time.time() + sleep_seconds
            logger.debug(f"设置脚本ID {script_id} 睡眠 {sleep_seconds}s")

    def __cleanup_script_id(self, script_id):
        """清理脚本ID，确保从active_script_ids中移除"""
        with self.__active_script_ids_lock:
            if script_id in self.__active_script_ids:
                self.__active_script_ids.discard(script_id)
                logger.debug(f"清理脚本ID: {script_id}，当前活跃脚本数: {len(self.__active_script_ids)}")

    def __extract_script_id(self, script_name, task):
        """
        从脚本名称中提取脚本ID
        :param script_name: 脚本名称，如 "jur_123.pyscript" 或 "sit_abc123.pyscript"
        :param task: 任务信息
        :return: 脚本ID
        """
        try:
            # 从脚本名称中提取（优先使用脚本名称，确保每个脚本有不同的ID）
            if script_name.startswith("jur_"):
                # 期刊脚本：jur_journalId.pyscript
                return script_name.replace("jur_", "").replace(".pyscript", "")
            elif script_name.startswith("sit_"):
                # 站点脚本：sit_md5.pyscript
                return script_name.replace("sit_", "").replace(".pyscript", "")
            else:
                # 默认使用脚本名称作为ID（去掉.pyscript后缀）
                base_name = script_name.replace(".pyscript", "")
                return base_name
        except Exception as e:
            logger.warning(f"提取脚本ID失败，使用脚本名称作为ID: {script_name}, 错误: {e}")
            return script_name.replace(".pyscript", "")

    def __execute_task(self, task) -> bool:
        """调用脚本，执行下载任务，使用多脚本协作执行"""
        return self.__execute_task_with_collaborative_scripts(task)

    def __execute_task_with_collaborative_scripts(self, task) -> bool:
        """
        多脚本协作执行：第一个脚本处理当前doc_id，其他脚本获取新doc_id，完成后互相重试失败任务
        :param task: 任务信息
        :return: 是否成功
        """
        task_id = task.get('taskId', 'Unknown')
        doc_id = task.get('docId', 'Unknown')
        # pmid = task.get('pmid', '')

        # 获取所有可用脚本
        try:
            # 收集所有脚本
            script_modules = list(self._script_context.iter_script_modules(task))
            if not script_modules:
                logger.warning(f"没有找到可用的脚本，任务执行失败 TaskId: {task_id}")
                return False

            # 更新可用脚本ID列表
            self.__available_script_ids = [self.__extract_script_id(name, task) for name, _, _ in script_modules]
            logger.info(f"找到 {len(script_modules)} 个可用脚本，准备协作执行: {self.__available_script_ids}")

            # 创建文档任务信息
            doc_task = DocTaskInfo(task)
            doc_task.result_dir_path = self.create_result_dir(task)
            task["resultDirPath"] = doc_task.result_dir_path

            # 将文档任务添加到管理器
            with self.__doc_tasks_lock:
                self.__doc_tasks[doc_id] = doc_task

            # 创建Future列表和脚本ID映射
            futures = []
            future_to_script = {}

            # 提交脚本到线程池
            for i, (name, md5, module) in enumerate(script_modules):
                script_id = self.__extract_script_id(name, task)

                # 检查脚本是否在睡眠期
                if self.__is_script_sleeping(script_id):
                    sleep_remaining = self.__get_script_sleep_remaining(script_id)
                    logger.info(f"脚本 {name} (ID: {script_id}) 还在睡眠期，剩余 {sleep_remaining:.1f}s，跳过")
                    continue

                # 检查脚本是否已在执行
                with self.__active_script_ids_lock:
                    if script_id in self.__active_script_ids:
                        logger.info(f"脚本 {name} (ID: {script_id}) 已有任务在执行，跳过")
                        continue

                    # 标记脚本为活跃状态
                    self.__active_script_ids.add(script_id)

                # 第一个脚本处理当前doc_id，其他脚本获取新任务
                is_first_script = (i == 0)

                # 提交脚本执行任务
                future = self.__script_executor.submit(
                    self.__execute_collaborative_script,
                    name=name,
                    md5=md5,
                    module=module,
                    task=task.copy() if is_first_script else None,  # 第一个脚本使用当前任务，其他脚本获取新任务
                    script_id=script_id,
                    is_first_script=is_first_script
                )
                futures.append(future)
                future_to_script[future] = (name, script_id)
                logger.info(f"提交脚本 {name} (ID: {script_id}) 到线程池执行 {'(处理当前doc_id)' if is_first_script else '(获取新doc_id)'}")

            # 如果没有可执行的脚本，返回失败
            if not futures:
                logger.warning(f"没有可执行的脚本（所有脚本都在睡眠期或已在执行），任务执行失败 TaskId: {task_id}")
                with self.__doc_tasks_lock:
                    if doc_id in self.__doc_tasks:
                        del self.__doc_tasks[doc_id]
                return False

            # 等待所有脚本完成
            for future in futures:
                try:
                    future.result()  # 等待完成，不关心返回值
                except Exception as e:
                    name, script_id = future_to_script[future]
                    logger.exception(f"脚本 {name} 执行异常: {e}")

            # 检查当前doc_id的最终状态
            with self.__doc_tasks_lock:
                final_doc_task = self.__doc_tasks.get(doc_id)
                if final_doc_task:
                    success = (final_doc_task.status == DocTaskStatus.SUCCESS)
                    if success:
                        logger.info(f"文档任务成功完成 TaskId: {task_id}, DocId: {doc_id}")
                        # 通知服务器下载结果
                        self._db.insert_item(task_info=task)
                    else:
                        logger.warning(f"文档任务最终失败 TaskId: {task_id}, DocId: {doc_id}")

                    # 清理文档任务
                    del self.__doc_tasks[doc_id]
                    return success
                else:
                    logger.error(f"文档任务信息丢失 TaskId: {task_id}, DocId: {doc_id}")
                    return False

        except Exception as e:
            logger.exception(f"协作执行脚本时发生异常: {e}")
            # 清理文档任务
            with self.__doc_tasks_lock:
                if doc_id in self.__doc_tasks:
                    del self.__doc_tasks[doc_id]
            return False

    def __execute_collaborative_script(self, name, md5, module, task, script_id, is_first_script):
        """
        执行协作脚本：第一个脚本处理当前任务，其他脚本获取新任务，完成后检查重试机会
        :param name: 脚本名称
        :param md5: 脚本MD5
        :param module: 脚本模块
        :param task: 任务信息（第一个脚本有值，其他脚本为None）
        :param script_id: 脚本ID
        :param is_first_script: 是否为第一个脚本
        :return: 无返回值
        """
        try:
            if is_first_script:
                # 第一个脚本：处理当前doc_id
                logger.info(f"脚本 {name} (ID: {script_id}) 开始处理当前doc_id: {task.get('docId')}")
                self.__process_current_doc_id(name, module, task, script_id)
            else:
                # 其他脚本：获取新的doc_id任务
                logger.info(f"脚本 {name} (ID: {script_id}) 开始获取新的doc_id任务")
                self.__process_new_doc_ids(name, module, script_id)

            # 脚本完成后，检查是否有其他失败的doc_id需要重试
            self.__check_and_retry_failed_docs(name, module, script_id)

        except Exception as e:
            logger.exception(f"协作脚本 {name} (ID: {script_id}) 执行异常: {e}")
        finally:
            # 设置脚本睡眠时间
            sleep_time = self.calculate_sleep_time(task if task else {}, is_multi_thread=True)
            self.__set_script_sleep_time(script_id, sleep_time)
            logger.info(f"脚本 {name} (ID: {script_id}) 执行完成，将睡眠 {sleep_time}s")

            # 清理脚本ID
            self.__cleanup_script_id(script_id)

    def __process_current_doc_id(self, name, module, task, script_id):
        """处理当前doc_id"""
        doc_id = task.get('docId', 'Unknown')
        task_id = task.get('taskId', 'Unknown')

        try:
            logger.info(f"脚本 {name} 开始处理doc_id: {doc_id}")

            # 执行脚本
            self.run_script(name=name, module=module, task=task)

            # 验证执行结果
            has_result = False
            if util.dict_has_value(task, "resultDirPath"):
                has_result = util.check_result_file(result_dir_path=task["resultDirPath"])

            # 更新文档任务状态
            with self.__doc_tasks_lock:
                doc_task = self.__doc_tasks.get(doc_id)
                if doc_task:
                    if has_result:
                        doc_task.mark_script_attempt(script_id, True)
                        logger.info(f"脚本 {name} 成功处理doc_id: {doc_id}")
                    else:
                        doc_task.mark_script_attempt(script_id, False, "没有获取到结果文件")
                        logger.error(f"脚本 {name} 处理doc_id失败: {doc_id} - 没有获取到结果文件")
                        if task.get("resultDirPath"):
                            util.remove_result_dir(task["resultDirPath"])

        except Exception as e:
            logger.exception(f"脚本 {name} 处理doc_id异常: {doc_id} - {e}")
            # 更新文档任务状态为失败
            with self.__doc_tasks_lock:
                doc_task = self.__doc_tasks.get(doc_id)
                if doc_task:
                    doc_task.mark_script_attempt(script_id, False, str(e))

    def __process_new_doc_ids(self, name, module, script_id):
        """处理新的doc_id任务"""
        while not self.__stop:
            try:
                # 获取新任务
                logger.info(f"脚本 {name} 尝试获取新的doc_id任务")

                # 获取当前活跃的脚本ID列表
                with self.__active_script_ids_lock:
                    active_script_ids = list(self.__active_script_ids)

                task_info = self._api_invoker.next_task_info(active_script_ids)
                task_status = task_info.get("status", "")

                # 没有任务了就退出
                if task_status == "none":
                    logger.info(f"脚本 {name} 没有获取到新任务，退出")
                    break

                # 获取任务失败
                if task_status != "success":
                    logger.warning(f"脚本 {name} 获取任务失败: {task_info}")
                    break

                # 验证任务信息
                is_valid, error_msg = self.validate_task_info(task_info)
                if not is_valid:
                    logger.warning(f"脚本 {name} 获取到无效任务: {error_msg}")
                    continue

                # 处理新的doc_id
                new_doc_id = task_info.get('docId', 'Unknown')
                logger.info(f"脚本 {name} 获取到新doc_id: {new_doc_id}")

                # 创建新的文档任务
                new_doc_task = DocTaskInfo(task_info)
                new_doc_task.result_dir_path = self.create_result_dir(task_info)
                task_info["resultDirPath"] = new_doc_task.result_dir_path

                # 添加到文档任务管理器
                with self.__doc_tasks_lock:
                    self.__doc_tasks[new_doc_id] = new_doc_task

                # 执行脚本处理新doc_id
                self.__process_single_doc_id(name, module, task_info, script_id, new_doc_id)

                # 通知服务器任务结果
                with self.__doc_tasks_lock:
                    final_doc_task = self.__doc_tasks.get(new_doc_id)
                    if final_doc_task and final_doc_task.status == DocTaskStatus.SUCCESS:
                        self._db.insert_item(task_info=task_info)

            except Exception as e:
                logger.exception(f"脚本 {name} 处理新doc_id时异常: {e}")
                break

    def __process_single_doc_id(self, name, module, task_info, script_id, doc_id):
        """处理单个doc_id"""
        try:
            logger.info(f"脚本 {name} 开始处理doc_id: {doc_id}")

            # 执行脚本
            self.run_script(name=name, module=module, task=task_info)

            # 验证执行结果
            has_result = False
            if util.dict_has_value(task_info, "resultDirPath"):
                has_result = util.check_result_file(result_dir_path=task_info["resultDirPath"])

            # 更新文档任务状态
            with self.__doc_tasks_lock:
                doc_task = self.__doc_tasks.get(doc_id)
                if doc_task:
                    if has_result:
                        doc_task.mark_script_attempt(script_id, True)
                        logger.info(f"脚本 {name} 成功处理doc_id: {doc_id}")
                    else:
                        doc_task.mark_script_attempt(script_id, False, "没有获取到结果文件")
                        logger.error(f"脚本 {name} 处理doc_id失败: {doc_id} - 没有获取到结果文件")
                        if task_info.get("resultDirPath"):
                            util.remove_result_dir(task_info["resultDirPath"])

        except Exception as e:
            logger.exception(f"脚本 {name} 处理doc_id异常: {doc_id} - {e}")
            # 更新文档任务状态为失败
            with self.__doc_tasks_lock:
                doc_task = self.__doc_tasks.get(doc_id)
                if doc_task:
                    doc_task.mark_script_attempt(script_id, False, str(e))

    def __check_and_retry_failed_docs(self, name, module, script_id):
        """检查并重试失败的文档任务"""
        logger.info(f"脚本 {name} 开始检查失败的文档任务进行重试")

        # 睡眠一段时间，让其他脚本有机会完成
        time.sleep(5)

        retry_count = 0
        max_retries = 10  # 最多重试10个失败的文档

        while retry_count < max_retries and not self.__stop:
            retry_doc_id = None
            retry_task_info = None

            # 查找需要重试的文档
            with self.__doc_tasks_lock:
                for doc_id, doc_task in self.__doc_tasks.items():
                    # 跳过已成功或已在所有脚本中失败的文档
                    if doc_task.status == DocTaskStatus.SUCCESS:
                        continue
                    if doc_task.is_all_scripts_failed(self.__available_script_ids):
                        continue

                    # 检查当前脚本是否已经尝试过这个文档
                    if not doc_task.has_script_attempted(script_id):
                        retry_doc_id = doc_id
                        retry_task_info = doc_task.task_info.copy()
                        retry_task_info["resultDirPath"] = doc_task.result_dir_path
                        break

            # 没有找到需要重试的文档
            if not retry_doc_id:
                logger.info(f"脚本 {name} 没有找到需要重试的文档任务")
                break

            logger.info(f"脚本 {name} 开始重试失败的doc_id: {retry_doc_id}")

            # 重试处理文档
            self.__process_single_doc_id(name, module, retry_task_info, script_id, retry_doc_id)

            retry_count += 1

            # 检查是否成功，如果成功则通知服务器
            with self.__doc_tasks_lock:
                doc_task = self.__doc_tasks.get(retry_doc_id)
                if doc_task and doc_task.status == DocTaskStatus.SUCCESS:
                    logger.info(f"脚本 {name} 重试成功doc_id: {retry_doc_id}")
                    self._db.insert_item(task_info=retry_task_info)
                    break  # 成功后可以继续寻找其他失败的文档

        logger.info(f"脚本 {name} 完成重试检查，共重试了 {retry_count} 个文档")

    def __task_end(self, task):
        """
        任务执行结束休息
        :param task: 任务信息
        """
        sleep_time = self.calculate_sleep_time(task, is_multi_thread=False)
        logger.info(f"【结束】任务执行，休眠 {sleep_time}s")
        self.safe_sleep(sleep_time)

    def stop(self):
        self.__stop = True

        # 关闭脚本执行线程池
        self.__script_executor.shutdown(wait=False)

        # 清空所有活跃脚本ID和睡眠时间记录
        with self.__active_script_ids_lock:
            if self.__active_script_ids:
                logger.info(f"系统停止，清理所有活跃脚本ID: {list(self.__active_script_ids)}")
                self.__active_script_ids.clear()

        with self.__script_sleep_lock:
            if self.__script_sleep_times:
                logger.info(f"系统停止，清理所有脚本睡眠时间记录: {list(self.__script_sleep_times.keys())}")
                self.__script_sleep_times.clear()

        # 清空文档任务记录
        with self.__doc_tasks_lock:
            if self.__doc_tasks:
                logger.info(f"系统停止，清理所有文档任务记录: {list(self.__doc_tasks.keys())}")
                self.__doc_tasks.clear()

# 删除测试代码
