<template>
  <div>
    <div class="search-container">
      <el-form :inline="true" :model="queryData" class="search-form">
        <el-form-item label="出版社名称">
          <div>
            <el-select
              v-model="queryData.publisher"
              multiple
              :multiple-limit="20"
              collapse-tags
              collapse-tags-tooltip
              :max-collapse-tags="1"
              filterable
              remote
              reserve-keyword
              clearable
              remote-show-suffix
              placeholder="请输入出版社名称"
              :remote-method="remoteMethodPublisher"
              :loading="publisherLoading"
              style="width: 240px"
            >
              <el-option
                v-for="item in publisherOptions"
                :key="`pub-${item.id}`"
                :label="item.text"
                :value="item.id"
              />
            </el-select>
          </div>
        </el-form-item>
        <el-form-item label="期刊名称">
          <el-input
            v-model="queryData.journalName"
            placeholder="请输入期刊名称"
            clearable
          />
        </el-form-item>
        <el-form-item label="ISSN Print">
          <el-input
            v-model="queryData.issnPrint"
            placeholder="请输入ISSN Print"
            clearable
          />
        </el-form-item>
        <el-form-item label="ISSN Electronic">
          <el-input
            v-model="queryData.issnElectronic"
            placeholder="请输入ISSN Electronic"
            clearable
          />
        </el-form-item>

        <template v-if="!isAssigned">
          <el-form-item label="是否有脚本">
            <el-select
              v-model="queryData.hasScript"
              placeholder="是否有脚本"
              clearable
              style="width: 120px"
            >
              <el-option label="是" value="yes" />
              <el-option label="否" value="no" />
            </el-select>
          </el-form-item>
        </template>

        <el-form-item label="脚本名称">
          <el-input
            v-model="queryData.scriptName"
            placeholder="请输入脚本名称"
            clearable
            @keyup.enter.prevent="search"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" icon="Search" @click="search"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div>
      <div>
        选中所有页:
        <el-switch
          v-model="selectAllPage"
          @change="handleSelectAllPageChange"
        />
      </div>
      <el-table
        ref="tableRef"
        v-loading="loading"
        :show-overflow-tooltip="true"
        :data="journalList"
        border
        style="width: 100%"
        @selection-change="handleSelectionChange"
        @select-all="handleSelectAll"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column
          label="期刊名称"
          prop="journalName"
          :width="isAssigned ? null : 400"
        />
        <el-table-column label="ISSN Print" prop="issnPrint" width="120" />
        <el-table-column
          label="ISSN Electronic"
          prop="issnElectronic"
          width="140"
        />
        <el-table-column label="出版社名称" prop="publisher" />
        <el-table-column
          label="期刊脚本"
          prop="scriptName"
          show-overflow-tooltip
          :width="isAssigned ? 190 : null"
        >
          <template #default="scope">
            <span v-if="scope.row.scriptId">{{ scope.row.scriptName }}</span>
            <el-tag v-else type="info">无脚本</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          :width="isAssigned ? 220 : 400"
          align="left"
        >
          <template #default="scope">
            <template v-if="isAssigned">
              <el-button
                type="primary"
                link
                icon="Edit"
                @click="$emit('edit-script', scope.row)"
                >更换脚本</el-button
              >
              <el-button
                type="danger"
                link
                icon="Delete"
                @click="$emit('remove-journal', scope.row)"
                >移除期刊</el-button
              >
            </template>
            <template v-else>
              <el-button
                type="success"
                link
                icon="Plus"
                @click="$emit('add-to-label', scope.row)"
                >应用到标签</el-button
              >
              <el-button
                type="primary"
                link
                icon="Edit"
                @click="$emit('assign-script', scope.row)"
                >分配脚本</el-button
              >
              <el-button
                v-if="scope.row.scriptId"
                type="danger"
                link
                icon="Delete"
                @click="$emit('remove-script', scope.row)"
                >移除脚本</el-button
              >
            </template>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="table-footer">
      <div>
        <slot name="footer-buttons"></slot>
      </div>

      <pagination
        v-show="total > 0"
        v-model:page="queryData.pageNum"
        v-model:limit="queryData.pageSize"
        :total="total"
        @pagination="loadData"
      />
    </div>
  </div>
</template>

<script setup>
  import { ref } from 'vue';
  import { findPublisherByName } from '@/api/task/script';

  const props = defineProps({
    isAssigned: {
      type: Boolean,
      default: false,
    },
    queryData: {
      type: Object,
      required: true,
    },
    queryInit: {
      type: Object,
      required: true,
    },
    journalList: {
      type: Array,
      required: true,
    },
    total: {
      type: Number,
      required: true,
    },
    loading: {
      type: Boolean,
      default: false,
    },
  });

  const emit = defineEmits([
    'update:queryData',
    'search',
    'reset',
    'selection-change',
    'select-all-change',
    'edit-script',
    'remove-journal',
    'add-to-label',
    'assign-script',
    'remove-script',
  ]);

  // 表格引用
  const tableRef = ref(null);

  // 全选所有页
  const selectAllPage = ref(false);

  // 出版社选项数据
  const publisherOptions = ref([]);
  const publisherLoading = ref(false);

  // 选中的期刊
  const selected = ref([]);

  // 远程搜索出版社
  const remoteMethodPublisher = query => {
    if (query === '') {
      publisherOptions.value = [];
      return;
    }

    publisherLoading.value = true;
    findPublisherByName(query)
      .then(response => {
        if (response.code === 200 && response.data) {
          publisherOptions.value = response.data;
        } else {
          publisherOptions.value = [];
        }
      })
      .catch(() => {
        publisherOptions.value = [];
      })
      .finally(() => {
        publisherLoading.value = false;
      });
  };

  // 搜索期刊
  const search = () => {
    selectAllPage.value = false;
    props.queryData.pageNum = 1;
    emit('search');
  };

  // 重置搜索
  const resetSearch = () => {
    // 重置查询条件
    Object.keys(props.queryInit).forEach(key => {
      props.queryData[key] = props.queryInit[key];
    });
    selectAllPage.value = false;
    emit('reset');
  };

  // 处理全选所有页开关变化
  const handleSelectAllPageChange = val => {
    if (val) {
      // 选中所有页时，先全选当前页
      tableRef.value.toggleAllSelection();
    } else {
      // 取消选中所有页时，清空当前选择
      tableRef.value.clearSelection();
    }
    emit('select-all-change', val);
  };

  // 期刊多选
  const handleSelectionChange = selection => {
    selected.value = selection;
    if (!selection || selection.length < props.journalList.length) {
      selectAllPage.value = false;
    }
    emit('selection-change', selection);
  };

  // 处理表格全选/取消全选事件
  const handleSelectAll = selection => {
    // 如果取消全选，则将selectAllPage设置为false
    if (selection.length === 0) {
      selectAllPage.value = false;
    }
    // 注意：这里不处理全选时的情况，因为全选只是选中了当前页
  };

  // 加载数据
  const loadData = () => {
    emit('search');
  };

  // 暴露方法给父组件
  defineExpose({
    tableRef,
    selectAllPage,
    selected,
    resetSelection: () => {
      tableRef.value?.clearSelection();
      selectAllPage.value = false;
    },
  });
</script>

<style lang="scss" scoped>
  .search-container {
    margin-bottom: 20px;
  }

  .table-footer {
    margin-top: 15px;
    margin-bottom: 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
</style>
