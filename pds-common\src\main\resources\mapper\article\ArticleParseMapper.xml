<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.biosino.lf.pds.article.mapper.ArticleParseMapper">

    <resultMap id="ArticleParseResult" type="org.biosino.lf.pds.article.domain.ArticleParse">
        <id property="id" column="id"/>
        <result property="fileName" column="file_name"/>
        <result property="localPath" column="local_path"/>
        <result property="sourceId" column="source_id"/>
        <result property="source" column="source"/>
        <result property="status" column="status"/>
        <result property="fileMd5" column="file_md5"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="errorMsg" column="error_msg"/>
        <result property="version" column="version"/>
    </resultMap>

    <select id="selectParseList" parameterType="org.biosino.lf.pds.article.dto.ArticleParseDTO" resultMap="ArticleParseResult">
        select s.id, s.doc_id, s.file_name, s.local_path, s.source_id, s.source, s.status,
               s.file_md5, s.create_time, s.update_time, s.error_msg,s.content_type,s.version
        from tb_dds_article_parse s
        <where>
            <if test="fileName != null and fileName != ''">
                AND s.file_name like concat('%', #{fileName}, '%')
            </if>
            <if test="sourceId != null and sourceId != ''">
                AND s.source_id = #{sourceId}
            </if>
            <if test="source != null and source != ''">
                AND s.source = #{source}
            </if>
            <!-- 只判断 null，避免字符串比较 -->
            <if test="status != null">
                AND s.status = #{status}
            </if>
            <if test="errorMsg != null and errorMsg != ''">
                AND s.error_msg like concat('%', #{errorMsg}, '%')
            </if>
            <if test="params != null and params.beginTime != null and params.beginTime != ''">
                AND s.update_time::date &gt;= to_date(#{params.beginTime}, 'yyyy-MM-dd')
            </if>
            <if test="params != null and params.endTime != null and params.endTime != ''">
                AND s.update_time::date &lt;= to_date(#{params.endTime}, 'yyyy-MM-dd')
            </if>
        </where>
        order by s.update_time DESC
    </select>

</mapper>
