<template>
  <div class="forgot-password-page">
    <!-- 装饰性几何图形 -->
    <div class="decoration-shapes">
      <div class="shape shape-1"></div>
      <div class="shape shape-2"></div>
      <div class="shape shape-3"></div>
    </div>

    <div class="forgot-password-container">
      <div class="forgot-password-card">
        <div class="forgot-password-header">
          <h1 class="forgot-password-title">忘记密码</h1>
        </div>

        <el-form
          ref="forgotPasswordFormRef"
          :model="forgotPasswordForm"
          :rules="forgotPasswordRules"
          class="forgot-password-form"
          label-position="top"
          size="large"
        >
          <el-form-item
            prop="email"
            class="form-group"
            required
            label="邮箱地址"
          >
            <div class="email-input-group">
              <el-input
                v-model="forgotPasswordForm.email"
                placeholder="请输入您的邮箱地址"
                class="form-input"
                style="width: 250px"
              />
              <el-button
                type="primary"
                class="send-code-btn"
                @click="handleSendCode"
                :loading="sendingCode"
                :disabled="!isEmailValid || countdown > 0"
              >
                {{ countdown > 0 ? `${countdown}s后重发` : '发送验证码' }}
              </el-button>
            </div>
          </el-form-item>

          <el-form-item
            prop="verificationCode"
            class="form-group"
            required
            label="邮箱验证码"
          >
            <el-input
              v-model="forgotPasswordForm.verificationCode"
              placeholder="请输入6位验证码"
              class="form-input"
              maxlength="6"
            />
          </el-form-item>
          <el-form-item
              prop="verificationCode"
              class="form-group"
              required
              label="新密码"
          >
            <el-input
                v-model="forgotPasswordForm.password"
                placeholder="请输入新密码"
                class="form-input"
            />
          </el-form-item>

          <el-button
            type="primary"
            size="large"
            class="confirm-button"
            @click="handleConfirm"
            :loading="loading"
          >
            确认
          </el-button>

          <div class="back-to-login">
            <router-link to="/login" class="link">返回登录</router-link>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'

const router = useRouter()

// 表单引用
const forgotPasswordFormRef = ref()

// 表单数据
const forgotPasswordForm = ref({
  email: '',
  verificationCode: '',
  password:''
})

// 状态管理
const loading = ref(false)
const sendingCode = ref(false)
const countdown = ref(0)
let countdownTimer = null

// 邮箱格式验证
const isEmailValid = computed(() => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(forgotPasswordForm.value.email)
})

// 表单验证规则
const forgotPasswordRules = {
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  verificationCode: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { len: 6, message: '验证码必须为6位数字', trigger: 'blur' },
    { pattern: /^\d{6}$/, message: '验证码必须为6位数字', trigger: 'blur' }
  ]
}

// 发送验证码
const handleSendCode = () => {
  if (!isEmailValid.value) {
    ElMessage.warning('请输入正确的邮箱地址')
    return
  }

  sendingCode.value = true

  // 模拟发送验证码请求
  setTimeout(() => {
    sendingCode.value = false
    ElMessage.success('验证码已发送到您的邮箱，请查收')
    
    // 开始倒计时
    startCountdown()
  }, 1000)
}

// 开始倒计时
const startCountdown = () => {
  countdown.value = 60
  countdownTimer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(countdownTimer)
      countdownTimer = null
    }
  }, 1000)
}

// 确认处理
const handleConfirm = () => {
  forgotPasswordFormRef.value.validate((valid) => {
    if (valid) {
      loading.value = true

      // 模拟验证请求
      setTimeout(() => {
        loading.value = false
        ElMessage.success('验证成功！请设置新密码')

        // 暂时跳转回登录页面
        router.push('/login')
      }, 1500)
    } else {
      ElMessage.warning('请检查表单输入')
    }
  })
}

// 组件销毁时清理定时器
import { onUnmounted } from 'vue'
onUnmounted(() => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
  }
})
</script>

<style lang="scss" scoped>
@import "@/assets/styles/variables";

.forgot-password-page {
  min-height: 80vh;
  background: linear-gradient(135deg, rgba($primary-color, 0.05) 0%, rgba($secondary-color, 0.05) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-lg;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
      radial-gradient(circle at 20% 80%, rgba($secondary-color, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba($primary-color, 0.08) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba($light-blue, 0.05) 0%, transparent 50%);
    z-index: 0;
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 60 60"><defs><pattern id="dots" width="60" height="60" patternUnits="userSpaceOnUse"><circle cx="30" cy="30" r="1.5" fill="rgba(4,56,115,0.1)"/><circle cx="10" cy="10" r="1" fill="rgba(79,156,249,0.08)"/><circle cx="50" cy="50" r="1" fill="rgba(79,156,249,0.08)"/></pattern></defs><rect width="100%" height="100%" fill="url(%23dots)"/></svg>');
    z-index: 0;
  }
}

.forgot-password-container {
  width: 100%;
  max-width: 480px;
}

.forgot-password-card {
  background: $white;
  border-radius: $border-radius-xxl;
  box-shadow: $box-shadow;
  padding: $spacing-xxxl;
  position: relative;
  z-index: 1;
  border: 1px solid rgba($primary-color, 0.08);

  @media (max-width: $breakpoint-md) {
    padding: $spacing-xl;
  }
}

.forgot-password-header {
  text-align: center;
  margin-bottom: $spacing-xxxl;

  .forgot-password-title {
    font-size: $font-size-xxxxxlarge;
    font-weight: $font-weight-bold;
    color: $primary-color;
    margin: 0 0 $spacing-sm 0;
    background: linear-gradient(135deg, $primary-color, lighten($primary-color, 20%));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .forgot-password-subtitle {
    font-size: $font-size-medium;
    color: $gray;
    margin: 0;
    line-height: 1.5;
  }
}

.forgot-password-form {
  :deep(.el-form-item) {
    margin-bottom: $spacing-lg;

    .el-form-item__label {
      font-size: 16px;
      color: #374151;
      font-weight: 500;
      padding: 0;
      margin-bottom: $spacing-xs;
      line-height: 1.4;
    }

    .el-form-item__content {
      line-height: 1.4;
    }

    .el-form-item__error {
      font-size: 12px;
      color: #ff4757;
      padding-top: 4px;
    }
  }

  .form-group {
    .form-input {
      :deep(.el-input__wrapper) {
        border-radius: $border-radius-md;
        box-shadow: none;
        border: 1px solid #e1e5e9;

        &:hover {
          border-color: $primary-color;
        }

        &.is-focus {
          border-color: $primary-color;
          box-shadow: 0 0 0 2px rgba($primary-color, 0.1);
        }
      }
    }
  }

  .email-input-group {
    display: flex;
    gap: $spacing-sm;

    .form-input {
      flex: 1;
    }

    .send-code-btn {
      flex-shrink: 0;
      width: 120px;
      border-radius: $border-radius-md;
      font-size: $font-size-small;

      &:disabled {
        background-color: #f5f5f5;
        border-color: #d9d9d9;
        color: #999;
      }
    }

    @media (max-width: $breakpoint-md) {
      flex-direction: column;
      gap: $spacing-xs;

      .send-code-btn {
        width: 100%;
      }
    }
  }

  .confirm-button {
    width: 100%;
    height: 48px;
    background-color: $primary-color;
    border-color: $primary-color;
    border-radius: $border-radius-md;
    font-size: $font-size-medium;
    font-weight: $font-weight-medium;
    margin-top: $spacing-lg;

    &:hover {
      background-color: darken($primary-color, 10%);
      border-color: darken($primary-color, 10%);
    }
  }

  .back-to-login {
    text-align: center;
    margin-top: $spacing-lg;
    color: $gray;
    font-size: $font-size-small;

    .link {
      color: $primary-color;
      text-decoration: none;
      font-weight: $font-weight-medium;

      &:hover {
        text-decoration: underline;
      }
    }
  }
}

// 装饰性几何图形
.decoration-shapes {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 0;

  .shape {
    position: absolute;
    border-radius: 50%;
    opacity: 0.6;
    animation: float 6s ease-in-out infinite;

    &.shape-1 {
      width: 90px;
      height: 90px;
      background: linear-gradient(135deg, rgba($secondary-color, 0.2), rgba($light-blue, 0.1));
      top: 10%;
      left: 10%;
      animation-delay: 0s;
    }

    &.shape-2 {
      width: 80px;
      height: 80px;
      background: linear-gradient(135deg, rgba($primary-color, 0.15), rgba($secondary-color, 0.1));
      top: 70%;
      right: 15%;
      animation-delay: 2s;
    }

    &.shape-3 {
      width: 60px;
      height: 60px;
      background: linear-gradient(135deg, rgba($light-blue, 0.2), rgba($secondary-color, 0.1));
      top: 30%;
      right: 25%;
      animation-delay: 4s;
    }
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}
</style>