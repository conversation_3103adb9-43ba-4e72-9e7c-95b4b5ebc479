package org.biosino.lf.pds.article.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.lf.pds.article.domain.Publisher;
import org.biosino.lf.pds.article.dto.PublisherMergeDTO;
import org.biosino.lf.pds.article.dto.PublisherValidationResult;
import org.biosino.lf.pds.article.mapper.PublisherMapper;
import org.biosino.lf.pds.article.service.PublisherValidationService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 出版社验证服务实现类
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class PublisherValidationServiceImpl implements PublisherValidationService {

    private final PublisherMapper publisherMapper;

    @Override
    public PublisherValidationResult validatePublisherUpdate(Publisher publisher) {
        List<Long> excludeIds = new ArrayList<>();
        if (publisher.getId() != null) {
            excludeIds.add(publisher.getId());
        }

        // 验证出版社名称
        if (StrUtil.isNotBlank(publisher.getName())) {
            PublisherValidationResult result = validateSingleValue("name", 
                publisher.getName(), excludeIds);
            if (!result.isValid()) {
                return result;
            }
        }

        // 验证别名
        if (CollUtil.isNotEmpty(publisher.getAlias())) {
            for (String alias : publisher.getAlias()) {
                if (StrUtil.isNotBlank(alias)) {
                    PublisherValidationResult result = validateSingleValue("alias", 
                        alias, excludeIds);
                    if (!result.isValid()) {
                        return result;
                    }
                }
            }
        }

        return PublisherValidationResult.valid();
    }

    @Override
    public PublisherValidationResult validatePublisherMerge(PublisherMergeDTO publisherMergeDTO) {
        List<Long> excludeIds = new ArrayList<>();
        
        // 排除目标出版社和源出版社
        if (publisherMergeDTO.getTargetId() != null) {
            excludeIds.add(publisherMergeDTO.getTargetId());
        }
        if (CollUtil.isNotEmpty(publisherMergeDTO.getSourceIds())) {
            excludeIds.addAll(publisherMergeDTO.getSourceIds());
        }

        // 验证出版社名称
        if (StrUtil.isNotBlank(publisherMergeDTO.getName())) {
            PublisherValidationResult result = validateSingleValue("name", 
                publisherMergeDTO.getName(), excludeIds);
            if (!result.isValid()) {
                return result;
            }
        }

        // 验证别名
        if (CollUtil.isNotEmpty(publisherMergeDTO.getAlias())) {
            for (String alias : publisherMergeDTO.getAlias()) {
                if (StrUtil.isNotBlank(alias)) {
                    PublisherValidationResult result = validateSingleValue("alias", 
                        alias, excludeIds);
                    if (!result.isValid()) {
                        return result;
                    }
                }
            }
        }

        return PublisherValidationResult.valid();
    }

    @Override
    public PublisherValidationResult validatePublisherSave(Publisher publisher, List<Long> excludeIds) {
        if (excludeIds == null) {
            excludeIds = new ArrayList<>();
        }

        // 验证出版社名称
        if (StrUtil.isNotBlank(publisher.getName())) {
            PublisherValidationResult result = validateSingleValue("name", 
                publisher.getName(), excludeIds);
            if (!result.isValid()) {
                return result;
            }
        }

        // 验证别名
        if (CollUtil.isNotEmpty(publisher.getAlias())) {
            for (String alias : publisher.getAlias()) {
                if (StrUtil.isNotBlank(alias)) {
                    PublisherValidationResult result = validateSingleValue("alias", 
                        alias, excludeIds);
                    if (!result.isValid()) {
                        return result;
                    }
                }
            }
        }

        return PublisherValidationResult.valid();
    }

    @Override
    public PublisherValidationResult validateSingleValue(String fieldName, String value, List<Long> excludeIds) {
        if (StrUtil.isBlank(value)) {
            return PublisherValidationResult.valid();
        }

        List<Publisher> conflictPublishers = null;
        
        // 根据字段名称选择相应的查询方法
        switch (fieldName) {
            case "name":
                conflictPublishers = publisherMapper.findConflictingPublishersByName(value, excludeIds);
                break;
            case "alias":
                conflictPublishers = publisherMapper.findConflictingPublishersByAlias(value, excludeIds);
                break;
            default:
                return PublisherValidationResult.valid();
        }

        if (CollUtil.isNotEmpty(conflictPublishers)) {
            Publisher conflictPublisher = conflictPublishers.get(0);
            String errorMessage = String.format("字段 '%s' 的值 '%s' 已存在于出版社记录中 (ID: %d, 名称: %s)", 
                getFieldDisplayName(fieldName), value, conflictPublisher.getId(), conflictPublisher.getName());
            
            return PublisherValidationResult.invalid(fieldName, value, conflictPublisher, errorMessage);
        }

        return PublisherValidationResult.valid();
    }

    /**
     * 获取字段的显示名称
     */
    private String getFieldDisplayName(String fieldName) {
        switch (fieldName) {
            case "name":
                return "出版社名称";
            case "alias":
                return "别名";
            default:
                return fieldName;
        }
    }
}
