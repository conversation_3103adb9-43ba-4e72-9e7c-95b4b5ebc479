<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration
        PUBLIC "-//mybatis.org//DTD Config 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
    <settings>
        <!-- 使用jdbc的getGeneratedKeys获取数据库自增主键值 -->
        <setting name="useGeneratedKeys" value="true" />
        
        <!-- 使用列别名替换列名 默认:true -->
        <setting name="useColumnLabel" value="true" />
        
        <!-- 开启驼峰命名转换 -->
        <setting name="mapUnderscoreToCamelCase" value="true" />
        
        <!-- 二级缓存开关 -->
        <setting name="cacheEnabled" value="true" />
        
        <!-- 延迟加载开关 -->
        <setting name="lazyLoadingEnabled" value="true" />
        
        <!-- 积极延迟加载开关 -->
        <setting name="aggressiveLazyLoading" value="false" />
        
        <!-- 是否允许单一语句返回多结果集 -->
        <setting name="multipleResultSetsEnabled" value="true" />
        
        <!-- 使用列标签代替列名 -->
        <setting name="useColumnLabel" value="true" />
        
        <!-- 允许JDBC支持自动生成主键 -->
        <setting name="useGeneratedKeys" value="true" />
        
        <!-- 指定MyBatis应如何自动映射列到字段或属性 -->
        <setting name="autoMappingBehavior" value="PARTIAL" />
        
        <!-- 指定发现自动映射目标未知列的行为 -->
        <setting name="autoMappingUnknownColumnBehavior" value="WARNING" />
        
        <!-- 配置默认的执行器 -->
        <setting name="defaultExecutorType" value="SIMPLE" />
        
        <!-- 指定语句超时时间 -->
        <setting name="defaultStatementTimeout" value="25" />
        
        <!-- 指定为任意嵌套结果集的默认值 -->
        <setting name="defaultFetchSize" value="100" />
        
        <!-- 允许在嵌套语句中使用分页 -->
        <setting name="safeRowBoundsEnabled" value="false" />
        
        <!-- 是否开启自动驼峰命名规则映射 -->
        <setting name="mapUnderscoreToCamelCase" value="true" />
        
        <!-- MyBatis利用本地缓存机制防止循环引用和加速重复嵌套查询 -->
        <setting name="localCacheScope" value="SESSION" />
        
        <!-- 当没有为参数提供特定的JDBC类型时，为空值指定JDBC类型 -->
        <setting name="jdbcTypeForNull" value="OTHER" />
        
        <!-- 指定哪个对象的方法触发一次延迟加载 -->
        <setting name="lazyLoadTriggerMethods" value="equals,clone,hashCode,toString" />
    </settings>
</configuration>
