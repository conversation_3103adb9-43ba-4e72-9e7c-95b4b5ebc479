<template>
  <el-dialog
      v-model="visible"
      title="添加到收藏夹"
      :width="isMobile ? '90%' : '480px'"
      :top="isMobile ? '10vh' : '15vh'"
      class="collection-modal"
      @close="handleClose"
      center
  >
    <div class="collection-content">
      <!-- 收藏夹列表 -->
      <div class="folders-list">
        <div
            v-for="folder in folders"
            :key="folder.id"
            :class="['folder-item', { selected: selectedFolderId === folder.id }]"
            @click="selectFolder(folder.id)"
        >
          <el-checkbox
              :model-value="selectedFolderId === folder.id"
              @change="selectFolder(folder.id)"
              size="large"
              :disabled="showAddFolder"
          >
            <template #default>
              <div class="folder-info">
                <span class="folder-name">{{ folder.name }}</span>
                <span class="folder-count">{{ folder.count }}/{{ folder.limit || 1000 }}</span>
              </div>
            </template>
          </el-checkbox>
        </div>
      </div>

      <!-- 新建收藏夹 -->
      <div class="add-folder-section">
        <el-button
            class="add-folder-btn"
            @click="showAddFolder = !showAddFolder"
            text
        >
          <el-icon><Plus /></el-icon>
          新建收藏夹
        </el-button>

        <div v-if="showAddFolder" class="add-folder-form">
          <el-input
              v-model="newFolderName"
              placeholder="请输入收藏夹名称"
              maxlength="20"
              show-word-limit
          />
          <el-button type="primary" class="confirm-folder-btn">确定</el-button>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button
            type="primary"
            @click="handleConfirm"
            class="confirm-btn"
            :disabled="!selectedFolderId"
        >
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  article: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:modelValue', 'confirm'])

// 响应式数据
const visible = computed({
  get: () => {
    return props.modelValue
  },
  set: (value) => {
    emit('update:modelValue', value)
  }
})

const selectedFolderId = ref('')
const showAddFolder = ref(false)
const newFolderName = ref('')

// 移动端检测
const windowWidth = ref(window.innerWidth)
const isMobile = computed(() => windowWidth.value <= 768)

// 收藏夹数据 (实际项目中应该从API获取)
const folders = ref([
  {
    id: 'default',
    name: '默认收藏夹',
    count: 17,
    limit: 1000
  },
  {
    id: 'ui-design',
    name: 'xxxx文件',
    count: 4,
    limit: 1000
  },
  {
    id: 'research',
    name: '综合',
    count: 1,
    limit: 1000
  }
])

// 方法
const selectFolder = (folderId) => {
  selectedFolderId.value = folderId
}

const handleClose = () => {
  visible.value = false
  selectedFolderId.value = ''
  showAddFolder.value = false
  newFolderName.value = ''
}

const handleConfirm = () => {
  handleClose()
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/variables";

.collection-modal {
  :deep(.el-dialog__header) {
    padding: $spacing-lg $spacing-lg $spacing-md;
    border-bottom: 1px solid #F3F4F6;

    .el-dialog__title {
      font-size: $font-size-large;
      font-weight: $font-weight-bold;
      color: $primary-color;
    }
  }

  :deep(.el-dialog__body) {
    padding: $spacing-lg;
  }

  :deep(.el-dialog__footer) {
    padding: $spacing-md $spacing-lg $spacing-lg;
    border-top: 1px solid #F3F4F6;
  }
}

.collection-content {
  min-height: 200px;
}

.folders-list {
  margin-bottom: $spacing-lg;
}

.folder-item {
  padding: 4px $spacing-md;
  border-radius: $border-radius-md;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: $spacing-xs;

  &:hover {
    background-color: #F9FAFB;
  }

  &.selected {
    background-color: rgba($primary-color, 0.05);
    border: 1px solid rgba($primary-color, 0.2);
  }

  :deep(.el-checkbox) {
    width: 100%;

    .el-checkbox__label {
      width: 100%;
      padding-left: 0;
    }
  }
}

.folder-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-left: 10px;
}

.folder-name {
  font-size: $font-size-medium;
  color: $primary-color;
  font-weight: $font-weight-medium;
}

.folder-count {
  font-size: $font-size-small;
  color: $gray;
  background-color: #F3F4F6;
  padding: 2px 8px;
  border-radius: 12px;
}

.add-folder-section {
  display: flex;
  flex-direction: column;
  border-top: 1px solid #F3F4F6;
  padding-top: $spacing-md;
}

.add-folder-btn {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  color: $primary-color;
  font-size: $font-size-small;
  padding: 0;

  &:hover {
    background-color: transparent;
  }
}

.add-folder-form {
  display: flex;
  margin-top: $spacing-md;
  padding: $spacing-md;
  background-color: #F9FAFB;
  border-radius: $border-radius-md;
  :deep(.el-input__wrapper){
    border-radius: 4px 0 0 4px !important;
    height: 40px;
  }
  .confirm-folder-btn{
    border-radius: 0 04px 4px 0 !important;
    height: 40px;
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: $spacing-sm;
    margin-top: $spacing-sm;
  }
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: $spacing-md;
  margin-top: 10px;

  .cancel-btn {
    color: $gray;
    border-color: #E5E7EB;
  }

  .confirm-btn {
    background-color: #9CA3AF;
    border-color: #9CA3AF;

    &:not(:disabled) {
      background-color: $primary-color;
      border-color: $primary-color;
    }
  }
}

@media (max-width: $breakpoint-md) {
  .dialog-footer {
    flex-direction: column-reverse;
    gap: $spacing-sm;

    .cancel-btn,
    .confirm-btn {
      width: 100%;
    }
  }
}
</style>
