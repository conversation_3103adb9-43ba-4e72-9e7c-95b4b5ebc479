package org.biosino.lf.pds.task.service;

import org.biosino.lf.pds.article.domain.ArticleParse;
import org.biosino.lf.pds.article.dto.ArticleParseDTO;
import org.biosino.lf.pds.article.service.CommonService;
import org.springframework.core.io.Resource;

import java.util.List;

/**
 * 文献解析服务接口
 *
 * <AUTHOR>
 */
public interface ITbDdsArticleParseService extends CommonService<ArticleParse> {

    /**
     * 查询文献解析列表
     */
    List<ArticleParse> selectParseList(ArticleParseDTO articleParseDTO);


    /**
     * 下载文献
     */
    Resource downloadFile(Long id);

    /**
     * 删除文献解析和文献
     */
    void deleteParses(Long[] id);


    /**
     * 修改文献解析
     */
    void retryParse(Long[] id);
}
