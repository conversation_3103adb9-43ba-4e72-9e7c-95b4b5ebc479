package org.biosino.lf.pds.common.constant;

/**
 * 缓存的key 常量
 *
 * <AUTHOR>
 */
public class CacheConstants {
    /**
     * 登录用户 redis key
     */
    public static final String LOGIN_TOKEN_KEY = "pds_login_tokens:";

    /**
     * 验证码 redis key
     */
    public static final String CAPTCHA_CODE_KEY = "pds_captcha_codes:";

    /**
     * 参数管理 cache key
     */
    public static final String SYS_CONFIG_KEY = "pds_sys_config:";

    /**
     * 字典管理 cache key
     */
    public static final String SYS_DICT_KEY = "pds_sys_dict:";

    /**
     * 防重提交 redis key
     */
    public static final String REPEAT_SUBMIT_KEY = "pds_repeat_submit:";

    /**
     * 限流 redis key
     */
    public static final String RATE_LIMIT_KEY = "pds_rate_limit:";

    /**
     * 登录账户密码错误次数 redis key
     */
    public static final String PWD_ERR_CNT_KEY = "pds_pwd_err_cnt:";

    /**
     * 站点信息缓存键前缀
     */
    public static final String SITE_INFO_KEY = "site_info:";

    /**
     * 任务信息缓存键前缀
     */
    public static final String TASK_INFO_KEY = "task_info:";
}
