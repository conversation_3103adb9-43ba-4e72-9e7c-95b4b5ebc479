package org.biosino.lf.plosp.portal.web.dto.auth;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 注册请求DTO
 */
@Data
@Schema(description = "User registration request")
public class RegisterRequest {

    @Schema(description = "Email address", example = "<EMAIL>")
    @NotBlank(message = "邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    @Size(max = 100, message = "邮箱长度不能超过100个字符")
    private String email;

    @Schema(description = "Password", example = "password123")
    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 100, message = "密码长度必须在6-100个字符之间")
    private String password;

    @Schema(description = "Confirm password", example = "password123")
    @NotBlank(message = "确认密码不能为空")
    private String confirmPassword;

    @Schema(description = "First name", example = "John")
    @NotBlank(message = "名字不能为空")
    @Size(max = 50, message = "名字长度不能超过50个字符")
    private String firstName;

    @Schema(description = "Last name", example = "Doe")
    @NotBlank(message = "姓氏不能为空")
    @Size(max = 50, message = "姓氏长度不能超过50个字符")
    private String lastName;

    @Schema(description = "Organization", example = "University of Science")
    @NotBlank(message = "组织机构不能为空")
    @Size(max = 200, message = "组织机构长度不能超过200个字符")
    private String organization;

    @Schema(description = "Department", example = "Computer Science")
    @Size(max = 100, message = "部门长度不能超过100个字符")
    private String department;

    @Schema(description = "PI name", example = "Dr. Smith")
    @Size(max = 100, message = "PI名称长度不能超过100个字符")
    private String piName;

    @Schema(description = "Title/Position", example = "Graduate Student")
    @Size(max = 50, message = "职称长度不能超过50个字符")
    private String title;

    @Schema(description = "Phone number", example = "******-123-4567")
    @Pattern(regexp = "^$|^[0-9\\+\\-\\(\\)\\s]{5,20}$", message = "电话号码格式不正确")
    @Size(max = 20, message = "电话长度不能超过20个字符")
    private String phone;

    @Schema(description = "Country/Region", example = "United States")
    @NotBlank(message = "国家/地区不能为空")
    @Size(max = 50, message = "国家/地区长度不能超过50个字符")
    private String countryRegion;

    @Schema(description = "State/Province", example = "California")
    @Size(max = 50, message = "州/省长度不能超过50个字符")
    private String stateProvince;

    @Schema(description = "City", example = "San Francisco")
    @Size(max = 50, message = "城市长度不能超过50个字符")
    private String city;

    @Schema(description = "User type", example = "researcher")
    @NotBlank(message = "用户类型不能为空")
    private String userType;

    @Schema(description = "Email verification code", example = "123456")
    @NotBlank(message = "验证码不能为空")
    private String verificationCode;
}
