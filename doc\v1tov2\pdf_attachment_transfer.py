#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF附件迁移脚本：从v1 MySQL数据库迁移PDF附件到v2 PostgreSQL数据库
迁移表：tb_dds_article_attachment (type='PDF') -> tb_dds_file
迁移范围：pmid < ************ OR pmid > ************
"""

import logging
import os
import psycopg2.extras
import sys
import traceback
from datetime import datetime
from toollib.guid import SnowFlake

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('pdf_migration.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# 数据库配置
V1_MYSQL_CONFIG = {
    'host': '************',  # TODO: 修改为实际的MySQL主机地址
    'port': 32526,
    'user': 'root',  # TODO: 修改为实际的MySQL用户名
    'password': 'Lfgzs@2021',  # TODO: 修改为实际的MySQL密码
    'database': 'plosp_online',
    'charset': 'utf8mb4'
}

V2_PGSQL_CONFIG = {
    'host': '************',  # TODO: 修改为实际的PostgreSQL主机地址
    'port': 31909,
    'user': 'postgres',  # TODO: 修改为实际的PostgreSQL用户名
    'password': 'Biosino+2025',  # TODO: 修改为实际的PostgreSQL密码
    'database': 'plosp',  # TODO: 修改为实际的PostgreSQL数据库名
    'options': '-c search_path=public'  # TODO: 修改为实际的schema名称，如果不是public的话
}

# 迁移范围配置
PMID_EXCLUDE_MIN = ************
PMID_EXCLUDE_MAX = ************

# 雪花ID生成器
snow = SnowFlake(worker_id=1, datacenter_id=1)


def get_mysql_connection():
    """获取MySQL数据库连接"""
    try:
        connection = pymysql.connect(**V1_MYSQL_CONFIG)
        logger.info("MySQL数据库连接成功")
        return connection
    except Exception as e:
        logger.error(f"MySQL数据库连接失败: {e}")
        raise


def get_pgsql_connection():
    """获取PostgreSQL数据库连接"""
    try:
        connection = psycopg2.connect(**V2_PGSQL_CONFIG)
        logger.info("PostgreSQL数据库连接成功")
        return connection
    except Exception as e:
        logger.error(f"PostgreSQL数据库连接失败: {e}")
        raise


def check_migration_scope():
    """检查迁移范围内的PDF附件数量"""
    mysql_conn = None
    try:
        mysql_conn = get_mysql_connection()
        cursor = mysql_conn.cursor()

        # 检查PDF附件数量
        attachment_query = """
        SELECT count(1) FROM `tb_dds_article_attachment`
        WHERE (`pmid` < %s OR `pmid` > %s) AND type = 'PDF'
        """
        cursor.execute(attachment_query, (PMID_EXCLUDE_MIN, PMID_EXCLUDE_MAX))
        attachment_count = cursor.fetchone()[0]
        logger.info(f"待迁移PDF附件数量: {attachment_count}")

        return attachment_count

    except Exception as e:
        logger.error(f"检查迁移范围失败: {e}")
        raise
    finally:
        if mysql_conn:
            mysql_conn.close()


def get_article_id_by_pmid(pmid_value):
    """根据pmid获取v2数据库中对应的文章ID

    Args:
        pmid_value: v1数据库中的pmid值

    Returns:
        对应的v2文章ID，如果未找到返回None
    """
    pgsql_conn = None
    try:
        pgsql_conn = get_pgsql_connection()
        cursor = pgsql_conn.cursor()

        # 判断pmid是否以90000开头
        if str(pmid_value).startswith('90000'):
            # 如果是90000开头，去pmc_id字段查找
            query = "SELECT id FROM tb_dds_article WHERE pmc_id = %s"
            cursor.execute(query, (pmid_value,))
        else:
            # 否则去pmid字段查找
            query = "SELECT id FROM tb_dds_article WHERE pmid = %s"
            cursor.execute(query, (pmid_value,))

        result = cursor.fetchone()
        if result:
            return result[0]
        else:
            return None

    except Exception as e:
        logger.error(f"查询文章ID失败 pmid={pmid_value}: {e}")
        return None
    finally:
        if pgsql_conn:
            pgsql_conn.close()


def calculate_file_size(file_path):
    """计算文件大小"""
    try:
        if file_path and os.path.exists(file_path):
            return os.path.getsize(file_path)
        else:
            logger.warning(f"文件不存在: {file_path}")
            return None
    except Exception as e:
        logger.warning(f"计算文件大小失败 {file_path}: {e}")
        return None


def migrate_pdf_attachments():
    """迁移PDF附件数据"""
    mysql_conn = None
    pgsql_conn = None
    migrated_count = 0
    failed_count = 0

    try:
        mysql_conn = get_mysql_connection()
        mysql_cursor = mysql_conn.cursor(pymysql.cursors.DictCursor)

        pgsql_conn = get_pgsql_connection()
        pgsql_cursor = pgsql_conn.cursor()

        # 查询v1数据库中的PDF附件数据
        select_query = """
        SELECT id, pmid, type, origin_name, local_path, file_suffix, source,
               description, create_userid, create_siteid, create_time, file_md5
        FROM `tb_dds_article_attachment`
        WHERE (`pmid` < %s OR `pmid` > %s) AND type = 'PDF'
        ORDER BY pmid, create_time
        """

        logger.info("开始查询v1数据库中的PDF附件数据...")
        mysql_cursor.execute(select_query, (PMID_EXCLUDE_MIN, PMID_EXCLUDE_MAX))

        # 准备v2数据库的插入语句
        insert_query = """
        INSERT INTO tb_dds_file (
            id, file_name, content_type, file_size, md5, file_path,
            create_time, type, doc_id, source
        ) VALUES (
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
        )
        """

        logger.info("开始迁移PDF附件数据...")
        batch_size = 100
        batch_data = []

        for row in mysql_cursor:
            try:
                # 根据pmid查找对应的文章ID
                v1_pmid = row['pmid']
                doc_id = get_article_id_by_pmid(v1_pmid)

                if doc_id is None:
                    logger.warning(f"PDF附件对应的文章未找到，跳过 pmid={v1_pmid}, attachment_id={row['id']}")
                    failed_count += 1
                    continue

                # 生成雪花ID作为v2的主键
                new_id = snow.gen_uid()

                # 字段映射和转换
                file_name = row['origin_name']
                content_type = row['file_suffix']  # v1的file_suffix对应v2的content_type

                # 计算文件大小
                # file_size = calculate_file_size(row['local_path'])
                file_size = None
                md5 = row['file_md5']
                file_path = row['local_path']
                create_time = row['create_time']
                file_type = 'PDF'  # 固定为PDF类型
                source = row['source']

                # 准备插入数据
                insert_data = (
                    new_id, file_name, content_type, file_size, md5, file_path,
                    create_time, file_type, doc_id, source
                )

                batch_data.append(insert_data)

                # 实际执行批量插入
                if len(batch_data) >= batch_size:
                    pgsql_cursor.executemany(insert_query, batch_data)
                    pgsql_conn.commit()
                    migrated_count += len(batch_data)
                    logger.info(f"已迁移PDF附件数量: {migrated_count}")
                    batch_data = []

            except Exception as e:
                logger.error(f"迁移PDF附件失败 pmid={row['pmid']}, attachment_id={row['id']}: {e}")
                failed_count += 1
                continue

        # 处理剩余的批次数据
        if batch_data:
            pgsql_cursor.executemany(insert_query, batch_data)
            pgsql_conn.commit()
            migrated_count += len(batch_data)

        logger.info(f"PDF附件迁移完成! 成功: {migrated_count}, 失败: {failed_count}")
        return migrated_count, failed_count

    except Exception as e:
        logger.error(f"PDF附件迁移过程中发生错误: {e}")
        logger.error(traceback.format_exc())
        if pgsql_conn:
            pgsql_conn.rollback()
        raise
    finally:
        if mysql_conn:
            mysql_conn.close()
        if pgsql_conn:
            pgsql_conn.close()


def main():
    """主函数：执行PDF附件迁移流程"""
    start_time = datetime.now()
    logger.info("=" * 60)
    logger.info("开始执行PDF附件迁移任务")
    logger.info(f"迁移范围: pmid < {PMID_EXCLUDE_MIN} OR pmid > {PMID_EXCLUDE_MAX}")
    logger.info(f"开始时间: {start_time}")
    logger.info("=" * 60)

    try:
        # 步骤1: 检查迁移范围
        logger.info("步骤1: 检查迁移范围内的PDF附件数量...")
        attachment_count = check_migration_scope()

        if attachment_count == 0:
            logger.warning("没有找到需要迁移的PDF附件数据，退出程序")
            return

        # 确认是否继续
        logger.info(f"即将迁移 {attachment_count} 个PDF附件")

        # 步骤2: 迁移PDF附件数据
        logger.info("步骤2: 开始迁移PDF附件数据...")
        attachment_migrated, attachment_failed = migrate_pdf_attachments()

        # 步骤3: 输出迁移结果
        end_time = datetime.now()
        duration = end_time - start_time

        logger.info("=" * 60)
        logger.info("PDF附件迁移任务完成!")
        logger.info(f"结束时间: {end_time}")
        logger.info(f"总耗时: {duration}")
        logger.info("迁移结果统计:")
        logger.info(f"  PDF附件: 成功 {attachment_migrated}, 失败 {attachment_failed}")
        logger.info("=" * 60)

        # 如果有失败的记录，提醒检查日志
        if attachment_failed > 0:
            logger.warning("存在迁移失败的记录，请检查日志文件 pdf_migration.log 获取详细信息")

    except Exception as e:
        logger.error(f"PDF附件迁移任务执行失败: {e}")
        logger.error(traceback.format_exc())
        sys.exit(1)


def verify_migration():
    """验证PDF附件迁移结果"""
    logger.info("开始验证PDF附件迁移结果...")

    mysql_conn = None
    pgsql_conn = None

    try:
        mysql_conn = get_mysql_connection()
        pgsql_conn = get_pgsql_connection()

        mysql_cursor = mysql_conn.cursor()
        pgsql_cursor = pgsql_conn.cursor()

        # 验证PDF附件数量
        mysql_cursor.execute(
            "SELECT count(1) FROM `tb_dds_article_attachment` WHERE (`pmid` < %s OR `pmid` > %s) AND type = 'PDF'",
            (PMID_EXCLUDE_MIN, PMID_EXCLUDE_MAX)
        )
        v1_attachment_count = mysql_cursor.fetchone()[0]

        # 统计v2中迁移的PDF附件数量（通过pmid和pmc_id字段关联）
        pgsql_cursor.execute(
            """SELECT count(1) FROM tb_dds_file f
               JOIN tb_dds_article a ON f.doc_id = a.id
               WHERE f.type = 'PDF' AND (
                   (a.pmid IS NOT NULL AND (a.pmid < %s OR a.pmid > %s)) OR
                   (a.pmc_id IS NOT NULL AND (a.pmc_id < %s OR a.pmc_id > %s))
               )""",
            (PMID_EXCLUDE_MIN, PMID_EXCLUDE_MAX, PMID_EXCLUDE_MIN, PMID_EXCLUDE_MAX)
        )
        v2_attachment_count = pgsql_cursor.fetchone()[0]

        logger.info("PDF附件迁移结果验证:")
        logger.info(f"  v1 PDF附件数量: {v1_attachment_count}")
        logger.info(f"  v2 PDF附件数量: {v2_attachment_count}")

        if v1_attachment_count == v2_attachment_count:
            logger.info("✓ PDF附件迁移验证通过，数据量一致")
        else:
            logger.warning("✗ PDF附件迁移验证失败，数据量不一致，请检查迁移过程")

        # 验证文件大小统计
        pgsql_cursor.execute(
            """SELECT count(1) as total_files,
                      count(file_size) as files_with_size,
                      sum(file_size) as total_size
               FROM tb_dds_file f
               JOIN tb_dds_article a ON f.doc_id = a.id
               WHERE f.type = 'PDF' AND (
                   (a.pmid IS NOT NULL AND (a.pmid < %s OR a.pmid > %s)) OR
                   (a.pmc_id IS NOT NULL AND (a.pmc_id < %s OR a.pmc_id > %s))
               )""",
            (PMID_EXCLUDE_MIN, PMID_EXCLUDE_MAX, PMID_EXCLUDE_MIN, PMID_EXCLUDE_MAX)
        )
        size_stats = pgsql_cursor.fetchone()
        total_files, files_with_size, total_size = size_stats

        logger.info("文件大小统计:")
        logger.info(f"  总文件数: {total_files}")
        logger.info(f"  有大小信息的文件数: {files_with_size}")
        logger.info(f"  总大小: {total_size if total_size else 0} 字节")

    except Exception as e:
        logger.error(f"验证PDF附件迁移结果失败: {e}")
    finally:
        if mysql_conn:
            mysql_conn.close()
        if pgsql_conn:
            pgsql_conn.close()


if __name__ == "__main__":
    # 检查必要的依赖包
    try:
        import pymysql
        import psycopg2
    except ImportError as e:
        logger.error(f"缺少必要的依赖包: {e}")
        logger.error("请安装依赖包: pip install pymysql psycopg2-binary")
        sys.exit(1)

    # 执行迁移
    main()

    # 验证迁移结果
    verify_migration()
