package org.biosino.lf.pds.common.enums.task;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
public enum TaskStatusEnum {

    create("待分配"), assigning("分配中"), assign_error("分配失败"), assigned("分配完成"), complete("完成"), paused("已暂停");

    private final String cnName;

    TaskStatusEnum(String cnName) {
        this.cnName = cnName;
    }

    public static String getCnName(String enName) {
        if (StringUtils.isBlank(enName)) {
            return "";
        }
        for (TaskStatusEnum e : values()) {
            if (enName.equals(e.name())) {
                return e.getCnName();
            }
        }
        return "";
    }

    public static List<String> getEnNames() {
        List<String> list = new ArrayList<String>();
        for (TaskStatusEnum e : values()) {
            list.add(e.name());
        }
        return list;
    }

    public static List<String> getCnNames() {
        List<String> list = new ArrayList<String>();
        for (TaskStatusEnum e : values()) {
            list.add(e.getCnName());
        }
        return list;
    }

    public static String getEnName(String cnName) {
        if (StringUtils.isBlank(cnName)) {
            return "";
        }
        for (TaskStatusEnum e : values()) {
            if (cnName.equals(e.getCnName())) {
                return e.name();
            }
        }
        return "";
    }

    public static Map<String, String> toMap() {
        Map<String, String> map = new LinkedHashMap<>();
        for (TaskStatusEnum e : values()) {
            map.put(e.name(), e.getCnName());
        }
        return map;
    }

}
