<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.biosino.lf.pds.article.mapper.TbDdsSiteMapper">

    <resultMap type="org.biosino.lf.pds.article.custbean.vo.SiteVO" id="SiteVOResult">
        <result property="id" column="id"/>
        <result property="siteName" column="site_name"/>
        <result property="siteAbbr" column="site_abbr"/>
        <result property="siteType" column="site_type"/>
        <result property="status" column="status"/>
        <!--<result property="siteGroup" column="site_group"/>-->
        <result property="obtainTaskInterval" column="obtain_task_interval"/>
        <result property="unit" column="unit"/>
        <result property="address" column="address"/>
        <result property="scriptlabelId" column="scriptlabel_id"/>
        <result property="labelName" column="label_name"/>
        <result property="creator" column="creator"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="taskThreadNum" column="task_thread_num"/>
    </resultMap>

    <sql id="selectTbDdsSiteVo">
        SELECT s.id,
               s.site_name,
               s.site_abbr,
               s.site_type,
               s.status,
               s.site_group,
               s.obtain_task_interval,
               s.unit,
               s.address,
               s.api_token,
               s.scriptlabel_id,
               s.creator,
               s.create_time,
               s.update_time,
               s.task_thread_num,
               sl.name as label_name
        FROM tb_dds_site s
                 LEFT JOIN tb_dds_scriptlabel sl ON s.scriptlabel_id = sl.id
    </sql>

    <select id="selectTbDdsSiteList" parameterType="org.biosino.lf.pds.article.custbean.dto.SiteDTO"
            resultMap="SiteVOResult">
        <include refid="selectTbDdsSiteVo"/>
        <where>
            <if test="id != null">
                AND s.id = #{id}
            </if>
            <if test="siteName != null and siteName != ''">
                AND s.site_name ILIKE CONCAT('%', #{siteName}, '%')
            </if>
            <!--<if test="siteGroup != null and siteGroup != ''">
                AND s.site_group = #{siteGroup}
            </if>-->
            <if test="status != null and status != ''">
                AND s.status = #{status}
            </if>
            <if test="siteType != null and siteType != ''">
                AND s.site_type = #{siteType}
            </if>
        </where>
        ORDER BY s.id
    </select>

    <select id="getSiteVOById" parameterType="java.lang.Integer" resultMap="SiteVOResult">
        <include refid="selectTbDdsSiteVo"/>
        <where>
            s.id = #{id}
        </where>
    </select>


    <!--and s.site_type IN
        <foreach collection="siteTypes" item="itemVal" open="(" separator="," close=")">
            #{itemVal}
        </foreach>-->
    <select id="findReTrySiteIds" resultType="java.lang.Integer">
        select s.id
        from tb_dds_site s
        where s.status = #{status}
        and s.id in (select h.site_id from tb_dds_handshake h where h.signal_date &gt;= CAST(#{aliveHandshakeTime} AS timestamp with time zone))
        and s.id not in (select p.site_id from tb_dds_task_schedule p where p.paper_id = #{paperId})
        and s.scriptlabel_id = #{scriptlabelId}
    </select>

    <select id="searchCanAutoAssignSiteIds" resultType="java.lang.Integer">
        select s.id
        from tb_dds_site s
        where s.status = #{status}
          and s.site_type = #{siteType}
          and s.id in (select h.site_id
                       from tb_dds_handshake h
                       where h.signal_date &gt;= CAST(#{aliveHandshakeTime} AS timestamp with time zone))
    </select>

</mapper> 