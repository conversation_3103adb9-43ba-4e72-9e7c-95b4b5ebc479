import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import App from './App.vue'
import router from './router'
import { useAuthStore } from '@/stores/auth'
import { permissionDirective } from '@/utils/permission'

// 导入全局样式
import './assets/styles/variables.scss'
import './assets/styles/global.scss'
import './assets/styles/index.scss'

const app = createApp(App)
const pinia = createPinia()

app.use(pinia)
app.use(router)
app.use(ElementPlus)

// 注册权限指令
app.directive('permission', permissionDirective)

// 初始化认证状态
const authStore = useAuthStore()
authStore.initAuth()

app.mount('#app')
