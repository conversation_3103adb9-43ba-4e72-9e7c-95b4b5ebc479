package org.biosino.lf.pds.article.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.biosino.lf.pds.article.domain.ArticleGene;

import java.util.List;

/**
 * 文章基因关联表 服务接口
 */
public interface IArticleGeneService extends IService<ArticleGene> {
    /**
     * 根据PMID查询文章基因关联信息
     *
     * @param pmid 文章PMID
     * @return 文章基因关联信息列表
     */
    List<ArticleGene> findByDocId(Long pmid);

    /**
     * 根据文档ID删除文章基因关联信息
     *
     * @param docId 文档ID
     * @return 是否删除成功
     */
    boolean removeByDocId(Long docId);
}
