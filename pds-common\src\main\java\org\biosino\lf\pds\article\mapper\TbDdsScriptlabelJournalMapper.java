package org.biosino.lf.pds.article.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.biosino.lf.pds.article.domain.TbDdsScriptlabelJournal;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
public interface TbDdsScriptlabelJournalMapper extends BaseMapper<TbDdsScriptlabelJournal> {

    /**
     * 批量删除脚本标签期刊关联记录（用于期刊合并）
     * 删除所有匹配指定期刊ID列表的记录
     *
     * @param journalIds 要删除的期刊ID列表
     * @return 删除的记录数
     */
    int deleteByJournalIds(@Param("journalIds") List<Long> journalIds);

}
