package org.biosino.lf.plosp.portal.web.dto.auth;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 用户配置文件响应DTO
 */
@Data
@Schema(description = "User profile response")
public class UserProfileResponse {

    @Schema(description = "User ID")
    private Long userId;

    @Schema(description = "Email address")
    private String email;

    @Schema(description = "First name")
    private String firstName;

    @Schema(description = "Last name")
    private String lastName;

    @Schema(description = "Full name")
    private String userName;

    @Schema(description = "Organization")
    private String organization;

    @Schema(description = "Department")
    private String department;

    @Schema(description = "PI name")
    private String piName;

    @Schema(description = "Title/Position")
    private String title;

    @Schema(description = "Phone number")
    private String phone;

    @Schema(description = "Country/Region")
    private String countryRegion;

    @Schema(description = "State/Province")
    private String stateProvince;

    @Schema(description = "City")
    private String city;

    @Schema(description = "User status (1-active, 0-disabled)")
    private Integer status;

    @Schema(description = "User points")
    private Integer points;

    @Schema(description = "User type")
    private String userType;

    @Schema(description = "Registration date")
    private Date createTime;

    @Schema(description = "Last update time")
    private Date updateTime;
}
