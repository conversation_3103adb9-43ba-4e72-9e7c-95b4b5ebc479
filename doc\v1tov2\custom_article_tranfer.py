#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库迁移脚本：从v1 MySQL数据库迁移到v2 PostgreSQL数据库
迁移表：tb_dds_article、tb_dds_article_attachment
迁移范围：pmid >= ************ AND pmid <= ************
"""

import logging
import sys
import traceback
from datetime import datetime

import pymysql
import psycopg2
import psycopg2.extras
from toollib.guid import SnowFlake

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('migration.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# 数据库配置
V1_MYSQL_CONFIG = {
    'host': '************',  # TODO: 修改为实际的MySQL主机地址
    'port': 32526,
    'user': 'root',  # TODO: 修改为实际的MySQL用户名
    'password': 'Lfgzs@2021',  # TODO: 修改为实际的MySQL密码
    'database': 'plosp_online',
    'charset': 'utf8mb4'
}

V2_PGSQL_CONFIG = {
    'host': '************',  # TODO: 修改为实际的PostgreSQL主机地址
    'port': 31909,
    'user': 'postgres',  # TODO: 修改为实际的PostgreSQL用户名
    'password': 'Biosino+2025',  # TODO: 修改为实际的PostgreSQL密码
    'database': 'plosp',  # TODO: 修改为实际的PostgreSQL数据库名
    'options': '-c search_path=public'  # TODO: 修改为实际的schema名称，如果不是public的话
}

# 迁移范围配置
PMID_MIN = ************
PMID_MAX = ************

# 运行模式配置
DRY_RUN = False  # 设置为True时只预览不实际执行迁移

# 雪花ID生成器
# 分布式需要根据ip或其他唯一标识映射机器码：worker_id与datacenter_id
# 如：SnowFlake(worker_id=1, datacenter_id=1)
# 更多参数详见源码
snow = SnowFlake(worker_id=1, datacenter_id=1)


# 用于dry_run模式的模拟ID生成器
class MockIdGenerator:
    def __init__(self, start_id=1000000000000000000):
        self.current_id = start_id

    def gen_uid(self):
        self.current_id += 1
        return self.current_id


mock_snow = MockIdGenerator()


def get_mysql_connection():
    """获取MySQL数据库连接"""
    try:
        connection = pymysql.connect(**V1_MYSQL_CONFIG)
        logger.info("MySQL数据库连接成功")
        return connection
    except Exception as e:
        logger.error(f"MySQL数据库连接失败: {e}")
        raise


def get_pgsql_connection():
    """获取PostgreSQL数据库连接"""
    try:
        connection = psycopg2.connect(**V2_PGSQL_CONFIG)
        logger.info("PostgreSQL数据库连接成功")
        return connection
    except Exception as e:
        logger.error(f"PostgreSQL数据库连接失败: {e}")
        raise


def check_migration_scope():
    """检查迁移范围内的数据量"""
    mysql_conn = None
    try:
        mysql_conn = get_mysql_connection()
        cursor = mysql_conn.cursor()

        # 检查文章数量
        article_query = """
        SELECT count(1) FROM `tb_dds_article`
        WHERE `pmid` >= %s AND `pmid` <= %s
        """
        cursor.execute(article_query, (PMID_MIN, PMID_MAX))
        article_count = cursor.fetchone()[0]
        logger.info(f"待迁移文章数量: {article_count}")

        # 检查附件数量
        attachment_query = """
        SELECT count(1) FROM `tb_dds_article_attachment`
        WHERE `pmid` >= %s AND `pmid` <= %s AND type = 'PDF'
        """
        cursor.execute(attachment_query, (PMID_MIN, PMID_MAX))
        attachment_count = cursor.fetchone()[0]
        logger.info(f"待迁移PDF附件数量: {attachment_count}")

        return article_count, attachment_count

    except Exception as e:
        logger.error(f"检查迁移范围失败: {e}")
        raise
    finally:
        if mysql_conn:
            mysql_conn.close()


def convert_text_to_array(text, separators=None):
    """将文本按分隔符转换为数组格式

    Args:
        text: 待转换的文本
        separators: 分隔符列表，默认为 [';']

    Returns:
        转换后的数组，如果文本为空则返回None
    """
    if not text or not text.strip():
        return None

    if separators is None:
        separators = [';']

    # 按优先级尝试分隔符
    for separator in separators:
        if separator in text:
            items = [item.strip() for item in text.split(separator) if item.strip()]
            return items if items else None

    # 如果没有找到分隔符，返回单个元素的数组
    return [text.strip()]


def convert_source_to_array(source_str):
    """将v1的source字符串转换为v2的数组格式"""
    return convert_text_to_array(source_str)


def convert_author_to_array(author_text):
    """将v1的author文本转换为v2的数组格式"""
    return convert_text_to_array(author_text)


def convert_affiliation_to_array(affiliation_text):
    """将v1的affiliation文本转换为v2的数组格式"""
    return convert_text_to_array(affiliation_text)


def convert_keyword_to_array(keyword_text):
    """将v1的keyword文本转换为v2的数组格式"""
    return convert_text_to_array(keyword_text)


def migrate_articles(dry_run=False):
    """迁移文章数据

    Args:
        dry_run: 是否为预览模式，True时不实际执行插入操作
    """
    mysql_conn = None
    pgsql_conn = None
    migrated_count = 0
    failed_count = 0

    try:
        mysql_conn = get_mysql_connection()
        mysql_cursor = mysql_conn.cursor(pymysql.cursors.DictCursor)

        # 只在非dry_run模式下连接PostgreSQL
        if not dry_run:
            pgsql_conn = get_pgsql_connection()
            pgsql_cursor = pgsql_conn.cursor()
        else:
            pgsql_conn = None
            pgsql_cursor = None

        # 查询v1数据库中的文章数据
        select_query = """
        SELECT pmid, pmc_id, article_type, subject_type, source, pub_status, language,
               vernacular_title, title, date_pubmed, date_received_accepted,
               published_year, published_month, published_day, print_published_year,
               print_published_month, print_published_day, journal_id, medline_date,
               year, volume, issue, page, e_location_type, e_location_id, author,
               author_isparsed, affiliation, affiliation_isparsed, article_abstract,
               article_abstract_cn, copyright, article_other_abstract, other_copyright,
               keyword, reference_number, reference_id_list, note, create_date,
               update_date, hit_num, update_validate_date
        FROM `tb_dds_article`
        WHERE `pmid` >= %s AND `pmid` <= %s
        ORDER BY pmid
        """

        logger.info("开始查询v1数据库中的文章数据...")
        mysql_cursor.execute(select_query, (PMID_MIN, PMID_MAX))

        # 准备v2数据库的插入语句
        insert_query = """
        INSERT INTO tb_dds_article (
            id, custom_id, pmid, pmc_id, doi, source, pub_status, language,
            vernacular_title, title, published_year, published_month, published_day,
            other_date, journal_id, year, volume, issue, page, author, affiliation,
            keywords, abstract, other_abstract, copyright, hit_num, download,
            create_time, update_time
        ) VALUES (
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
        )
        """

        if dry_run:
            logger.info("【DRY RUN模式】开始预览文章数据迁移...")
        else:
            logger.info("开始迁移文章数据...")
        batch_size = 1000
        batch_data = []
        preview_count = 0

        for row in mysql_cursor:
            try:
                # 生成雪花ID作为v2的主键
                if dry_run:
                    new_id = mock_snow.gen_uid()  # 使用模拟ID生成器
                else:
                    new_id = snow.gen_uid()  # 使用真实雪花ID生成器

                # 字段映射和转换
                custom_id = row['pmid']  # v1的pmid作为v2的custom_id
                pmid = None  # v2的pmid字段设为None，因为这些是自定义文章，不是真正的PubMed文章
                pmc_id = row['pmc_id']
                doi = None  # v1表中没有doi字段

                sourceStr = row['source']
                if not sourceStr:
                    sourceStr = 'Custom'

                source = convert_source_to_array(sourceStr)

                pub_status = row['pub_status']
                language = row['language']
                vernacular_title = row['vernacular_title']
                title = row['title']
                published_year = row['published_year']
                published_month = row['published_month']
                published_day = row['published_day']

                # other_date字段需要特殊处理，v1中有多个日期字段
                # TODO: 根据实际需求构建other_date数组
                other_date = None

                journal_id = row['journal_id']
                year = row['year']
                volume = row['volume']
                issue = row['issue']
                page = row['page']

                # 转换文本字段为数组
                author = convert_author_to_array(row['author'])
                affiliation = convert_affiliation_to_array(row['affiliation'])
                keywords = convert_keyword_to_array(row['keyword'])

                # 摘要字段
                abstract = row['article_abstract']
                other_abstract = row['article_other_abstract']
                copyright_info = row['copyright']

                hit_num = row['hit_num'] or 0
                download = 0  # v1中没有下载数字段，设为0

                create_time = row['create_date']
                update_time = row['update_date']

                # 准备插入数据
                insert_data = (
                    new_id, custom_id, pmid, pmc_id, doi, source, pub_status, language,
                    vernacular_title, title, published_year, published_month, published_day,
                    other_date, journal_id, year, volume, issue, page, author, affiliation,
                    keywords, abstract, other_abstract, copyright_info, hit_num, download,
                    create_time, update_time
                )

                batch_data.append(insert_data)

                # 在dry_run模式下，只预览前几条数据
                if dry_run:
                    preview_count += 1
                    if preview_count <= 3:  # 只显示前3条作为预览
                        logger.info(f"【预览】文章数据 #{preview_count}:")
                        logger.info(f"  v1.pmid: {row['pmid']} -> v2.custom_id: {custom_id}")
                        logger.info(f"  标题: {title}")
                        logger.info(f"  作者: {author}")
                        logger.info(f"  期刊ID: {journal_id}")
                    if len(batch_data) >= batch_size:
                        migrated_count += len(batch_data)
                        logger.info(f"【DRY RUN】预览文章数量: {migrated_count}")
                        batch_data = []
                else:
                    # 实际执行批量插入
                    if len(batch_data) >= batch_size:
                        pgsql_cursor.executemany(insert_query, batch_data)
                        pgsql_conn.commit()
                        migrated_count += len(batch_data)
                        logger.info(f"已迁移文章数量: {migrated_count}")
                        batch_data = []

            except Exception as e:
                logger.error(f"迁移文章失败 pmid={row['pmid']}: {e}")
                failed_count += 1
                continue

        # 处理剩余的批次数据
        if batch_data:
            if dry_run:
                migrated_count += len(batch_data)
                logger.info(f"【DRY RUN】预览文章数量: {migrated_count}")
            else:
                pgsql_cursor.executemany(insert_query, batch_data)
                pgsql_conn.commit()
                migrated_count += len(batch_data)

        if dry_run:
            logger.info(f"【DRY RUN】文章预览完成! 预计迁移: {migrated_count}, 失败: {failed_count}")
        else:
            logger.info(f"文章迁移完成! 成功: {migrated_count}, 失败: {failed_count}")
        return migrated_count, failed_count

    except Exception as e:
        logger.error(f"文章迁移过程中发生错误: {e}")
        logger.error(traceback.format_exc())
        if pgsql_conn:
            pgsql_conn.rollback()
        raise
    finally:
        if mysql_conn:
            mysql_conn.close()
        if pgsql_conn:
            pgsql_conn.close()


def get_article_id_mapping():
    """获取文章ID映射关系：v1的pmid -> v2的id"""
    pgsql_conn = None
    mapping = {}

    try:
        pgsql_conn = get_pgsql_connection()
        cursor = pgsql_conn.cursor()

        # 查询已迁移的文章，获取custom_id和id的映射关系
        query = """
        SELECT id, custom_id FROM tb_dds_article
        WHERE custom_id >= %s AND custom_id <= %s
        """
        cursor.execute(query, (PMID_MIN, PMID_MAX))

        for row in cursor.fetchall():
            article_id, custom_id = row
            mapping[custom_id] = article_id

        logger.info(f"获取到文章ID映射关系 {len(mapping)} 条")
        return mapping

    except Exception as e:
        logger.error(f"获取文章ID映射关系失败: {e}")
        raise
    finally:
        if pgsql_conn:
            pgsql_conn.close()


def migrate_attachments(dry_run=False):
    """迁移附件数据

    Args:
        dry_run: 是否为预览模式，True时不实际执行插入操作
    """
    mysql_conn = None
    pgsql_conn = None
    migrated_count = 0
    failed_count = 0

    try:
        # 在dry_run模式下，创建模拟的文章ID映射关系
        if dry_run:
            logger.info("【DRY RUN模式】创建模拟的文章ID映射关系...")
            article_mapping = {}
            # 为了预览，我们创建一些模拟的映射关系
            for pmid in range(PMID_MIN, min(PMID_MIN + 1000, PMID_MAX + 1)):  # 只模拟前1000个
                article_mapping[pmid] = mock_snow.gen_uid()  # 生成模拟的文章ID
            logger.info(f"【DRY RUN模式】创建了 {len(article_mapping)} 条模拟映射关系")
        else:
            # 实际模式下获取真实的文章ID映射关系
            article_mapping = get_article_id_mapping()
            if not article_mapping:
                logger.warning("没有找到文章ID映射关系，跳过附件迁移")
                return 0, 0

        mysql_conn = get_mysql_connection()
        mysql_cursor = mysql_conn.cursor(pymysql.cursors.DictCursor)

        # 只在非dry_run模式下连接PostgreSQL
        if not dry_run:
            pgsql_conn = get_pgsql_connection()
            pgsql_cursor = pgsql_conn.cursor()
        else:
            pgsql_conn = None
            pgsql_cursor = None

        # 查询v1数据库中的附件数据
        select_query = """
        SELECT id, pmid, type, origin_name, local_path, file_suffix, source,
               description, create_userid, create_siteid, create_time file_md5
        FROM `tb_dds_article_attachment`
        WHERE `pmid` >= %s AND `pmid` <= %s AND type = 'PDF'
        ORDER BY pmid, create_time
        """

        logger.info("开始查询v1数据库中的附件数据...")
        mysql_cursor.execute(select_query, (PMID_MIN, PMID_MAX))

        # 准备v2数据库的插入语句
        insert_query = """
        INSERT INTO tb_dds_file (
            id, file_name, content_type, file_size, md5, file_path,
            create_time, type, doc_id, source
        ) VALUES (
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
        )
        """

        if dry_run:
            logger.info("【DRY RUN模式】开始预览附件数据迁移...")
        else:
            logger.info("开始迁移附件数据...")
        batch_size = 100
        batch_data = []
        preview_count = 0

        for row in mysql_cursor:
            try:
                # 检查对应的文章是否已迁移
                v1_pmid = row['pmid']
                if v1_pmid not in article_mapping:
                    logger.warning(f"附件对应的文章未找到，跳过 pmid={v1_pmid}, attachment_id={row['id']}")
                    failed_count += 1
                    continue

                # 生成雪花ID作为v2的主键
                if dry_run:
                    new_id = mock_snow.gen_uid()  # 使用模拟ID生成器
                else:
                    new_id = snow.gen_uid()  # 使用真实雪花ID生成器

                # 字段映射和转换
                file_name = row['origin_name']
                content_type = row['file_suffix']  # v1的file_suffix对应v2的content_type
                file_size = None  # v1表中没有文件大小字段，需要根据实际文件计算
                md5 = row['file_md5']
                file_path = row['local_path']
                create_time = row['create_time']
                file_type = 'PDF'  # 固定为PDF类型
                doc_id = article_mapping[v1_pmid]  # 对应的文章ID
                source = row['source']

                # TODO: 如果需要计算文件大小，可以在这里添加文件大小计算逻辑
                # import os
                # if file_path and os.path.exists(file_path):
                #     file_size = os.path.getsize(file_path)

                # 准备插入数据
                insert_data = (
                    new_id, file_name, content_type, file_size, md5, file_path,
                    create_time, file_type, doc_id, source
                )

                batch_data.append(insert_data)

                # 在dry_run模式下，只预览前几条数据
                if dry_run:
                    preview_count += 1
                    if preview_count <= 3:  # 只显示前3条作为预览
                        logger.info(f"【预览】附件数据 #{preview_count}:")
                        logger.info(f"  v1.pmid: {v1_pmid} -> v2.doc_id: {doc_id}")
                        logger.info(f"  文件名: {file_name}")
                        logger.info(f"  文件路径: {file_path}")
                        logger.info(f"  MD5: {md5}")
                    if len(batch_data) >= batch_size:
                        migrated_count += len(batch_data)
                        logger.info(f"【DRY RUN】预览附件数量: {migrated_count}")
                        batch_data = []
                else:
                    # 实际执行批量插入
                    if len(batch_data) >= batch_size:
                        pgsql_cursor.executemany(insert_query, batch_data)
                        pgsql_conn.commit()
                        migrated_count += len(batch_data)
                        logger.info(f"已迁移附件数量: {migrated_count}")
                        batch_data = []

            except Exception as e:
                logger.error(f"迁移附件失败 pmid={row['pmid']}, attachment_id={row['id']}: {e}")
                failed_count += 1
                continue

        # 处理剩余的批次数据
        if batch_data:
            if dry_run:
                migrated_count += len(batch_data)
                logger.info(f"【DRY RUN】预览附件数量: {migrated_count}")
            else:
                pgsql_cursor.executemany(insert_query, batch_data)
                pgsql_conn.commit()
                migrated_count += len(batch_data)

        if dry_run:
            logger.info(f"【DRY RUN】附件预览完成! 预计迁移: {migrated_count}, 失败: {failed_count}")
        else:
            logger.info(f"附件迁移完成! 成功: {migrated_count}, 失败: {failed_count}")
        return migrated_count, failed_count

    except Exception as e:
        logger.error(f"附件迁移过程中发生错误: {e}")
        logger.error(traceback.format_exc())
        if pgsql_conn:
            pgsql_conn.rollback()
        raise
    finally:
        if mysql_conn:
            mysql_conn.close()
        if pgsql_conn:
            pgsql_conn.close()


def main():
    """主函数：执行完整的迁移流程"""
    start_time = datetime.now()
    logger.info("=" * 60)
    if DRY_RUN:
        logger.info("【DRY RUN模式】开始预览数据库迁移任务")
    else:
        logger.info("开始执行数据库迁移任务")
    logger.info(f"迁移范围: pmid >= {PMID_MIN} AND pmid <= {PMID_MAX}")
    logger.info(f"开始时间: {start_time}")
    if DRY_RUN:
        logger.info("注意：当前为预览模式，不会实际修改数据库")
    logger.info("=" * 60)

    try:
        # 步骤1: 检查迁移范围
        logger.info("步骤1: 检查迁移范围内的数据量...")
        article_count, attachment_count = check_migration_scope()

        if article_count == 0:
            logger.warning("没有找到需要迁移的文章数据，退出程序")
            return

        # 确认是否继续
        logger.info(f"即将迁移 {article_count} 篇文章和 {attachment_count} 个PDF附件")

        # 步骤2: 迁移文章数据
        if DRY_RUN:
            logger.info("步骤2: 开始预览文章数据...")
        else:
            logger.info("步骤2: 开始迁移文章数据...")
        article_migrated, article_failed = migrate_articles(dry_run=DRY_RUN)

        if article_migrated == 0 and not DRY_RUN:
            logger.error("文章迁移失败，跳过附件迁移")
            return

        # 步骤3: 迁移附件数据
        if DRY_RUN:
            logger.info("步骤3: 开始预览附件数据...")
        else:
            logger.info("步骤3: 开始迁移附件数据...")
        attachment_migrated, attachment_failed = migrate_attachments(dry_run=DRY_RUN)

        # 步骤4: 输出迁移结果
        end_time = datetime.now()
        duration = end_time - start_time

        logger.info("=" * 60)
        if DRY_RUN:
            logger.info("【DRY RUN模式】数据库迁移预览完成!")
        else:
            logger.info("数据库迁移任务完成!")
        logger.info(f"结束时间: {end_time}")
        logger.info(f"总耗时: {duration}")
        if DRY_RUN:
            logger.info("预览结果统计:")
            logger.info(f"  文章: 预计迁移 {article_migrated}, 失败 {article_failed}")
            logger.info(f"  附件: 预计迁移 {attachment_migrated}, 失败 {attachment_failed}")
            logger.info("注意：以上为预览结果，未实际修改数据库")
        else:
            logger.info("迁移结果统计:")
            logger.info(f"  文章: 成功 {article_migrated}, 失败 {article_failed}")
            logger.info(f"  附件: 成功 {attachment_migrated}, 失败 {attachment_failed}")
        logger.info("=" * 60)

        # 如果有失败的记录，提醒检查日志
        if article_failed > 0 or attachment_failed > 0:
            logger.warning("存在迁移失败的记录，请检查日志文件 migration.log 获取详细信息")

        # DRY RUN模式的额外提示
        if DRY_RUN:
            logger.info("如需实际执行迁移，请将脚本中的 DRY_RUN 设置为 False")

    except Exception as e:
        logger.error(f"迁移任务执行失败: {e}")
        logger.error(traceback.format_exc())
        sys.exit(1)


def verify_migration():
    """验证迁移结果"""
    logger.info("开始验证迁移结果...")

    mysql_conn = None
    pgsql_conn = None

    try:
        mysql_conn = get_mysql_connection()
        pgsql_conn = get_pgsql_connection()

        mysql_cursor = mysql_conn.cursor()
        pgsql_cursor = pgsql_conn.cursor()

        # 验证文章数量
        mysql_cursor.execute(
            "SELECT count(1) FROM `tb_dds_article` WHERE `pmid` >= %s AND `pmid` <= %s",
            (PMID_MIN, PMID_MAX)
        )
        v1_article_count = mysql_cursor.fetchone()[0]

        pgsql_cursor.execute(
            "SELECT count(1) FROM tb_dds_article WHERE custom_id >= %s AND custom_id <= %s",
            (PMID_MIN, PMID_MAX)
        )
        v2_article_count = pgsql_cursor.fetchone()[0]

        # 验证附件数量
        mysql_cursor.execute(
            "SELECT count(1) FROM `tb_dds_article_attachment` WHERE `pmid` >= %s AND `pmid` <= %s AND type = 'PDF'",
            (PMID_MIN, PMID_MAX)
        )
        v1_attachment_count = mysql_cursor.fetchone()[0]

        pgsql_cursor.execute(
            """SELECT count(1) FROM tb_dds_file f
               JOIN tb_dds_article a ON f.doc_id = a.id
               WHERE a.custom_id >= %s AND a.custom_id <= %s AND f.type = 'PDF'""",
            (PMID_MIN, PMID_MAX)
        )
        v2_attachment_count = pgsql_cursor.fetchone()[0]

        logger.info("迁移结果验证:")
        logger.info(f"  文章数量 - v1: {v1_article_count}, v2: {v2_article_count}")
        logger.info(f"  附件数量 - v1: {v1_attachment_count}, v2: {v2_attachment_count}")

        if v1_article_count == v2_article_count and v1_attachment_count == v2_attachment_count:
            logger.info("✓ 迁移验证通过，数据量一致")
        else:
            logger.warning("✗ 迁移验证失败，数据量不一致，请检查迁移过程")

    except Exception as e:
        logger.error(f"验证迁移结果失败: {e}")
    finally:
        if mysql_conn:
            mysql_conn.close()
        if pgsql_conn:
            pgsql_conn.close()


if __name__ == "__main__":
    # 检查必要的依赖包
    try:
        import pymysql
        import psycopg2
    except ImportError as e:
        logger.error(f"缺少必要的依赖包: {e}")
        logger.error("请安装依赖包: pip install pymysql psycopg2-binary")
        sys.exit(1)

    # 执行迁移
    main()

    # 验证迁移结果（仅在非DRY_RUN模式下执行）
    if not DRY_RUN:
        verify_migration()
    else:
        logger.info("【DRY RUN模式】跳过迁移结果验证")
