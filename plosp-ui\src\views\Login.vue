<template>
  <div class="login-page">
    <!-- 装饰性几何图形 -->
    <div class="decoration-shapes">
      <div class="shape shape-1"></div>
      <div class="shape shape-2"></div>
      <div class="shape shape-3"></div>
    </div>

    <div class="login-container">
      <div class="login-card">
        <div class="login-header">
          <h1 class="login-title">用户登录</h1>
          <p class="login-subtitle">欢迎回到PLOSP科学文献私人订制图书馆</p>
        </div>

        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginRules"
          class="login-form"
          label-position="left"
          size="large"
        >
          <el-form-item
            prop="email"
            class="form-group"
            required
            label="邮箱"
          >
            <el-input
              v-model="loginForm.email"
              placeholder="请输入邮箱地址"
              class="form-input"
            />
          </el-form-item>

          <el-form-item
            prop="password"
            class="form-group"
            required
            label="密码"
          >
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="请输入密码"
              class="form-input"
              show-password
            />
          </el-form-item>

          <div class="form-options">
            <el-checkbox v-model="loginForm.rememberMe">记住我</el-checkbox>
            <router-link to="/forgot-password" class="forgot-link">忘记密码？</router-link>
          </div>

          <el-button
            type="primary"
            size="large"
            class="login-button"
            @click="handleLogin"
            :loading="loading"
          >
            立即登录
          </el-button>

          <div class="register-link">
            还没有账户？
            <router-link to="/register" class="link">立即注册</router-link>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import { getRedirectPath } from '@/utils/permission'

// 路由相关
const router = useRouter()
const route = useRoute()

// 认证状态管理
const authStore = useAuthStore()

// 表单引用
const loginFormRef = ref()

// 表单数据
const loginForm = ref({
  email: '',
  password: '',
  rememberMe: false
})

// 表单验证规则
const loginRules = {
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ]
}

// 加载状态
const loading = ref(false)

// 登录处理
const handleLogin = async () => {
  if (!loginFormRef.value) return

  const valid = await loginFormRef.value.validate().catch(() => false)
  if (!valid) {
    ElMessage.warning('请检查表单输入')
    return
  }

  loading.value = true

  try {
    const result = await authStore.login(loginForm.value)

    if (result.success) {
      // 登录成功，跳转到目标页面
      const redirectPath = getRedirectPath(route)
      await router.push(redirectPath)
    }
  } catch (error) {
    console.error('登录失败:', error)
  } finally {
    loading.value = false
  }
}

// 组件挂载时检查是否已登录
onMounted(() => {
  if (authStore.isLoggedIn) {
    const redirectPath = getRedirectPath(route)
    router.push(redirectPath)
  }
})
</script>

<style lang="scss" scoped>
@import "@/assets/styles/variables";

.login-page {
  min-height: 80vh;
  background: linear-gradient(135deg, rgba($primary-color, 0.05) 0%, rgba($secondary-color, 0.05) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-lg;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
      radial-gradient(circle at 20% 80%, rgba($secondary-color, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba($primary-color, 0.08) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba($light-blue, 0.05) 0%, transparent 50%);
    z-index: 0;
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 60 60"><defs><pattern id="dots" width="60" height="60" patternUnits="userSpaceOnUse"><circle cx="30" cy="30" r="1.5" fill="rgba(4,56,115,0.1)"/><circle cx="10" cy="10" r="1" fill="rgba(79,156,249,0.08)"/><circle cx="50" cy="50" r="1" fill="rgba(79,156,249,0.08)"/></pattern></defs><rect width="100%" height="100%" fill="url(%23dots)"/></svg>');
    z-index: 0;
  }
}

.login-container {
  width: 100%;
  max-width: 480px;
}

.login-card {
  background: $white;
  border-radius: $border-radius-xxl;
  box-shadow: $box-shadow;
  padding: $spacing-xxxl;
  position: relative;
  z-index: 1;
  border: 1px solid rgba($primary-color, 0.08);



  @media (max-width: $breakpoint-md) {
    padding: $spacing-xl;
  }
}

.login-header {
  text-align: center;
  margin-bottom: $spacing-xxxl;

  .login-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, $primary-color, lighten($primary-color, 20%));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto $spacing-lg auto;
    box-shadow: 0 10px 30px rgba($primary-color, 0.3);

    .el-icon {
      color: white;
    }
  }

  .login-title {
    font-size: $font-size-xxxxxlarge;
    font-weight: $font-weight-bold;
    color: $primary-color;
    margin: 0 0 $spacing-sm 0;
    background: linear-gradient(135deg, $primary-color, lighten($primary-color, 20%));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .login-subtitle {
    font-size: $font-size-medium;
    color: $gray;
    margin: 0;
    line-height: 1.5;
  }
}

.login-form {
  :deep(.el-form-item) {
    margin-bottom: $spacing-lg;

    .el-form-item__label {
      font-size: 16px;
      color: #374151;
      font-weight: 500;
      padding: 0;
      margin-right: 10px;
    }

    .el-form-item__content {
      line-height: 1.4;
    }

    .el-form-item__error {
      font-size: 12px;
      color: #ff4757;
      padding-top: 4px;
    }
  }

  .form-group {
    .form-label {
      display: block;
      font-size: $font-size-small;
      color: $gray;
      font-weight: $font-weight-medium;

      .required {
        color: #ff4757;
        margin-right: 4px;
      }
    }

    .form-input {
      :deep(.el-input__wrapper) {
        border-radius: $border-radius-md;
        box-shadow: none;
        border: 1px solid #e1e5e9;

        &:hover {
          border-color: $primary-color;
        }

        &.is-focus {
          border-color: $primary-color;
          box-shadow: 0 0 0 2px rgba($primary-color, 0.1);
        }
      }
    }
  }

  .form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-xl;

    @media (max-width: $breakpoint-md) {
      flex-direction: column;
      gap: $spacing-sm;
      align-items: flex-start;
    }

    :deep(.el-checkbox) {
      .el-checkbox__label {
        color: $gray;
        font-size: $font-size-small;
      }
    }

    .forgot-link {
      color: $primary-color;
      text-decoration: none;
      font-size: $font-size-small;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .login-button {
    width: 100%;
    height: 48px;
    background-color: $primary-color;
    border-color: $primary-color;
    border-radius: $border-radius-md;
    font-size: $font-size-medium;
    font-weight: $font-weight-medium;

    &:hover {
      background-color: darken($primary-color, 10%);
      border-color: darken($primary-color, 10%);
    }
  }

  .register-link {
    text-align: center;
    margin-top: $spacing-lg;
    color: $gray;
    font-size: $font-size-small;

    .link {
      color: $primary-color;
      text-decoration: none;
      font-weight: $font-weight-medium;

      &:hover {
        text-decoration: underline;
      }
    }
  }
}

// 装饰性几何图形
.decoration-shapes {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 0;

  .shape {
    position: absolute;
    border-radius: 50%;
    opacity: 0.6;
    animation: float 6s ease-in-out infinite;

    &.shape-1 {
      width: 90px;
      height: 90px;
      background: linear-gradient(135deg, rgba($secondary-color, 0.2), rgba($light-blue, 0.1));
      top: 10%;
      left: 10%;
      animation-delay: 0s;
    }

    &.shape-2 {
      width: 80px;
      height: 80px;
      background: linear-gradient(135deg, rgba($primary-color, 0.15), rgba($secondary-color, 0.1));
      top: 70%;
      right: 15%;
      animation-delay: 2s;
    }

    &.shape-3 {
      width: 60px;
      height: 60px;
      background: linear-gradient(135deg, rgba($light-blue, 0.2), rgba($secondary-color, 0.1));
      top: 30%;
      right: 25%;
      animation-delay: 4s;
    }
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}
</style>
