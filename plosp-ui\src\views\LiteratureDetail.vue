<template>
  <div class="literature-detail-view">
    <div class="detail-layout">
      <main class="detail-main">
        <div class="container main-content">
          <div>
        <!-- 标题与来源徽章 -->
           <div class="detail-title-row">
            <div class="source-badge">PubMed</div>
            <!-- 期刊、DOI、PMID、时间 -->
            <div class="detail-meta-row">
              <span class="journal">Journal of Immunology Research</span>
              <span class="publish-date">2023 Jun;15(6):1243-1258</span>
              <span class="doi">DOI: <span class="meta-value">10.1016/j.cger.2020.06.009</span></span>
              <span class="pmid">PMID: <span class="meta-value">33010902</span></span>
            </div>
          </div>
           <h1 class="detail-title">Underlying sources of cognitive-anatomical variation in multi-modal neuroimaging and cognitive testing</h1>
            <!--  下载量和浏览量-->
            <div class="download">
              <div>
                <div>
                  <img src="@/assets/images/download-stat.svg" alt="" class="option-icon" />
                  <span>下载量:</span>
                </div>
                <span>12285</span>
              </div>
              <div>
                <div>
                  <img src="@/assets/images/browse-stat.svg" alt="" class="option-icon" />
                  <span>浏览量:</span>
                </div>
                <span>12285</span>
              </div>
            </div>

            <!-- 作者列表 -->
          <div class="detail-authors-row">
            <div>
              <img src="@/assets/images/author.svg" alt="" class="author-icon" />
              <div class="authors" :class="{ expanded: authorExpanded}">
                <span class="author">Paraminder Dhillon<sup class="author-index">1</sup></span>
                <el-divider direction="vertical" />
                <span class="author">P D Watson<sup class="author-index">2</sup></span>
                <el-divider direction="vertical" />
                <span class="author">G E Cooke<sup class="author-index">1</sup></span>
                <el-divider direction="vertical" />
                <span class="author">G E Cooke<sup class="author-index">1</sup></span>
                <el-divider direction="vertical" />
                <span class="author">G E Cooke<sup class="author-index">1</sup></span>
                <el-divider direction="vertical" />
                <span class="author">G E Cooke<sup class="author-index">1</sup></span>
                <el-divider direction="vertical" />
                <span class="author">G E Cooke<sup class="author-index">1</sup></span>
                <el-divider direction="vertical" />
                <span class="author">G E Cooke<sup class="author-index">1</sup></span>
                <el-divider direction="vertical" />
                <span class="author">G E Cooke<sup class="author-index">1</sup></span>
                <el-divider direction="vertical" />
                <span class="author">G E Cooke<sup class="author-index">1</sup></span>
                <el-divider direction="vertical" />
              </div>
            </div>
            <el-button type="text" class="toggle-btn" @click="authorExpanded=!authorExpanded">
              <span>{{ authorExpanded ? '收起' : '展开' }}</span>
              <el-icon><ArrowDown v-if="!authorExpanded"/><ArrowUp v-else/></el-icon>
            </el-button>
          </div>
          <!-- 作者机构 -->
          <div class="detail-affiliations-row">
            <img src="@/assets/images/affiliation.svg" alt="" class="option-icon" />
            <div class="affiliations">
              <span class="affiliation">1. Beckman Institute for Advanced Science and Technology, University of Illinois at Urbana-Champaign, Urbana, IL, USA.</span>
              <span class="affiliation">2. Stanford University, School of Medicine</span>
              <span class="affiliation">3. Harvard University, Department of Biology</span>
            </div>
          </div>
          <div class="row-btn">
              <el-popover
                  placement="bottom-start"
                  :width="160"
                  trigger="click"
              >
                <template #reference>
                  <el-button :icon="MessageBox">文献传递</el-button>
                </template>
                <div class="delivery-options">
                  <div class="delivery-option" >
                    <span>获取全文</span>
                  </div>
                  <div class="delivery-option" >
                    <span>获取元数据文件</span>
                  </div>
                  <div class="delivery-option">
                    <span>获取图文</span>
                  </div>
                </div>
              </el-popover>
              <el-button :icon="Star"  @click="handleBatchFavorite">收藏</el-button>
              <el-button :icon="Document">引用</el-button>
              <el-button :icon="EditPen" @click="handleCorrection">纠错</el-button>
          </div>
          <!-- 摘要区块 -->
          <div class="detail-section" id="abstract">
            <div class="section-title">摘要</div>
            <el-tabs v-model="activeName" class="demo-tabs">
              <el-tab-pane label="原文" name="first">
                <div class="section-content abstract">
                  Healthy adults have robust individual differences in <span class="text-primary">neuroanatomy</span>  and <span class="text-danger">cognitive ability</span>  not captured by demographics or <span class="text-primary">gross morphology</span>. We used a <span class="text-success">hierarchical independent component analysis (hICA)</span> to create novel characterizations of individual differences in our participants (N=190). These components fused data across multiple <span class="text-danger"> cognitive tests</span> and <span class="text-primary">neuroanatomical variables</span>. The first level contained four independent, underlying sources of <span class="text-warning">phenotypic variance</span> that predominately modeled broad relationships within types of data (e.g., <span class="text-primary">"white matter"</span> , or <span class="text-primary">"subcortical gray matter"</span>), but were not reflective of traditional individual difference measures such as sex, age, or <span class="text-primary">intracranial volume</span>. After accounting for the novel individual difference measures, a second level analysis identified two underlying sources of <span class="text-warning"> phenotypic variation</span>. One of these made strong, joint contributions to both the <span class="text-primary">anatomical structures</span> associated with the core <span class="text-primary">fronto-parietal</span> "rich club" network, and to cognitive factors.
                </div>
              </el-tab-pane>
              <el-tab-pane label="解读" name="second">
                <div class="section-content abstract">
                  Healthy adults have robust individual differences in <span class="text-primary">neuroanatomy</span>  and <span class="text-danger">cognitive ability</span>  not captured by demographics or <span class="text-primary">gross morphology</span>. We used a <span class="text-success">hierarchical independent component analysis (hICA)</span> to create novel characterizations of individual differences in our participants (N=190). These components fused data across multiple <span class="text-danger"> cognitive tests</span> and <span class="text-primary">neuroanatomical variables</span>. The first level contained four independent, underlying sources of <span class="text-warning">phenotypic variance</span> that predominately modeled broad relationships within types of data (e.g., <span class="text-primary">"white matter"</span> , or <span class="text-primary">"subcortical gray matter"</span>), but were not reflective of traditional individual difference measures such as sex, age, or <span class="text-primary">intracranial volume</span>. After accounting for the novel individual difference measures, a second level analysis identified two underlying sources of <span class="text-warning"> phenotypic variation</span>. One of these made strong, joint contributions to both the <span class="text-primary">anatomical structures</span> associated with the core <span class="text-primary">fronto-parietal</span> "rich club" network, and to cognitive factors.
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
          <!-- 关键词区块 -->
          <div class="detail-section">
            <div class="section-title">关键词</div>
            <div class="section-content keywords">
              <span class="keyword">Seagrass</span>
              <span class="keyword">Climate</span>
              <span class="keyword">Evolution</span>
              <span class="keyword">Ecosystem</span>
              <span class="keyword">Genetic diversity</span>
            </div>
          </div>
          <!-- 文献分类区块 -->
            <div class="detail-section">
              <div class="section-title">文献分类</div>
              <div class="section-content keywords">
                <span class="keyword">Marine Biology</span>
                <span class="keyword">Ecology</span>
                <span class="keyword">Evolution</span>
              </div>
            </div>
          <!-- 图表区块 -->
          <div class="detail-section">
            <div class="section-title">图表</div>
            <swiper
                :modules="modules"
                :slides-per-view="isMobile ? 1 : 2"
                :space-between="isMobile ? 15 : 25"
                :pagination="{ clickable: true }"
                :scrollbar="{ draggable: true }"
                :loop="true"
                :autoplay="{ delay: 4000 }"
                class="swiper"
                @swiper="onSwiper"
                @slide-change="onSlideChange"
            >
              <swiper-slide key="1">
                <img src="@/assets/images/chart1.png" alt="" class="option-icon"/>
              </swiper-slide>
              <swiper-slide key="2">
                <img src="@/assets/images/chart2.png" alt="" class="option-icon"/>
              </swiper-slide>
              <swiper-slide key="3">
                <img src="@/assets/images/chart2.png" alt="" class="option-icon"/>
              </swiper-slide>
            </swiper>
          </div>
            <!-- 实体区块 -->
            <div class="entity-section detail-section">
              <div class="entity-title">实体</div>
              <div class="entity-list">
                <div class="entity-item" v-for="(entity, idx) in entityTagList" :key="entity.key">
                  <div class="entity-header">
                    <div class="entity-content-wrapper">
                      <div class="entity-name-wrapper">
                        <span class="entity-name">{{ entity.title }}</span>
                      </div>
                      <div class="entity-tags-wrapper">
                        <div class="section-content keywords" :class="{ expanded: entity.expanded }">
                          <span class="keyword" v-for="(tag, tIdx) in entity.tags" :key="tIdx">{{ tag }}</span>
                        </div>
                      </div>
                    </div>
                    <div class="entity-toggle-wrapper">
                      <el-button type="text" class="toggle-btn" @click="toggleTagExpand(idx)">
                        <span>{{ entity.expanded ? '收起' : '展开' }}</span>
                        <el-icon><ArrowDown v-if="!entity.expanded"/><ArrowUp v-else/></el-icon>
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          <!-- 基金资助/关联数据/MeSH/参考文献/相似文章/相关链接区块 -->
          <div class="detail-section">
            <div class="section-title">基金资助</div>
            <div class="section-content">
              <ul class="funding-support">
                <li>
                  国家自然科学基金面上项目 (No. 82074123)
                </li>
                <li>
                  国家重点研发计划 (2021YFC2301800)
                </li>
              </ul>
            </div>
          </div>
          <div class="detail-section">
            <div class="section-title">关联数据</div>
            <div class="section-content">
              <span class="data-link">OED00007364</span>
              <span class="data-link">OED00007365</span>
            </div>
          </div>
            <div class="detail-section">
              <div class="section-title">MeSH主题词</div>
              <div class="section-content keywords">
                <span class="keyword">Ecosystem</span>
                <span class="keyword">Climate</span>
                <span class="keyword">Evolution</span>
                <span class="keyword">Ecosystem</span>
                <span class="keyword">Genetic diversity</span>
              </div>
            </div>
          <div class="detail-section">
            <div class="section-title">参考文献</div>
            <div class="section-content references">
              <div class="reference">
                <a href=""> 1. McKenzie, G. A. Communities and Ecosystem Dynamics. 1975.</a>
                <div class="date">
                  European Geophysics of the Sea Academy Press, Vol. 2, 2007.
                </div>
              </div>
              <div class="reference">
                <a href=""> 2. Smith, D., et al. The plant roots that drive ecosystem. Evidence from the continental shelf. 2023.</a>
                <div class="date">
                  Nature Ecology & Evolution, 7, 231-237.
                </div>
              </div>
              <div class="reference">
                <a href="">
                  3. Thompson, R. B., et al. Population genetic patterns across the native and invasive range of a widely distributed seagrass.
                </a>
                <div class="date">
                  Molecular Ecology, 2023.
                </div>

              </div>
            </div>
          </div>
          <div class="detail-section">
            <div class="section-title">相似文章</div>
            <div class="section-content references">
              <div class="reference">
                <a href="">1. Marine ecosystem responses to Cenozoic global change</a>
                <div class="date">
                  Nature, 2023
                </div>
              </div>
              <div class="reference">
                <a href=""> 2. Global patterns in marine biodiversity</a>
                <div class="date">
                  Science, 2023
                </div>
              </div>
            </div>
          </div>
          <div class="detail-section">
            <div class="section-title">相关链接</div>
            <div class="section-content">
              <div>
                <a href="#" class="data-link">PubMed</a>
              </div>
              <div>
                <a href="#" class="data-link">Web of Science</a>
              </div>
            </div>
          </div>
          </div>
          <!-- 右侧统计/导航/指标卡片 -->
          <aside class="detail-aside">
            <div class="aside-card ">
              <div class="metric-card">
                <div class="metric-row">
                  <div class="metric-label">
                    <img src="@/assets/images/frame.svg" alt="" />
                    影响因子
                  </div>
                  <div class="metric-circle">3356</div>
                </div>
                <div class="metric-row">
                  <div class="metric-label">
                    <img src="@/assets/images/frame.svg" alt="" />
                    引用量</div>
                  <div class="metric-circle">1218</div>
                </div>
                <div class="metric-row">
                  <div class="metric-label"> <img src="@/assets/images/frame.svg" alt="" />JCR分区</div>
                  <div class="metric-circle">2894</div>
                </div>
              </div>

              <div class="nav-card">
                <div class="nav-title">导航目录</div>
                <ul class="nav-list">
                  <li v-for="item in navList">
                    <img :src="iconMap[item.icon]" alt="">
                    {{item.label}}
                  </li>
                </ul>
              </div>
            </div>
          </aside>
        </div>
      </main>
    </div>
    <!-- 收藏弹框 -->
    <collection-modal
        v-model="showCollectionModal"
        :article="selectedArticle"
        @confirm="handleCollectionConfirm"
    />

    <!-- 纠错弹框 -->
    <el-dialog
        v-model="showCorrectionModal"
        title="文献纠错"
        width="500px"
        center
        :before-close="handleCorrectionClose"
    >
      <el-form :model="correctionForm" label-width="70px">
        <el-form-item label="数据类型">
          <el-select
              v-model="correctionForm.dataType"
              placeholder="请选择数据类型"
              style="width: 100%"
          >
            <el-option
                v-for="item in correctionDataTypes"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="反馈内容">
          <el-input
              v-model="correctionForm.description"
              type="textarea"
              :rows="6"
              placeholder="请详细描述发现的问题..."
              maxlength="500"
              show-word-limit
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCorrectionClose">取消</el-button>
          <el-button type="primary" @click="handleCorrectionConfirm">确认提交</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import 'swiper/css';
import 'swiper/css/pagination'; // 轮播图底面的小圆点
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/scrollbar';
import {ArrowDown,ArrowUp,Edit,Star,EditPen,Document,MessageBox,Files,Picture} from '@element-plus/icons-vue'

import {
  Navigation,
  Pagination,
  Scrollbar,
  Autoplay,
  Mousewheel,
} from 'swiper/modules';

const modules = [
  Mousewheel,
  Pagination,
  Navigation,
  Scrollbar,
  Autoplay,
];
import { Swiper, SwiperSlide } from 'swiper/vue';
import CollectionModal from "@/components/CollectionModal.vue";

const authorExpanded=ref(false)
const activeName = ref('first')

// 移动端检测
const windowWidth = ref(window.innerWidth)
const isMobile = computed(() => windowWidth.value <= 768)

// 收藏相关
const showCollectionModal = ref(false)
const selectedArticle = ref(null)

// 纠错相关
const showCorrectionModal = ref(false)
const correctionForm = ref({
  dataType: '',
  description: ''
})

const handleResize = () => {
  windowWidth.value = window.innerWidth
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

// 用import.meta.glob批量导入svg图片
const images = import.meta.glob('@/assets/images/*.svg', { eager: true, import: 'default' })
const chartSwiper = ref(null);
const iconMap = {}
Object.keys(images).forEach(key => {
  const fileName = key.split('/').pop()
  iconMap[fileName] = images[key]
})

const onSwiper = swiper => {
  chartSwiper.value = swiper;
};
// swiper切换时触发
const onSlideChange = swiper => {};

// 实体区块数据
const entityTagList = ref([
  {
    key: 'anatomy',
    title: '解剖学术语',
    expanded: false,
    tags: ['neuroanatomy', 'gross morphology', 'white matter', 'fronto-parietal','neuroanatomy', 'gross morphology', 'white matter', 'fronto-parietal']
  },
  {
    key: 'function',
    title: '功能指标',
    expanded: false,
    tags: ['cognitive ability', 'cognitive tests']
  },
  {
    key: 'method',
    title: '研究方法',
    expanded: false,
    tags: ['hierarchical independent component analysis (hICA)','hierarchical independent component analysis (hICA)','hierarchical independent component analysis (hICA)']
  },
  {
    key: 'bio',
    title: '生物学特征',
    expanded: false,
    tags: ['phenotypic variation', 'gross morphology', 'white matter']
  },
])
const toggleTagExpand = idx => {
  entityTagList.value[idx].expanded = !entityTagList.value[idx].expanded
}

// 纠错数据类型选项（基于navList + 其它）
const correctionDataTypes = computed(() => {
  const types = navList.value.map(item => ({
    label: item.label,
    value: item.label
  }))
  types.push({
    label: '其它',
    value: '其它'
  })
  return types
})
const navList=ref([
  {
    icon:'info.svg',
    label:'基本信息'
  },
 {
   icon:'references.svg',
       label:'摘要'
 },
  {
    icon:'keywords.svg',
    label:'关键词'
  }
  ,
  {
    icon:'category.svg',
    label:'文献分类'
  },
  {
    icon:'chart.svg',
    label:'图表'
  },
  {
    icon:'entity.svg',
    label:'实体'
  },
  {
    icon:'fund.svg',
    label:'基金支持'
  },
  {
    icon:'link-data.svg',
    label:'关联数据'
  },
  {
    icon:'mesh.svg',
    label:'MeSH主题词'
  },
  {
    icon:'frame.svg',
    label:'参考文献'
  },
  {
    icon:'article.svg',
    label:'相似文章'
  },
  {
    icon:'link.svg',
    label:'相关链接'
  }
])
// 收藏相关方法
const handleBatchFavorite = () => {
  showCollectionModal.value = true
}

const handleCollectionConfirm = (data) => {
  console.log('收藏确认:', data)
}

// 纠错相关方法
const handleCorrection = () => {
  showCorrectionModal.value = true
}

const handleCorrectionClose = () => {
  showCorrectionModal.value = false
  // 重置表单
  correctionForm.value = {
    dataType: '',
    description: ''
  }
}

const handleCorrectionConfirm = () => {
  handleCorrectionClose()
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/variables";
.literature-detail-view {
  background: $background-color;
  min-height: 100vh;
  padding: 0;
}
.detail-layout {
  padding: $spacing-xxl 0;
  font-size: $font-size-small;

  @media (max-width: $breakpoint-md) {
    padding: $spacing-md 0;
  }
}
.detail-main {
  flex: 1 1 0;
  background: $white;
  border-radius: $border-radius-xl;
  box-shadow: $box-shadow;
  padding-top: $spacing-xxl;
  @media (max-width: $breakpoint-md) {
    padding: $spacing-md $spacing-xs;
  }
}
.main-content{
  display: flex;
  gap: 140px;
  @media (max-width: $breakpoint-lg) {
    flex-direction: column;
    gap: $spacing-lg;
    padding: $spacing-lg 0;
  }
}
.row-btn{
  display: flex;
  gap: 20px;
  margin-top: 20px;

  @media (max-width: $breakpoint-md) {
    flex-wrap: wrap;
    gap: $spacing-sm;
  }

  .el-button{
    border: 1px solid #374151;
    width: 107px;
    font-weight: 400;

    @media (max-width: $breakpoint-md) {
      flex: 1;
      min-width: calc(50% - #{$spacing-sm / 2});
      width: auto;
    }

    &:hover{
      background-color: #043873;
      color: #FFFFFF;
    }
  }
}

// 文献传递下拉选项样式
.delivery-options {
  padding: 8px 0;

  .delivery-option {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    cursor: pointer;
    border-radius: 4px;
    transition: background-color 0.2s;
    font-size: 14px;
    color: #374151;

    &:hover {
      background-color: #f3f4f6;
    }

    .el-icon {
      font-size: 16px;
      color: #6b7280;
    }

    span {
      flex: 1;
    }
  }
}

.detail-aside {
  width: 310px;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  gap: $spacing-xl;
  @media (max-width: $breakpoint-lg) {
    width: 100%;
    flex-direction: row;
    gap: $spacing-md;
  }
  @media (max-width: $breakpoint-md) {
    flex-direction: column;
    gap: $spacing-md;
  }
}
.aside-card {
  position: sticky;
  top: 150px;
  background: $white;
  border-radius: $border-radius-lg;
  padding: $spacing-lg;
  margin-bottom: $spacing-md;
  border: 1px solid #B9B9B9;
}
.metric-card {
  display: flex;
  flex-direction: column;
  gap: $spacing-md;
}
.metric-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: $spacing-md;
}
.metric-circle {
 padding: 0px 14px;
  background: #F3F6F9;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: $font-size-medium;
  color: $gray;
}
.metric-label {
  display: flex;
  gap: 8px;
  font-size: $font-size-medium;
  color: $gray;
}

.nav-title {
  font-size: $font-size-medium;
  font-weight: $font-weight-bold;
  color: $primary-color;
  margin: 36px 0 16px 0;
}
.nav-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 16px;
  li {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}
.detail-title-row {
  display: flex;
  align-items: center;
  gap: $spacing-md;
  flex-wrap: wrap;
  margin-bottom: $spacing-sm;

  @media (max-width: $breakpoint-md) {
    flex-direction: column;
    align-items: flex-start;
    gap: $spacing-sm;
  }
}
.source-badge {
  background: #DCFCE7;
  color: #166534;
  font-size: $font-size-small;
  padding: 4px 14px;
  border-radius: 9999px;
  flex-shrink: 0;
}
.detail-title {
  font-size: $font-size-large;
  font-weight: $font-weight-bold;
  color: $primary-color;
  margin: 0;
  line-height: 1.3;
  flex: 1;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.detail-meta-row {
  display: flex;
  align-items: center;
  gap: $spacing-xl;
  color: $gray;
  font-size: $font-size-medium;

  @media (max-width: $breakpoint-md) {
    flex-direction: column;
    align-items: flex-start;
    gap: $spacing-xs;
    font-size: $font-size-small;
  }

  .publish-date {
    color: $gray;
  }
}
.download{
  display: flex;
  gap:$spacing-xl;
  align-items: center;
  color: $gray;
  font-size: $font-size-small;
  margin: $spacing-sm 0;

  @media (max-width: $breakpoint-md) {
    gap: $spacing-lg;
    flex-wrap: wrap;
  }

  &>div{
    display: flex;
    gap: $spacing-md;
    align-items: center;

    @media (max-width: $breakpoint-md) {
      gap: $spacing-sm;
    }

    &>div{
      display: flex;
      gap: 12px;
      align-items: center;

      @media (max-width: $breakpoint-md) {
        gap: 8px;
      }
    }
  }
}
.detail-authors-row {
  display: flex;
  flex-wrap: wrap;
  gap: $spacing-md;
  margin-bottom: $spacing-sm;
  justify-content: space-between;

  @media (max-width: $breakpoint-md) {
    flex-direction: column;
    gap: $spacing-sm;
  }

  &>div:first-child{
    flex: 1;
    display: flex;
    gap: $spacing-md;
    align-items: start;

    @media (max-width: $breakpoint-md) {
      gap: $spacing-sm;
    }

    .author-icon{
      margin-top: 4px;
    }
    .authors{
      display: flex;
      gap: $spacing-md;
      align-items: center;
      max-height: 24px;
      transition: all 0.2s;
      flex-wrap: wrap;
      overflow: hidden;

      @media (max-width: $breakpoint-md) {
        gap: $spacing-sm;
      }

      &.expanded{
        max-height: 300px;
      }
    }
  }
  .author {
    font-size: $font-size-small;
    color: $gray;
    font-weight: $font-weight-medium;
    .author-index{
      display: inline-block;
      width: 15px;
      height: 15px;
      line-height: 15px;
      background-color: #F1F1F1;
      text-align: center;
      border-radius: 50%;
      font-weight: 400;
    }
  }
}
.detail-affiliations-row {
  display: flex;
  align-items: start;
  gap: $spacing-md;

  @media (max-width: $breakpoint-md) {
    gap: $spacing-sm;
  }

  .affiliations{
    display: flex;
    flex-direction: column;
    gap: $spacing-xs;
  }
  .affiliation {
    font-size: $font-size-small;
    color: $gray;

    @media (max-width: $breakpoint-md) {
      font-size: 14px;
      line-height: 1.4;
    }
  }
}
.detail-section {
  margin: $spacing-xl 0;
  position: relative;
  .section-title {
    font-size: $font-size-medium;
    font-weight: $font-weight-bold;
    color: $primary-color;
    margin: $spacing-xs 0;
  }
  .section-content {
    font-size: $font-size-small;
    color: #374151;
    line-height: 1.7;
    &.keywords {
      display: flex;
      flex-wrap: wrap;
      gap: $spacing-xs;
      .keyword {
        background: #EFF6FF;
        color: #4F9CF9;
        font-size: $font-size-small;
        padding: 2px 12px;
        border-radius: 9999px;
      }
    }
    &.categories {
      display: flex;
      flex-wrap: wrap;
      gap: $spacing-xs;
      .category {
        background: $yellow;
        color: $primary-color;
        font-size: $font-size-small;
        padding: 4px 14px;
        border-radius: 9999px;
        margin-bottom: 2px;
      }
    }
    &.references {
      display: flex;
      flex-direction: column;
      gap: $spacing-xs;
      .reference {
        font-size: $font-size-small;
        color:  $secondary-color;
        line-height: 1.5;
        .date{
          color: $gray;
          font-size: 14px;
          margin-top: 2px;
        }
      }
    }
    .data-link {
      color: $secondary-color;
      margin-right: $spacing-md;
      font-size: $font-size-small;
    }
    .funding-support{
      color: $secondary-color;
      padding-left: 18px;
    }
    &.chart-content{
      img{
        width: 50%;
        @media (max-width: $breakpoint-md) {
          width: 100%;
        }
      }
    }
  }
  .demo-tabs{
    :deep(.el-tabs__nav-scroll){
      padding: 4px;
    }
    :deep(.el-tabs__active-bar){
      display: none;
    }
    :deep(.el-tabs__header){
      width: 145px;
      height: 36px;
      position: absolute;
      top: -8px;
      right: 0;
    }
    :deep(.el-tabs__nav-wrap){
      background-color: #F6F7F9;
      width: 145px;
      height: 36px;
      border-radius: 4px;
      &::after{
        display: none;
      }
    }
    :deep(.el-tabs__item){
      padding: 4px 20px;
      height: 28px;
      &.is-active{
        background-color: #ffffff;
      }
    }
  }
  .abstract{
    text-align: justify;
  }
}

.entity-section {
  border-radius: $border-radius-lg;
  margin-bottom: $spacing-xl;

  .keywords{
    max-height: 36px;
    overflow: hidden;
    transition: all 0.2s;

    @media (max-width: $breakpoint-md) {
      max-height: 32px;
    }

    &.expanded {
      max-height: 500px;
    }
  }

  .entity-title {
    font-size: $font-size-large;
    font-weight: $font-weight-bold;
    color: $primary-color;
    margin-bottom: $spacing-md;

    @media (max-width: $breakpoint-md) {
      font-size: $font-size-medium;
      margin-bottom: $spacing-sm;
    }
  }

  .entity-list {
    display: flex;
    flex-direction: column;
    gap: $spacing-md;

    @media (max-width: $breakpoint-md) {
      gap: $spacing-sm;
    }
  }

  .entity-item {
    background: $white;
    border-radius: $border-radius-md;
    position: relative;

    @media (max-width: $breakpoint-md) {
      padding: $spacing-sm;
      border: 1px solid #f0f0f0;
    }
  }
  .entity-header {
    display: flex;
    justify-content: space-between;
    cursor: pointer;
    font-size: $font-size-medium;
    color: $primary-color;
    padding: 0;

    @media (max-width: $breakpoint-md) {
      flex-direction: column;
      gap: $spacing-sm;
    }
  }

  .entity-content-wrapper {
    display: flex;
    align-items: start;
    flex: 1;

    @media (max-width: $breakpoint-md) {
      flex-direction: column;
      width: 100%;
      gap: $spacing-sm;
    }
  }

  .entity-name-wrapper {
    flex: 0 0 100px;

    @media (max-width: $breakpoint-md) {
      flex: 0 0 auto;
      width: 100%;
    }
  }

  .entity-name {
    font-size: $font-size-small;
    color: #000000;
    font-weight: 400;
    display: flex;
    align-items: center;
    height: 33px;

    @media (max-width: $breakpoint-md) {
      height: auto;
      padding: $spacing-xs 0;
      font-weight: 500;
    }
  }

  .entity-tags-wrapper {
    flex: 1;

    @media (max-width: $breakpoint-md) {
      width: 100%;
    }
  }

  .entity-toggle-wrapper {
    flex-shrink: 0;

    @media (max-width: $breakpoint-md) {
      align-self: flex-end;
      margin-top: $spacing-xs;
    }
  }
  .entity-content {
    margin-top: $spacing-sm;
    font-size: $font-size-small;
    color: $gray;
    ul {
      padding-left: 1.2em;
      margin: 0;
      li {
        margin-bottom: 4px;
      }
    }
  }
  .entity-divider {
    height: 1px;
    background: $light-gray;
    margin: $spacing-md 0 0 0;
    border-radius: 1px;
  }
}
.toggle-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  background: none;
  border: none;
  color: $primary-color;
  font-size: 14px;
  cursor: pointer;
  transition: color 0.2s;
  font-weight: 400;
  width: 45px;

  @media (max-width: $breakpoint-md) {
    font-size: 12px;
    width: auto;
    padding: $spacing-xs;
    background-color: rgba($primary-color, 0.1);
    border-radius: $border-radius-sm;
    min-height: 32px;
  }

  &:hover {
    color: $primary-color;
  }

  svg {
    transition: transform 0.2s;
  }

  &.open svg {
    transform: rotate(90deg);
  }
}


.swiper{
  width: 1055px!important;

  @media (max-width: $breakpoint-md) {
    width: 100%!important;
  }
}
.text-primary{
  color: #4F9CF9;
}
.text-warning{
  color: #F3AA44;
}
.text-danger{
  color: #E363B6;
}
.text-success{
  color: #31BD2C;
}
@media (max-width: $breakpoint-md) {
  .detail-main {
    padding: $spacing-md $spacing-xs;
    border-radius: $border-radius-md;
  }

  .detail-title {
    font-size: $font-size-large;
    line-height: 1.4;
    margin-bottom: $spacing-md;
  }

  .detail-section {
    margin: $spacing-lg 0;

    .section-title {
      font-size: $font-size-medium;
      margin-bottom: $spacing-sm;
    }

    .section-content {
      font-size: $font-size-small;

      &.keywords .keyword {
        font-size: 14px;
        padding: 2px 8px;
      }

      &.references .reference {
        font-size: 14px;
        margin-bottom: $spacing-sm;
      }
    }

    .demo-tabs {
      :deep(.el-tabs__header) {
        width: 140px;
        height: 32px;
      }

      :deep(.el-tabs__nav-wrap) {
        width: 140px;
        height: 32px;
      }

      :deep(.el-tabs__item) {
        padding: 2px 12px;
        font-size: 14px;
      }
    }
  }

  .entity-section {
    padding: 0;

    .entity-title {
      font-size: $font-size-medium;
    }

    .entity-item {
      margin-bottom: $spacing-sm;

      .entity-header {
        align-items: flex-start;
      }

      .entity-content-wrapper {
        gap: $spacing-xs;
      }

      .entity-name {
        font-size: $font-size-small;
        font-weight: 500;
        color: $primary-color;
        border-bottom: 1px solid #f0f0f0;
        padding-bottom: $spacing-xs;
        margin-bottom: $spacing-xs;
      }

      .keywords {
        .keyword {
          font-size: 12px;
          padding: 2px 6px;
          margin: 2px 4px 2px 0;
        }
      }
    }
  }

  .entity-item {
    padding: 0;
  }

  .toggle-btn {
    font-size: 12px;
    width: auto;
    padding: 4px 8px;
  }

  .source-badge {
    font-size: 12px;
    padding: 2px 8px;
  }

  .metric-card {
    .metric-row {
      flex-direction: column;
      align-items: flex-start;
      gap: $spacing-xs;
    }

    .metric-label {
      font-size: $font-size-small;
    }

    .metric-circle {
      font-size: $font-size-small;
      padding: 4px 8px;
    }
  }

  .nav-list {
    gap: $spacing-sm;

    li {
      font-size: $font-size-small;
      gap: 6px;
    }
  }
}
</style>
<style>
.swiper-scrollbar{
  display: none;
}
.swiper-pagination{
  bottom: -3px!important;
}
</style>
