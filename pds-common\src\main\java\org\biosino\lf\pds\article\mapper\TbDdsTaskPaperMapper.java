package org.biosino.lf.pds.article.mapper;

import org.apache.ibatis.annotations.Param;
import org.biosino.lf.pds.article.custbean.dto.ArticleViewQueryDTO;
import org.biosino.lf.pds.article.custbean.dto.TaskPaperQueryDTO;
import org.biosino.lf.pds.article.custbean.vo.StatInfoVO;
import org.biosino.lf.pds.article.domain.TbDdsTaskPaper;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
public interface TbDdsTaskPaperMapper extends CommonMapper<TbDdsTaskPaper> {
    /**
     * 获取 下一个任务
     */
    Long getNextExecuteTaskPaperId(TaskPaperQueryDTO dto);

    /**
     * 重试时，获取当前节点标签其他节点的未完成的任务
     */
    Long getNextExecuteTaskPaperIdByScriptlableId(TaskPaperQueryDTO dto);

    void updateNotCompleteTaskPagerStatusByDocId(@Param("docId") Long docId, @Param("completeFlag") boolean completeFlag,
                                                 @Param("changeToStatus") String changeToStatus,
                                                 @Param("notCompleteStatus") Set<String> notCompleteStatus);

    /**
     * 统计任务下的文献传递状态数量
     */
    List<StatInfoVO> statisticsTaskPaperNumByStatus(@Param("taskId") String taskId);

    List<TbDdsTaskPaper> searchTaskPaper(ArticleViewQueryDTO queryDto);

    /**
     * 批量更新任务论文表中的出版社ID（用于期刊合并）
     * 将所有匹配源出版社ID的记录更新为目标出版社ID
     *
     * @param targetPublisherId 目标出版社ID
     * @param sourcePublisherIds 源出版社ID列表
     */
    void updatePublisherIdBatch(@Param("targetPublisherId") Long targetPublisherId,
                                @Param("sourcePublisherIds") List<Long> sourcePublisherIds);

    /**
     * 批量更新任务论文表中的期刊ID（用于期刊合并）
     * 将所有匹配源期刊ID的记录更新为目标期刊ID
     *
     * @param targetJournalId  目标期刊ID
     * @param sourceJournalIds 源期刊ID列表
     */
    void updateJournalIdBatch(Long targetJournalId, List<Long> sourceJournalIds);

}
