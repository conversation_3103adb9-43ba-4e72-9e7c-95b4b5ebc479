<template>
  <div class="api-page">
    <div class="page-content">
      <!-- API Keys 管理区域 -->
      <div class="api-section">
        <el-card class="api-card">
          <template #header>
            <div class="card-header">
              <h3 class="section-title">
                <el-icon><Key /></el-icon>
                API keys
              </h3>
              <el-button type="primary" class="create-key-btn" @click="openCreateDialog">
                <el-icon class="mr-1"><Plus /></el-icon>
                创建 API key
              </el-button>
            </div>
          </template>

          <!-- 说明文字 -->
          <div class="description-section">
            <span class="description-title">列表内是你的全部 API key，API key 仅在创建时可见可复制，请妥善保存。不要与他人共享你的 API key，或将其暴露在浏览器或其他客户端代码中。</span>
          </div>

          <!-- API Keys 表格 -->
          <div class="api-table-section">
            <el-table
              :data="apiKeys"
              class="api-table"
              border
              empty-text="暂无 API Key"
            >
              <el-table-column
                prop="name"
                label="名称"
                width="280"
                show-overflow-tooltip
              >
              </el-table-column>

              <el-table-column
                prop="key"
                label="Key"
                width="400"
                show-overflow-tooltip
              >
                <template #default="{ row }">
                  <div class="key-container">
                    <span class="api-key">{{ row.key }}</span>
                    <el-tooltip content="复制密钥">
                      <el-icon class="ml-1 cursor-pointer" color="#004581"><DocumentCopy /></el-icon>
                    </el-tooltip>

                  </div>
                </template>
              </el-table-column>

              <el-table-column
                prop="createDate"
                label="创建日期"
                sortable
                width="300"
              >
                <template #default="{ row }">
                  <span class="create-date">{{ row.createDate }}</span>
                </template>
              </el-table-column>

              <el-table-column
                prop="lastUsed"
                label="最新使用日期"
                sortable
                width="300"
              >
                <template #default="{ row }">
                  <span class="last-used">{{ row.lastUsed }}</span>
                </template>
              </el-table-column>

              <el-table-column
                label="操作"
                align="center"
              >
                <template #default="{ row }">
                  <div class="action-buttons">
                    <el-button
                      type="primary"
                      size="small"
                      circle
                      class="edit-api"
                      @click="editKey(row)"
                      :icon="Edit"
                    >
                    </el-button>
                    <el-button
                      type="danger"
                      size="small"
                      circle
                      @click="deleteKey(row)"
                      :icon="Delete"
                    >
                    </el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
      </div>
    </div>

    <!-- API Key 对话框（创建/编辑通用） -->
    <el-dialog
      v-model="showDialog"
      :title="dialogTitle"
      width="500px"
      class="create-api-dialog"
      :show-close="true"
      center
    >
      <div class="dialog-content">
        <div class="form-section">
          <label class="form-label">名称</label>
          <el-input
            v-model="apiKeyName"
            placeholder="输入 API key 的名称"
            class="name-input"
            clearable
          />
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancel" class="cancel-btn">
            取消
          </el-button>
          <el-button type="primary" @click="handleConfirm" class="create-btn">
            保存
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup>
import { ref, computed } from 'vue'
import {
  Key,
  Plus,
  DocumentCopy,
  Edit,
  Delete,
  InfoFilled,
  Lock,
  Warning,
  Calendar
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// 响应式数据
const showDialog = ref(false)
const apiKeyName = ref('')
const isEditMode = ref(false)

// 计算属性
const dialogTitle = computed(() => isEditMode.value ? '修改名称' : '创建 API key')

// API Keys 数据
const apiKeys = ref([
  {
    id: 1,
    name: 'rank',
    key: 'sk-dff99************************2343',
    createDate: '2025-02-27',
    lastUsed: '2025-05-07'
  },
  {
    id: 2,
    name: '本地API',
    key: 'sk-e5a84************************807d',
    createDate: '2025-02-27',
    lastUsed: '2025-05-15'
  },
  {
    id: 3,
    name: '翻译',
    key: 'sk-4c3f7************************85f8',
    createDate: '2025-03-10',
    lastUsed: '2025-06-19'
  },
  {
    id: 4,
    name: 'dify',
    key: 'sk-33481************************96db',
    createDate: '2025-05-25',
    lastUsed: '2025-05-25'
  }
])

const editKey = (row) => {
  // 打开编辑对话框
  isEditMode.value = true
  apiKeyName.value = row.name
  showDialog.value = true
}

const deleteKey = (row) => {
  // 删除 API Key
  ElMessage.warning('删除 API Key 功能')
}

// 打开创建对话框
const openCreateDialog = () => {
  isEditMode.value = false
  apiKeyName.value = ''
  showDialog.value = true
}

// 统一的对话框方法
const handleCancel = () => {
  showDialog.value = false
  apiKeyName.value = ''
  isEditMode.value = false
}

const handleConfirm = () => {
  // 关闭对话框并重置表单
  showDialog.value = false
}
</script>
<style lang="scss" scoped>
@import "@/assets/styles/variables";

.page-content {
  display: flex;
  flex-direction: column;
  gap: $spacing-lg;
}

// API 卡片样式
.api-card {
  background: white;
  border-radius: $border-radius-lg;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04), 0 1px 4px rgba(0, 0, 0, 0.02);
  border: 1px solid rgba(226, 232, 240, 0.5);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.06), 0 2px 8px rgba(0, 0, 0, 0.04);
    transform: translateY(-1px);
  }

  :deep(.el-card__header) {
    padding: $spacing-md $spacing-lg;
    border-bottom: 1px solid rgba(226, 232, 240, 0.6);
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.8), rgba(241, 245, 249, 0.6));
  }

  :deep(.el-card__body) {
    padding: $spacing-lg;
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .section-title {
    display: flex;
    align-items: center;
    gap: $spacing-xs;
    font-size: $font-size-large;
    font-weight: $font-weight-bold;
    color: $primary-color;
    margin: 0;

    .el-icon {
      font-size: 24px;
      background: linear-gradient(135deg, $primary-color, #2563eb);
      color: white;
      padding: 6px;
      border-radius: 50%;
      box-shadow: 0 2px 8px rgba(4, 56, 115, 0.2);
    }
  }

  .create-key-btn {
    background:  $primary-color;
    border: none;
    color: white;
    font-weight: $font-weight-bold;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(4, 56, 115, 0.3);
    }
  }
}
.edit-api{
  background: #004C92;
  color: #ffffff;
  &:hover {
    background: #004c92ba;
    color: #ffffff;
  }
}

// 描述区域样式
.description-section {
  margin-bottom: $spacing-lg;
  padding: $spacing-md;
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.8), rgba(241, 245, 249, 0.6));
  border-radius: $border-radius-lg;
  border: 1px solid rgba(226, 232, 240, 0.6);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.02);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.04);
    transform: translateY(-1px);
  }

  .info-icon {
      font-size: 20px;
      color: #4FC3F7;
      background: rgba(79, 195, 247, 0.1);
      padding: 6px;
      border-radius: 50%;
  }

  .description-title {
      font-size: $font-size-small;
      color: $primary-color;
  }

}
.form-section{
  display: flex;
  padding: 20px 0;
  align-items: center;
  .form-label{
    color: #333333;
    width: 60px;
  }
}
// 表格样式
.api-table {
  :deep(.el-table__header) {
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.8), rgba(241, 245, 249, 0.6));

    th {
      background: #FAFBFD;
      color: $gray;
      font-weight: $font-weight-bold;
      font-size: $font-size-small;
      border-bottom: 2px solid rgba(226, 232, 240, 0.8);
    }
  }

}
// 响应式设计
@media (max-width: $breakpoint-lg) {
  .card-header {
    flex-direction: column;
    gap: $spacing-md;
    align-items: stretch;

    .section-title {
      justify-content: center;
    }

    .create-key-btn {
      width: 100%;
    }
  }

  .api-table {
    font-size: $font-size-small;

    :deep(.el-table__body) {
      tr td {
        padding: $spacing-xs $spacing-sm;
      }
    }
  }
}

@media (max-width: $breakpoint-md) {
  .page-content {
    gap: $spacing-md;
  }

  .api-card {
    :deep(.el-card__body) {
      padding: $spacing-md;
    }
  }

  .description-section {
    padding: $spacing-md;

    .description-header {
      flex-direction: column;
      text-align: center;
      gap: $spacing-xs;

      .description-title {
        font-size: $font-size-small;
      }
    }

    .description-content {
      grid-template-columns: 1fr;
      gap: $spacing-sm;
    }

    .description-item {
      padding: $spacing-xs;

      .item-text {
        font-size: $font-size-small;
      }
    }
  }

  .api-table {
    :deep(.el-table) {
      font-size: 12px;
    }

    .key-container {
      flex-direction: column;
      align-items: stretch;
      gap: $spacing-xxs;

      .copy-btn {
        align-self: flex-end;
      }
    }

    .action-buttons {
      flex-direction: column;
      gap: $spacing-xxs;
    }
  }
}

// 响应式对话框
@media (max-width: $breakpoint-md) {
  :deep(.create-api-dialog) {
    .el-dialog {
      width: 90% !important;
      margin: 5vh auto;
    }

    .el-dialog__body {
      padding: $spacing-md;
    }
  }

  .dialog-footer {
    flex-direction: column;

    .cancel-btn,
    .create-btn {
      width: 100%;
      margin: 0;
    }
  }
}
</style>