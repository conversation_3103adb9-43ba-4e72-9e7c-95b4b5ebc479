<template>
  <div class="collect-page">
    <div class="collect-content">
      <!-- 左侧文件夹列表 -->
      <div class="left-sidebar">
        <div class="folders-card">
          <!-- 文件夹列表 -->
          <div class="folders-list">
            <div
                v-for="folder in folders"
                :key="folder.id"
                :class="['folder-item', { active: selectedFolderId === folder.id }]"
                @click="selectFolder(folder.id)"
            >
              <div class="folder-info">
                <el-icon class="folder-icon">
                  <Folder />
                </el-icon>
                <span class="folder-name">{{ folder.name }}</span>
              </div>
              <span class="folder-count">{{ folder.count }}</span>
            </div>
          </div>

          <!-- 新建收藏夹按钮 -->
          <div class="add-folder-footer">
            <el-button
                class="add-folder-btn"
                @click="showAddFolderDialog = true"
            >
              <el-icon class="mr-1"><Plus /></el-icon>
              新建收藏夹
            </el-button>
          </div>
        </div>
      </div>

      <!-- 右侧文献列表 -->
      <div class="right-content">
        <div class="literature-card">
          <!-- 工具栏 -->
          <div class="toolbar">
            <div class="toolbar-left">
              <el-radio-group v-model="sort" size="large">
                  <el-radio-button label="最近收藏" value="最近收藏" />
                  <el-radio-button label="最多阅读" value="最多阅读" />
                  <el-radio-button label="最多下载" value="最多下载" />
                </el-radio-group>
            </div>

            <div class="toolbar-right">
              <div class="select-all-section">
                <el-checkbox v-model="selectAll" @change="handleSelectAll">
                  全选
                </el-checkbox>
                <el-button
                    type="text"
                    size="small"
                    @click="showSearchPanel = !showSearchPanel"
                    class="filter-btn"
                >
                  <el-icon><Filter /></el-icon>
                </el-button>
              </div>

              <div class="batch-actions">
                <el-button type="primary" plain>批量导出</el-button>
                <el-button type="danger" plain>取消收藏</el-button>
              </div>
            </div>
          </div>

          <el-collapse-transition>
          <!-- 搜索面板 -->
          <div v-show="showSearchPanel" class="search-panel">
            <div class="search-row">
              <div class="search-field">
                <label class="search-label">文献ID</label>
                <el-input
                    v-model="searchFilters.id"
                    placeholder="请输入文献ID"
                    class="search-input"
                />
              </div>
              <div class="search-field">
                <label class="search-label">文献标题</label>
                <el-input
                    v-model="searchFilters.title"
                    placeholder="请输入文献标题"
                    class="search-input"
                />
              </div>
              <div class="search-field">
                <label class="search-label">作者</label>
                <el-input
                    v-model="searchFilters.author"
                    placeholder="请输入作者"
                    class="search-input"
                />
              </div>
            </div>
            <div class="search-row">
              <div class="search-field">
                <label class="search-label">关键字</label>
                <el-input
                    v-model="searchFilters.keywords"
                    placeholder="请输入关键字"
                    class="search-input"
                />
              </div>
              <div class="search-field">
                <label class="search-label">发表时间</label>
                <el-select
                    v-model="searchFilters.publishTime"
                    placeholder="请选择发表时间"
                    class="search-input"
                >
                  <el-option label="不限" value="" />
                  <el-option label="近1年" value="1year" />
                  <el-option label="近3年" value="3years" />
                  <el-option label="近5年" value="5years" />
                </el-select>
              </div>
              <div class="search-field">
                <el-button type="primary" class="search-btn">
                  <el-icon class="mr-1"><Search /></el-icon>
                  搜索
                </el-button>
              </div>
            </div>
          </div>
          </el-collapse-transition>

          <!-- 文献列表 -->
          <div class="literature-list">
            <div
                v-for="article in filteredArticles"
                :key="article.id"
                class="literature-item"
            >
              <div class="literature-checkbox">
                <el-checkbox
                    :v-model="selectedArticles.includes(article.id)"
                    @change="toggleArticleSelection(article.id)"
                />
              </div>

              <div class="literature-content">
                <!-- 标题行 -->
                <div class="title-row">
                  <span class="source-tag" :class="article.source.toLowerCase()">{{ article.source }}</span>
                  <h4 class="literature-title">{{ article.title }}</h4>
                </div>

                <!-- 作者 -->
                <div class="literature-authors">
                  {{ article.authors }}
                </div>

                <!-- 期刊信息和标识符 -->
                <div class="literature-meta-line">
                  <div class="literature-meta-line mb-0">
                    <div class="journal-info">
                      <span class="journal-name">{{ article.journal }}</span>
                      <span class="publication-year">{{ article.year }}</span>
                      <span class="volume-info">{{ article.volume }}({{ article.issue }}):{{ article.pages }}</span>
                    </div>
                    <div class="literature-ids">
                    <span v-if="article.pmid" class="id-tag pmid-tag">
                      PMID: {{ article.pmid }}
                    </span>
                      <span v-if="article.doi" class="id-tag doi-tag">
                      DOI: {{ article.doi }}
                    </span>
                    </div>
                  </div>
                  <div class="collect-time">{{ article.collectTime }}</div>
                </div>

                <!-- 描述 -->
                <div class="literature-description">
                  {{ article.description }}
                </div>
              </div>
            </div>
          </div>
          <!-- 分页 -->
          <div class="pagination-wrapper">
            <el-pagination
                v-model:current-page="pagination.currentPage"
                v-model:page-size="pagination.pageSize"
                :page-sizes="[10, 20, 50, 100]"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                class="pagination"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 新建文件夹对话框 -->
    <el-dialog
        v-model="showAddFolderDialog"
        title="新建收藏夹"
        width="400px"
    >
      <el-form :model="newFolder" label-width="80px">
        <el-form-item label="文件夹名">
          <el-input v-model="newFolder.name" placeholder="请输入文件夹名称" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAddFolderDialog = false">取消</el-button>
          <el-button type="primary" @click="createFolder">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed,reactive } from 'vue'
import {
  Folder,
  Plus,
  Filter,
  Search,
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// 响应式数据
const selectedFolderId = ref('default')
const selectAll = ref(false)
const selectedArticles = ref([])
const showSearchPanel = ref(false)
const showAddFolderDialog = ref(false)
const sort=ref('最近收藏')
const total=ref(10)
// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 20
})
// 搜索过滤器
const searchFilters = ref({
  id: '',
  title: '',
  author: '',
  keywords: '',
  publishTime: ''
})

// 新建文件夹表单
const newFolder = ref({
  name: ''
})

// 文件夹数据
const folders = ref([
  {
    id: 'default',
    name: '默认收藏夹',
    count: 8
  },
  {
    id: 'xxx',
    name: 'xxx收藏夹',
    count: 1
  }
])

// 文献数据
const articles = ref([
  {
    id: 1,
    source: 'PubMed',
    title: '新型冠状病毒感染中医药防治的临床研究进展及其机制分析',
    authors: 'Zhang L, Wang Y, Li J, Wang K, Chen M, Sun E',
    description: '本研究通过系统性回顾和荟萃分析，探讨了中医药在新型冠状病毒感染防治中的临床应用效果及其潜在机制，为中西医结合治疗提供了重要的理论依据和实践指导。',
    journal: 'Nature',
    year: '2024',
    volume: '615',
    issue: '7952',
    pages: '456-468',
    doi: '10.1038/s41586-024-07123-4',
    pmid: '38234567',
    collectTime: '2024-01-15',
    folderId: 'default'
  },
  {
    id: 2,
    source: 'PMC',
    title: 'PD-1/PD-L1免疫检查点抑制剂在非小细胞肺癌中的应用及其耐药机制',
    authors: 'Chen H, Liu Y, Yang R, Zhao W, Wang J, Dong Z',
    description: '深入分析了PD-1/PD-L1免疫检查点抑制剂在非小细胞肺癌治疗中的作用机制，重点探讨了耐药性产生的分子基础和潜在的克服策略。',
    journal: 'Nature',
    year: '2024',
    volume: '615',
    issue: '7952',
    pages: '123-135',
    doi: '10.1038/s41586-024-07456-7',
    pmid: '38345678',
    collectTime: '2024-01-14',
    folderId: 'default'
  },
  {
    id: 3,
    source: 'BioRxiv',
    title: '基于深度学习的医学影像诊断技术在早期癌症检测中的应用研究',
    authors: 'Li Q, Wu J, Xu L, Huang P, Zhang J',
    description: '利用先进的深度学习算法开发了一套高精度的医学影像分析系统，能够有效识别早期癌症病变，为临床诊断提供了强有力的技术支持。',
    journal: 'Science',
    year: '2024',
    volume: '383',
    issue: '6630',
    pages: '789-801',
    doi: '10.1126/science.abcd5678',
    pmid: '38456789',
    collectTime: '2024-01-13',
    folderId: 'xxx'
  },
  {
    id: 4,
    source: 'PubMed',
    title: '新型冠状病毒感染中医药防治的临床研究进展及其机制分析',
    authors: 'Zhang L, Wang Y, Li J, Wang K, Chen M, Sun E',
    description: '本研究通过系统性回顾和荟萃分析，探讨了中医药在新型冠状病毒感染防治中的临床应用效果及其潜在机制，为中西医结合治疗提供了重要的理论依据和实践指导。',
    journal: 'Nature',
    year: '2024',
    volume: '615',
    issue: '7952',
    pages: '456-468',
    doi: '10.1038/s41586-024-07123-4',
    pmid: '38234567',
    collectTime: '2024-01-15',
    folderId: 'default'
  },
])

// 计算属性
const filteredArticles = computed(() => {
  let filtered = articles.value.filter(article => article.folderId === selectedFolderId.value)
  return filtered
})

// 选择文件夹
const selectFolder = (folderId) => {
  selectedFolderId.value = folderId
  selectedArticles.value = []
  selectAll.value = false
}

const handleSelectAll = (checked) => {
  if (checked) {
    selectedArticles.value = filteredArticles.value.map(article => article.id)
  } else {
    selectedArticles.value = []
  }
}

const toggleArticleSelection = (articleId) => {
  const index = selectedArticles.value.indexOf(articleId)
  if (index > -1) {
    selectedArticles.value.splice(index, 1)
  } else {
    selectedArticles.value.push(articleId)
  }

  // 更新全选状态
  selectAll.value = selectedArticles.value.length === filteredArticles.value.length
}



const createFolder = () => {
  if (!newFolder.value.name.trim()) {
    ElMessage.warning('请输入文件夹名称')
    return
  }

  const newId = Date.now().toString()
  folders.value.push({
    id: newId,
    name: newFolder.value.name,
    count: 0
  })

  newFolder.value.name = ''
  showAddFolderDialog.value = false
  ElMessage.success('创建成功')
}

</script>

<style lang="scss" scoped>
@import "@/assets/styles/variables";

.collect-page {
  padding: 0;
}

.collect-content {
  display: flex;
  gap: $spacing-xl;
  align-items: stretch;

  @media (max-width: $breakpoint-lg) {
    flex-direction: column;
    gap: $spacing-lg;
  }
}

.left-sidebar {
  flex: 0 0 280px;

  @media (max-width: $breakpoint-lg) {
    flex: none;
    width: 100%;
  }
}

.right-content {
  flex: 1;
  min-width: 0;
}

// 文件夹卡片样式
.folders-card {
  background: white;
  border-radius: $border-radius-lg;
  display: flex;
  flex-direction: column;
  height: 100%;
}

// 文献卡片样式
.literature-card {
  background: white;
  border-radius: $border-radius-lg;
  padding: $spacing-md $spacing-lg;
  border: 1px solid rgba(226, 232, 240, 0.5);
}


// 新建收藏夹底部
.add-folder-footer {
  border-top: 1px solid rgba(226, 232, 240, 0.6);
}
//分页
.pagination-wrapper{
  margin-top: 20px;
  .el-pagination{
    justify-content: center;

  }
}
// 文件夹列表样式
.folders-list {
  display: flex;
  flex-direction: column;
  gap: $spacing-xs;
  flex: 1;
}

.folder-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: $spacing-sm $spacing-md;
  border-radius: $border-radius-md;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 16px;


  &:hover {
    background: #E5EBF1;
    border-color: rgba(148, 163, 184, 0.4);
  }

  &.active {
    background: #003D71;
    color: white;
    border-color: #003D71;

    .folder-name {
      color: white;
      font-size: 16px;
    }

    .folder-icon {
      color: white!important;
    }

    .folder-count {
      background: rgba(255, 255, 255, 0.2);
      color: white;
    }
  }

  .folder-info {
    display: flex;
    align-items: center;
    gap: $spacing-xs;

    .folder-icon {
      font-size: 18px;
      color:#003D71;
      transition: color 0.3s ease;
    }

  }

  .folder-count {
    background: rgba(148, 163, 184, 0.2);
    color: $gray;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: $font-size-small;
    font-weight: $font-weight-bold;
    min-width: 24px;
    text-align: center;
    transition: all 0.3s ease;
  }
}

.add-folder-btn {
  width: 100%;
  background:#ffffff;
  border: none;
  color: #00416F;
  font-weight: $font-weight-bold;
  padding: $spacing-md $spacing-lg;
  border-radius: $border-radius-md;
  transition: all 0.3s ease;
  height: 44px;

  &:hover {
    background: #e3ebf1;
    color: #003d71;
    transform: translateY(-1px);
    //box-shadow: 0 4px 12px rgba(79, 195, 247, 0.3);
  }
}

// 工具栏样式
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-md;
  padding-bottom: $spacing-sm;
  border-bottom: 1px solid rgba(226, 232, 240, 0.6);
  flex-wrap: wrap;
  gap: $spacing-sm;

  @media (max-width: $breakpoint-md) {
    flex-direction: column;
    align-items: stretch;
  }
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: $spacing-md;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: $spacing-md;
  flex-wrap: wrap;

  @media (max-width: $breakpoint-md) {
    justify-content: space-between;
  }
}

.toolbar-left {
  .el-radio-button.is-active{
    :deep(.el-radio-button__inner){
      background: #2E4E73;
      color: #ffffff;
    }
  }
}

.select-all-section {
  display: flex;
  align-items: center;
  gap: $spacing-xs;

  .filter-btn {
    padding: 6px;
    color: $primary-color;

    &:hover {
      background: rgba(4, 56, 115, 0.1);
    }
  }
}

.batch-actions {
  display: flex;
  align-items: center;
  gap: $spacing-xs;

  .selected-count {
    font-size: $font-size-small;
    color: $primary-color;
    font-weight: $font-weight-medium;
  }
  .el-button:first-child{
    background: #E4EBF1;
    color: $primary-color;
    border: 1px solid #00416f38;
    &:hover{
      background: #00416f;
      color: #ffffff;
      border: 1px solid #00416f;
    }
  }
}

// 搜索面板样式
.search-panel {
  background: rgba(241, 245, 249, 0.5);
  border: 1px solid rgba(226, 232, 240, 0.6);
  border-radius: $border-radius-md;
  padding: $spacing-lg;
  margin-bottom: $spacing-md;

  .search-row {
    display: flex;
    gap: $spacing-lg;
    margin-bottom: $spacing-md;
    align-items: flex-end;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .search-field {
    flex: 1;
    min-width: 150px;

    .search-label {
      display: block;
      font-size: $font-size-small;
      color: $gray;
      font-weight: $font-weight-medium;
      margin-bottom: $spacing-xs;
    }

    .search-input {
      width: 100%;
    }

    .search-btn {
      background: #E4EBF1;
      color: #00416f;
      border: 1px solid #00416f38;
      height: 40px;
      padding: 0 $spacing-lg;
      transition: all 0.3s ease;
      &:hover{
        background: #00416f;
        color: #ffffff;
        border: 1px solid #00416f;
      }
    }
  }
}

// 文献列表样式
.literature-list {
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;
}

.literature-item {
  display: flex;
  gap: $spacing-sm;
  padding: $spacing-sm $spacing-md;
  border: 1px solid rgba(226, 232, 240, 0.6);
  border-radius: $border-radius-md;
  background: white;
  transition: all 0.3s ease;

  &:hover {
    border-color: rgba(148, 163, 184, 0.4);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
  }

  .literature-checkbox {
    flex-shrink: 0;
    display: flex;
    align-items: flex-start;
    padding-top: 2px;
  }

  .literature-content {
    flex: 1;
    min-width: 0;
  }
}
.title-row {
  display: flex;
  align-items: flex-start;
  gap: $spacing-xs;
  margin-bottom: $spacing-xs;
}

.source-tag {
  flex-shrink: 0;
  padding: 2px 14px;
  border-radius: 10px;
  font-size: 14px;
  font-weight: $font-weight-bold;
  margin-top: 2px;

  &.pubmed {
    background: #E3F2FD;
    color: #1976D2;
  }

  &.pmc {
    background: #F3E5F5;
    color: #7B1FA2;
  }

  &.biorxiv {
    background: #E8F5E8;
    color: #388E3C;
  }
}
// 表格样式
  :deep(.el-table__header) {
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.8), rgba(241, 245, 249, 0.6));

    th {
      background: #FAFBFD;
      color: $gray;
      font-weight: $font-weight-bold;
      font-size: $font-size-small;
      border-bottom: 2px solid rgba(226, 232, 240, 0.8);
    }
  }


.literature-title {
  font-size: $font-size-medium;
  font-weight: $font-weight-bold;
  color: $primary-color;
  margin: 0;
  line-height: 1.5;
  cursor: pointer;
  transition: all 0.3s ease;
  flex: 1;

  &:hover {
    color: #2563eb;
  }
}

.literature-description {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.literature-authors {
  font-size: $font-size-small;
  color: #575757;
  margin-bottom: $spacing-xs;
  line-height: 1.4;
  font-weight: $font-weight-medium;
}

.literature-meta-line {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: $spacing-md;
  font-size: 14px;
  color: #374151;
  margin-bottom: $spacing-xxs;

  .journal-info {
    display: flex;
    align-items: center;
    gap: $spacing-xs;
    font-size: $font-size-small;

    .publication-year {
      color: #6b7280;
    }

    .volume-info {
      color: #6b7280;
    }
  }

  .literature-ids {
    display: flex;
    align-items: center;
    gap: $spacing-xs;
    flex-shrink: 0;

    .id-tag {
      display: inline-flex;
      align-items: center;
      padding: 3px 8px;
      border-radius: 12px;
      transition: all 0.3s ease;
      cursor: pointer;
      white-space: nowrap;

      &.pmid-tag,
      &.doi-tag {
        background: #F2F7FB;
        color: #374151;

        &:hover {
          background: #dae8fa;
        }
      }
    }
  }

  .collect-time {
    font-size: $font-size-small;
    color: #6b7280;
    flex-shrink: 0;
  }
}

// 响应式调整
@media (max-width: $breakpoint-md) {
  .collect-content {
    gap: $spacing-md;
  }

  .left-sidebar {
    flex: none;
    width: 100%;
  }

  .section-card {
    padding: $spacing-sm $spacing-md;
  }

  .literature-item {
    padding: $spacing-xs $spacing-sm;
    flex-direction: column;
    gap: $spacing-xs;

    .literature-checkbox {
      align-self: flex-start;
    }
  }

  .literature-content {
    .title-row {
      flex-direction: column;
      gap: $spacing-xxs;
    }

    .literature-title {
      font-size: $font-size-small;
    }

    .literature-authors {
      font-size: 14px;
      margin-bottom: $spacing-xxs;
    }

    .literature-meta-line {
      flex-direction: column;
      align-items: flex-start;
      gap: $spacing-xs;

      .journal-info {
        gap: 2px;
      }

      .literature-ids {
        gap: $spacing-xxs;
        flex-wrap: wrap;
        width: 100%;

        .id-tag {
          padding: 1px 6px;
        }

        .collect-time {
          font-size: 10px;
        }
      }
    }
  }

  .search-panel {
    .search-row {
      flex-direction: column;
      gap: $spacing-sm;

      .search-field {
        min-width: 100%;
      }
    }
  }



  .toolbar {
    .toolbar-left,
    .toolbar-right {
      width: 100%;
      justify-content: space-between;
    }

    .sort-buttons {
      flex-wrap: wrap;
    }

    .batch-actions {
      flex-wrap: wrap;
      justify-content: flex-start;
    }
  }
}

// 对话框样式调整
.el-dialog {
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: $spacing-sm;
  }
}
</style>