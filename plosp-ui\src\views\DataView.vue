<template>
  <div class="data">
    <section class="content-section">
      <div class="container">
          <!-- 左侧接口导航 -->
          <el-row :gutter="30" class="data-content">
            <el-col :span="5" :xs="24">
              <div class="api-navigation">
                <div class="nav-header">
                  <el-icon><Menu /></el-icon>
                  <h2>接口导航</h2>
                </div>

                <div class="nav-list">
                  <div
                      v-for="(navItem, index) in navItems"
                      :key="navItem.id"
                      class="nav-item"
                      :class="{ active: activeNavItem === navItem.id }"
                      @click="scrollToSection(navItem.id)"
                  >
                    <div class="nav-item-bg"></div>
                    <div class="nav-item-content">
                      <span>{{ navItem.label }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :span="19" :xs="24">
              <div class="api-docs">
                <!-- 文献下载接口 -->
                <div class="api-doc-item" id="literature-download">
                  <div class="api-header">
                    <div class="api-title">
                      <img src="@/assets/images/literature-download.svg"/>
                      <h2>文献下载接口</h2>
                    </div>
                    <div class="api-method">
                      <span>GET</span>
                    </div>
                  </div>

                  <div class="api-endpoint">
                    <code>/api/v1/download/{id}</code>
                  </div>

                  <h3 class="api-section-title">参数说明</h3>
                  <div class="api-params">
                    <div class="param-row">
                      <img src="@/assets/images/right.svg"/>
                      <div class="param-name">ID</div>
                      <div class="param-desc">: PMID或DOI</div>
                    </div>

                    <div class="param-row">
                      <img src="@/assets/images/right.svg"/>
                      <div class="param-name">Format</div>
                      <div class="param-desc">: 可选,返回格式,支持pdf、xml</div>
                    </div>
                  </div>

                  <h3 class="api-section-title">示例</h3>

                  <div class="api-example">
                    <div class="example-code">
                      GET /api/v1/download/10.1038/nature14539
                    </div>
                  </div>
                </div>

                <!-- 期刊信息接口 -->
                <div class="api-doc-item" id="journal-info">
                  <div class="api-header">
                    <div class="api-title">
                      <img src="@/assets/images/journal-info.svg"/>
                      <h2>期刊信息接口</h2>
                    </div>
                    <div class="api-method">
                      <span>GET</span>
                    </div>
                  </div>

                  <div class="api-endpoint">
                    <code>/api/v1/journal/{id}</code>
                  </div>

                  <h3 class="api-section-title">参数说明</h3>

                  <div class="api-params">
                    <div class="param-row">
                      <img src="@/assets/images/right.svg"/>
                      <div class="param-name">ID</div>
                      <div class="param-desc">: ISSN或期刊名称</div>
                    </div>

                    <div class="param-row">
                      <img src="@/assets/images/right.svg"/>
                      <div class="param-name">Year</div>
                      <div class="param-desc">: 可选，年份，获取特定年份的期刊指标</div>
                    </div>
                  </div>

                  <h3 class="api-section-title">示例</h3>

                  <div class="api-example">
                    <div class="example-code">
                      GET /api/v1/journal/0028-0836?year=2023
                    </div>
                  </div>
                </div>

                <!-- 认证说明 -->
                <div class="api-doc-item" id="authentication">
                  <div class="api-header">
                    <div class="api-title">
                      <img src="@/assets/images/authentication.svg"/>
                      <h2>认证说明</h2>
                    </div>
                  </div>

                  <div class="api-content">
                    <p class="mt-0">所有API请求都需要在Header中包含API密钥：</p>
                    <div class="api-endpoint">
                      <code>/api/v1/download/{id}</code>
                    </div>
                    <p>您可以通过以下方式获取API密钥：</p>
                    <p class="api-steps">
                      1.注册账号并登录；<br>
                      2.进入个人中心；<br>
                      3.在"API管理"页面申请API密钥。
                    </p>
                  </div>
                </div>

                <!-- 使用限制 -->
                <div class="api-doc-item" id="usage-limits">
                  <div class="api-header">
                    <div class="api-title">
                      <img src="@/assets/images/permission.svg"/>
                      <h2>使用限制</h2>
                    </div>
                  </div>

                  <div class="api-content">
                    <div class="limit-row">
                      <img src="@/assets/images/right.svg"/>
                      <div>普通用户：每秒上限3次请求</div>
                    </div>

                    <div class="limit-row">
                      <img src="@/assets/images/right.svg"/>
                      <div>高级用户：每秒上限100次请求</div>
                    </div>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>

      </div>
    </section>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { Menu, Download, Reading, Lock, Warning, CircleCheck } from '@element-plus/icons-vue'

// 导航数据
const navItems = ref([
  { id: 'literature-download', label: '1.文献下载接口' },
  { id: 'journal-info', label: '2.期刊信息接口' },
  { id: 'authentication', label: '3.认证说明' },
  { id: 'usage-limits', label: '4.使用限制' }
])

// 当前激活的导航项
const activeNavItem = ref('literature-download')


// 滚动到指定区域
const scrollToSection = (sectionId) => {
  const element = document.getElementById(sectionId)

  if (element) {
    // 计算元素位置，考虑固定导航的高度
    const headerOffset = 120 // 根据固定导航的位置调整
    const elementPosition = element.offsetTop
    const offsetPosition = elementPosition - headerOffset

    // 平滑滚动到目标位置
    window.scrollTo({
      top: offsetPosition,
      behavior: 'smooth'
    })

    // 更新激活状态
    activeNavItem.value = sectionId
  }
}

// 监听页面滚动事件，更新激活状态
const handleScroll = () => {
  const scrollPosition = window.scrollY + 200 // 偏移量，提前激活

  // 找到当前可见的区域
  for (let i = navItems.value.length - 1; i >= 0; i--) {
    const element = document.getElementById(navItems.value[i].id)
    if (element && element.offsetTop <= scrollPosition) {
      activeNavItem.value = navItems.value[i].id
      break
    }
  }
}

const throttle = (func, limit) => {
  let inThrottle
  return function() {
    const args = arguments
    const context = this
    if (!inThrottle) {
      func.apply(context, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

const throttledHandleScroll = throttle(handleScroll, 100)

onMounted(() => {
  window.addEventListener('scroll', throttledHandleScroll)
})

onUnmounted(() => {
  window.removeEventListener('scroll', throttledHandleScroll)
})
</script>

<style lang="scss" scoped>
@import "@/assets/styles/variables";

.data {
  background-color: #F5F5F5;
  min-height: calc(100vh - 92px); // 减去头部高度
  padding: $spacing-xxl 0;
}

.page-title {
  font-size: $font-size-xxlarge;
  font-weight: $font-weight-bold;
  color: $primary-color;
  margin-bottom: $spacing-xl;
}
// 左侧接口导航
.api-navigation {
  width: 295px;
  background-color: $white;
  border-radius: $border-radius-lg;
  box-shadow: $box-shadow;
  padding: $spacing-lg;
  position: fixed; // 固定定位
  top: 129px; // 距离顶部的距离
}

.nav-header {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  margin-bottom: $spacing-lg;

  h2 {
    font-size: $font-size-medium;
    font-weight: $font-weight-bold;
    color: $primary-color;
    margin: 0;
  }

  .nav-icon {
    font-size: $font-size-large;
    color: $primary-color;
  }
}

.nav-list {
  display: flex;
  flex-direction: column;
  gap: $spacing-xs;
}

.nav-item {
  position: relative;
  height: 50px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    .nav-item-bg {
      background-color: rgba($primary-color, 0.1);
    }
  }

  &.active {
    .nav-item-bg {
      background-color: $primary-color;
    }

    .nav-item-content {
      color: $white;
    }

    &:hover {
      .nav-item-bg {
        background-color: $primary-color;
      }
    }
  }
}

.nav-item-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: $border-radius-md;
  z-index: 1;
  transition: background-color 0.3s ease;
}

.nav-item-content {
  position: relative;
  z-index: 2;
  height: 100%;
  display: flex;
  align-items: center;
  padding-left: $spacing-lg;
  font-size: $font-size-small;
  font-weight: $font-weight-regular;
  transition: color 0.3s ease;
}

// 右侧API文档
.api-docs {
  display: flex;
  flex-direction: column;
  gap: $spacing-lg;
}

.api-doc-item {
  background-color: $white;
  border-radius: $border-radius-lg;
  box-shadow: $box-shadow;
  padding: $spacing-lg;
  border-top: 1px solid #F3F4F6;
  flex-shrink: 0; // 防止在flex容器中被压缩
  scroll-margin-top: 20px; // 为滚动定位预留空间
}

.api-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: $spacing-md;
}

.api-title {
  display: flex;
  align-items: center;
  gap: $spacing-xs;

  h2 {
    font-size: $font-size-medium;
    font-weight: $font-weight-bold;
    color: $primary-color;
    margin: 0;
  }

  .api-icon {
    font-size: $font-size-large;
    color: $primary-color;
  }
  img{
    width: 22px;
  }
}

.api-method {
  background-color: #DCFCE7;
  padding: 2px 16px;
  border-radius: 9999px;

  span {
    color: #166534;
    font-size: $font-size-small;
    font-weight: $font-weight-regular;
  }
}

.api-endpoint {
  background-color: #F9FAFB;
  padding: $spacing-xs  $spacing-md;
  border-radius: $border-radius-md;
  margin-bottom: $spacing-lg;

  code {
    font-family: monospace;
    font-size: $font-size-small;
    color: #1F2937;
  }
}

.api-section-title {
  font-size: $font-size-small;
  font-weight: $font-weight-medium;
  color: #374151;
  margin-bottom: $spacing-md;
}

.api-params {
  margin-bottom: $spacing-lg;
}

.param-row, .limit-row {
  display: flex;
  align-items: center;
  margin-bottom: $spacing-xs;
  color: #4B5563;
  font-size: $font-size-small;
  img{
    margin-right: .5rem;
    width: 12px;
  }
}

.param-icon, .limit-icon {
  width: 16px;
  height: 16px;
  margin-right: $spacing-xs;
  color: #333333;
}

.param-name {
  font-size: $font-size-small;
  font-weight: $font-weight-regular;
  color: #4B5563;
  margin-right: 4px;
}

.param-desc {
  font-size: $font-size-small;
  font-weight: $font-weight-regular;
  color: #4B5563;
}

.api-example {
  background-color: #F9FAFB;
  padding:$spacing-xs $spacing-md;
  border-radius: $border-radius-md;

  .example-code {
    font-family: monospace;
    font-size: $font-size-small;
    color: #2563EB;
  }
}

.api-content {
  p {
    font-size: $font-size-small;
    color: #4B5563;
    margin-bottom: $spacing-sm;
  }

  .api-steps {
    font-size: $font-size-small;
    color: #4B5563;
    line-height: 1.8;
  }
}

// 响应式样式
@media (max-width: $breakpoint-lg) {
  .data-content {
    flex-direction: column;
    gap: 20px;
  }

  .api-navigation {
    position: relative; // 移动端取消固定定位
    top: auto;
    transform: none;
    width: 100%;

  }

  .api-docs {
    padding-left: 0; // 移动端移除左侧间距
    //max-width: 100%;
  }
}

@media (max-width: $breakpoint-md) {
  .page-title {
    font-size: $font-size-xlarge;
  }

  .api-title {
    h2 {
      font-size: $font-size-large;
    }
  }

  .api-endpoint {
    code {
      font-size: $font-size-small;
    }
  }

  .param-name, .param-desc {
    font-size: $font-size-small;
  }

  .example-code {
    font-size: $font-size-small;
  }
}

@media (max-width: $breakpoint-sm) {
  .data {
    padding: $spacing-lg 0;
  }

  .container {
    padding: 0 $spacing-sm;
  }

  .api-header {
    gap: $spacing-sm;
  }

  .nav-item-content {
    font-size: $font-size-small;
  }

  .api-content {
    p, .api-steps {
      font-size: $font-size-small;
    }
  }
}
</style>
