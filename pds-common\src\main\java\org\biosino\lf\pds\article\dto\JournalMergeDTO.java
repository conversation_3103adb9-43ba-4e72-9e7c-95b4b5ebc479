package org.biosino.lf.pds.article.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
public class JournalMergeDTO {
    /**
     * 目标期刊id
     */
    @NotNull
    private Long targetId;

    /**
     * 源出版社id
     */
    @NotNull
    @NotEmpty
    private List<Long> sourceIds;

    /**
     * 合并期刊名称
     */
    @NotBlank
    private String title;

    /**
     * 合并期刊简称
     */
    private String isoabbreviation;

    /**
     * 合并出版社名称
     */
    private String publisherName;

    /**
     * 合并出版社id
     */
    private Long publisherId;

    /**
     * 合并issn print
     */
    private String issnPrint;

    /**
     * 合并issn electronic
     */
    private String issnElectronic;

    /**
     * 合并Unique NLM ID
     */
    private String uniqueNlmId;

    /**
     * 合并历史issn
     */
    private List<String> issnHistory;

    /**
     * 合并历史Unique NLM ID
     */
    private List<String> uniqueHistory;

    /**
     * 合并medlineTA
     */
    private String medlineTa;

    /**
     * 合并来源
     */
    private List<String> source;

    /**
     * 合并脚本ID
     */
    private Long scriptId;

}
