package org.biosino.lf.pds.task.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.FastDateFormat;
import org.biosino.lf.pds.article.custbean.dto.FileUploadDTO;
import org.biosino.lf.pds.article.custbean.dto.TaskPaperQueryDTO;
import org.biosino.lf.pds.article.custbean.dto.api.HandshakeDTO;
import org.biosino.lf.pds.article.custbean.dto.api.NoticeApiDTO;
import org.biosino.lf.pds.article.custbean.dto.api.ScriptInfoDTO;
import org.biosino.lf.pds.article.custbean.vo.ScriptVO;
import org.biosino.lf.pds.article.custbean.vo.api.ApiResultVO;
import org.biosino.lf.pds.article.custbean.vo.api.SiteInfoVO;
import org.biosino.lf.pds.article.domain.*;
import org.biosino.lf.pds.article.mapper.*;
import org.biosino.lf.pds.article.service.ITbDdsFileService;
import org.biosino.lf.pds.common.config.AppConfig;
import org.biosino.lf.pds.common.core.domain.AjaxResult;
import org.biosino.lf.pds.common.enums.DirectoryEnum;
import org.biosino.lf.pds.common.enums.StatusEnums;
import org.biosino.lf.pds.common.enums.task.*;
import org.biosino.lf.pds.common.exception.ServiceException;
import org.biosino.lf.pds.common.utils.CompressUtil;
import org.biosino.lf.pds.common.utils.MyHashUtil;
import org.biosino.lf.pds.common.utils.PDFHelper;
import org.biosino.lf.pds.task.config.PdsAppConfig;
import org.biosino.lf.pds.task.service.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 小蚂蚁请求接口api业务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TaskApiServiceImpl extends ServiceImpl<TbDdsTaskScheduleMapper, TbDdsTaskSchedule> implements ITaskApiService {

    private final ITbDdsHandshakeService tbDdsHandshakeService;
    private final ITbDdsTaskService tbDdsTaskService;
    private final ITbDdsJournalScriptService tbDdsJournalScriptService;
    private final ITbDdsSiteService tbDdsSiteService;
    private final ITbDdsScriptlabelService tbDdsScriptlabelService;
    private final ITbDdsFileService tbDdsFileService;

    private final TbDdsTaskLogMapper tbDdsTaskLogMapper;
    private final TbDdsTaskPaperMapper tbDdsTaskPaperMapper;
    private final TbDdsTaskLinkMapper tbDdsTaskLinkMapper;
    private final TbDdsTaskScheduleMapper tbDdsTaskScheduleMapper;
    private final ArticleMapper articleMapper;
    private final JournalMapper journalMapper;
    private final ArticleOtherIdMapper articleOtherIdMapper;
    private final TbDdsJournalScriptMapper tbDdsJournalScriptMapper;

    private static final String NO_TASK = "没有可运行的任务";

    @Override
    public ApiResultVO sendHandshake(HandshakeDTO dto, HttpServletRequest sysInfo) {
        return tbDdsHandshakeService.sendHandshake(dto, sysInfo);
    }

    @Override
    public synchronized JSONObject getNextTaskInfo(String siteIdStr, String startPmidStr, String endPmidStr, final String activeScriptIds) {
        if (StrUtil.isBlank(siteIdStr)) {
            return initTaskInfo("error", "请指定siteId");
        }
        try {
            final int siteId = Integer.parseInt(siteIdStr);
            final boolean startPmidEmpty = StrUtil.isBlank(startPmidStr);
            final boolean endPmidEmpty = StrUtil.isBlank(endPmidStr);

            Long startPmid;
            Long endPmid;
            if ((startPmidEmpty && endPmidEmpty) || (!startPmidEmpty && !endPmidEmpty)) {
                // 条件满足：两者都为空 或 都不为空
                startPmid = startPmidEmpty ? null : Long.valueOf(startPmidStr);
                endPmid = endPmidEmpty ? null : Long.valueOf(endPmidStr);
            } else {
                // 条件不满足：一空一非空
                throw new ServiceException("startPmid和endPmid必须同时为空或同时不为空");
            }


            final Map<String, Object> objectMap = generateNextTaskInfo(siteId, startPmid, endPmid, activeScriptIds);
            return initTaskInfo(null, null, objectMap);
        } catch (ServiceException e) {
            log.error(e.getMessage());
            if (NO_TASK.equals(e.getMessage())) {
                // 没有任务
                return initTaskInfo("none", e.getMessage());
            } else {
                return initTaskInfo("error", e.getMessage());
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return initTaskInfo("error", getErrMessage(e));
        }
    }

    private JSONObject initTaskInfo(final String status, final String msg) {
        return initTaskInfo(status, msg, null);
    }

    private JSONObject initTaskInfo(final String status, final String msg, final Map<String, Object> objectMap) {
        final JSONObject jsonObject = new JSONObject();
        jsonObject.put("status", StrUtil.isNotBlank(status) ? status : "error");
        jsonObject.put("msg", msg);
        if (CollUtil.isNotEmpty(objectMap)) {
            // objectMap会覆盖status和msg
            jsonObject.putAll(objectMap);
        }
        return jsonObject;
    }

    private Map<String, Object> generateNextTaskInfo(final int siteId, final Long startPmid, final Long endPmid, final String activeScriptIds) {
        final TbDdsSite site = tbDdsSiteService.getById(siteId);
        if (site == null) {
            throw new ServiceException("没有找到这个站点，站点ID：" + siteId);
        }

        if (!StatusEnums.ENABLE.getCode().toString().equals(site.getStatus())) {
            throw new ServiceException("站点已停用，站点ID：" + siteId);
        }

        final Integer scriptlabelId = site.getScriptlabelId();
        if (scriptlabelId == null) {
            throw new ServiceException("此站点还未分配脚本标签，站点ID：" + siteId);
        }

        final Set<Integer> activeScriptIdCol = new HashSet<>();
        log.warn("siteId:{}, activeScriptIds:{}", siteId, activeScriptIds);
        if (StrUtil.isNotBlank(activeScriptIds)) {
            try {
                String[] split = activeScriptIds.split(",");
                for (String s : split) {
                    activeScriptIdCol.add(Integer.parseInt(s));
                }
            } catch (Exception e) {
                log.warn("解析以拉取任务的脚本id数据出错:{}", e.getMessage());
                activeScriptIdCol.clear();
            }
        }

        final String siteType = site.getSiteType();
        // 先查询是否存在失败的任务进行重试，获取当前节点分组其他节点的未完成的任务
        TaskPaperQueryDTO dto = initTaskPaperQueryDTO(siteId, scriptlabelId, siteType, activeScriptIdCol);
        dto.setDownloadMode(TaskDownloadModeEnum.complete.name());
        dto.setScheduleStatus(TaskPaperScheduleStatusEnum.failed.name());
        Long paperId = tbDdsTaskPaperMapper.getNextExecuteTaskPaperIdByScriptlableId(dto);
        if (CollUtil.isNotEmpty(activeScriptIdCol) && paperId == null) {
            // 当未查询到activeScriptIds不同的脚本任务时，再剩余任务
            dto.setActiveScriptIdCol(null);
            paperId = tbDdsTaskPaperMapper.getNextExecuteTaskPaperIdByScriptlableId(dto);
        }


        if (paperId == null) {
            // 无重试任务时，查询当前节点任务
            dto = initTaskPaperQueryDTO(siteId, scriptlabelId, siteType, activeScriptIdCol);

            dto.setStartPmid(startPmid);
            dto.setEndPmid(endPmid);
            paperId = tbDdsTaskPaperMapper.getNextExecuteTaskPaperId(dto);
            if (CollUtil.isNotEmpty(activeScriptIdCol) && paperId == null) {
                // 当未查询到activeScriptIds不同的脚本任务时，再剩余任务
                dto.setActiveScriptIdCol(null);
                paperId = tbDdsTaskPaperMapper.getNextExecuteTaskPaperId(dto);
            }
        }

        if (paperId == null) {
            throw new ServiceException(NO_TASK);
        }

        final TbDdsTaskPaper paper = tbDdsTaskPaperMapper.selectById(paperId);
        paper.setStatus(TaskPaperStatusEnum.executing.name());
        paper.setTimeExecute(new Date());
        tbDdsTaskPaperMapper.updateById(paper);

        TbDdsTaskSchedule schedule = tbDdsTaskService.findTaskSchedule(paper.getId(), siteId);
        if (schedule == null) {
            schedule = new TbDdsTaskSchedule();
            schedule.setPaperId(paperId);
            schedule.setSiteId(siteId);
            schedule.setTimeAssigned(paper.getCreateTime());
        }
        schedule.setTimeExecute(paper.getTimeExecute() == null ? new Date() : paper.getTimeExecute());
        schedule.setStatus(TaskPaperScheduleStatusEnum.executing.name());
        tbDdsTaskScheduleMapper.insertOrUpdate(schedule);

        final Map<String, Object> result = wrapperNextTaskInfo(paper, site);

        final String taskId = paper.getTaskId();
        appendTaskMessage(tbDdsTaskLogMapper, taskId, "已成功下发 doc_id：" + paper.getDocId() + " 给站点：" + siteId);

        log.info("已成功下发 " + taskId + "  " + paper.getDocId() + " 给站点：" + siteId);
        return result;
    }

    private TaskPaperQueryDTO initTaskPaperQueryDTO(final int siteId, final int scriptlabelId, final String siteType, Set<Integer> activeScriptIdCol) {
        final TaskPaperQueryDTO dto = new TaskPaperQueryDTO();
        dto.setSiteId(siteId);
        dto.setScriptlabelId(scriptlabelId);
        dto.setSiteType(siteType);
        dto.setActiveScriptIdCol(CollUtil.isEmpty(activeScriptIdCol) ? null : activeScriptIdCol);

        dto.setTaskStatus(TaskStatusEnum.assigned.name());
        dto.setPaperStatus(TaskPaperStatusEnum.waiting.name());

        return dto;
    }

    private Map<String, Object> wrapperNextTaskInfo(TbDdsTaskPaper paper, TbDdsSite site) {
        final Map<String, Object> taskInfoMap = new LinkedHashMap<>();

        if (paper == null || site == null) {
            taskInfoMap.put("status", "error");
            taskInfoMap.put("msg", "获取任务失败");
            return taskInfoMap;
        }

        // ------------------设置返回站点的信息-------------------------------------------------
//        final SimpleDateFormat sf = new SimpleDateFormat(DatePattern.NORM_DATETIME_PATTERN);
        final FastDateFormat sf = FastDateFormat.getInstance(DatePattern.NORM_DATETIME_PATTERN, null, null);

        // 控制站点下载时间间隔，这里需要对期刊限量进行判断，当前暂时不做
        if (site.getObtainTaskInterval() == null || site.getObtainTaskInterval() < 1) {
            taskInfoMap.put("obtainTaskInterval", 60);
        } else {
            taskInfoMap.put("obtainTaskInterval", site.getObtainTaskInterval());
        }

        // 站点
        final String siteType = site.getSiteType();
        taskInfoMap.put("currSiteType", siteType);
        taskInfoMap.put("siteId", site.getId());
        taskInfoMap.put("siteName", site.getSiteName());

        // 任务信息
        final TbDdsTask tbDdsTask = tbDdsTaskService.getById(paper.getTaskId());
        taskInfoMap.put("taskId", paper.getTaskId());
        taskInfoMap.put("taskStatus", tbDdsTask.getStatus());
        taskInfoMap.put("taskCreateTime", sf.format(tbDdsTask.getCreateTime()));
        taskInfoMap.put("taskPriority", tbDdsTask.getPriority());
        taskInfoMap.put("retryNum", TbDdsTask.RETRY_NUM);
        taskInfoMap.put("retryInterval", tbDdsTask.getRetryInterval());
        // 文献基本信息
        final Long docId = paper.getDocId();
        final Article article = articleMapper.selectById(docId);
        taskInfoMap.put("docId", article.getId());
        taskInfoMap.put("pmid", article.getPmid());
        taskInfoMap.put("pmcId", article.getPmcId());
        taskInfoMap.put("title", article.getTitle());
        taskInfoMap.put("vernacularTitle", article.getVernacularTitle());
        taskInfoMap.put("publishedYear", article.getPublishedYear());
        taskInfoMap.put("publishedMonth", article.getPublishedMonth());
        taskInfoMap.put("publishedDay", article.getPublishedDay());

        final Long journalId = paper.getJournalId();
        Journal journal = null;
        if (journalId == null) {
            taskInfoMap.put("journalId", null);
            taskInfoMap.put("journalName", null);
        } else {
            taskInfoMap.put("journalId", journalId);

            journal = journalMapper.selectById(journalId);
            taskInfoMap.put("journalName", journal.getTitle());
        }
        taskInfoMap.put("year", article.getYear());
        taskInfoMap.put("volume", article.getVolume());
        taskInfoMap.put("issue", article.getIssue());
        taskInfoMap.put("page", article.getPage());

        final List<ArticleOtherId> articleOtherids = articleOtherIdMapper.selectList(Wrappers.lambdaQuery(ArticleOtherId.class).eq(ArticleOtherId::getDocId, article.getId()));
        Map<String, String> otherIdsMap = new HashMap<>();
        if (article.getPmid() != null) {
            otherIdsMap.put("pubmed", article.getPmid().toString());
        }
        if (CollUtil.isNotEmpty(articleOtherids)) {
            for (ArticleOtherId ids : articleOtherids) {
                otherIdsMap.put(ids.getSource(), ids.getOtherId());
            }
        }
        taskInfoMap.put("otherIds", otherIdsMap);

        // 任务执行参数
        if (journalId != null && !ScriptTypeEnum.batch.getCode().equalsIgnoreCase(siteType)) {
            // 源刊和高校
            Map<String, String> map = getCurrentScriptFile(journal.getScriptId());
            if (StrUtil.isBlank(map.get(ScriptInfoDTO.SCRIPT_SERVICE_PATH))) {
                throw new ServiceException("服务器中未指定执行脚本文件，请先上传脚本文件。doc_id:" + docId + "，期刊：" + journal.getTitle());
            }

            taskInfoMap.putAll(map);
        }
        taskInfoMap.put("status", "success");
        taskInfoMap.put("msg", "执行成功");

        return taskInfoMap;
    }

    /**
     * 获得执行脚本
     */
    private Map<String, String> getCurrentScriptFile(final Integer scriptId) {
        final Map<String, String> map = new LinkedHashMap<>();
        if (scriptId == null) {
            return map;
        }

        final TbDdsJournalScript tbDdsJournalScript = tbDdsJournalScriptMapper.findOne(Wrappers.lambdaQuery(TbDdsJournalScript.class)
                .eq(TbDdsJournalScript::getId, scriptId)
                .eq(TbDdsJournalScript::getStatus, StatusEnums.ENABLE.getCode().toString()));

        if (tbDdsJournalScript == null) {
            return map;
        }

        final Long fileId = tbDdsJournalScript.getFileId();
        final TbDdsFile ddsFile = tbDdsFileService.getById(fileId);
        if (ddsFile == null) {
            return map;
        }
        if (!tbDdsFileService.existsContentById(fileId)) {
            return map;
        }

        final String fileName = ddsFile.getFileName();
        final ScriptInfoDTO dto = new ScriptInfoDTO();
        dto.setScriptIdStr(scriptId.toString());
        dto.setScriptServicePath(StrUtil.format("/{}/{}", scriptId, fileName));
        dto.setScriptMD5(tbDdsJournalScript.getScriptMd5());

        final String contentType = ddsFile.getContentType();
        String scriptType = StrUtil.EMPTY;
        if (contentType != null) {
            int i = contentType.indexOf(".");
            if (i > -1) {
                scriptType = contentType.substring(i + 1);
            }
        }
        dto.setScriptType(StrUtil.trimToEmpty(scriptType));

        final JSONObject scriptJsonObject = (JSONObject) JSON.toJSON(dto);
        for (Map.Entry<String, Object> entry : scriptJsonObject.entrySet()) {
            final String key = entry.getKey();
            final Object value = entry.getValue();
            if (value != null) {
                map.put(key, value.toString());
            }
        }
        return map;
    }

    @Override
    public void getTaskScriptFile(String scriptIdStr, HttpServletResponse response) {
        if (StrUtil.isBlank(scriptIdStr)) {
            throw new ServiceException("脚本id不能为空");
        }
        try {
            tbDdsJournalScriptService.download(Integer.parseInt(scriptIdStr), response);
        } catch (Exception e) {
            throw new ServiceException("脚本下载失败：" + e.getMessage());
        }
    }

    @Override
    public SiteInfoVO getSiteBaseinfo(String siteId) {
        if (StrUtil.isBlank(siteId)) {
            throw new ServiceException("站点ID不能为空");
        }

        try {
            // 将字符串转换为整数
            Integer siteIdInt = Integer.parseInt(siteId);

            // 获取站点信息
            final TbDdsSite site = tbDdsSiteService.getById(siteIdInt);

            if (site == null) {
                throw new ServiceException("未找到该站点，请先在服务器上注册");
            }
            SiteInfoVO siteInfoVO = new SiteInfoVO();


            // 构建返回对象
            siteInfoVO.setSiteId(site.getId());
            siteInfoVO.setSiteName(site.getSiteName());
            siteInfoVO.setSiteAddr(site.getSiteAbbr());
            siteInfoVO.setSiteType(site.getSiteType());
            siteInfoVO.setStatus(Integer.parseInt(site.getStatus()));

            // 设置获取任务间隔，如果为空则默认为10分钟
            siteInfoVO.setObtainTaskInterval(site.getObtainTaskInterval() == null ? 10 : site.getObtainTaskInterval());

            // 设置任务线程数，如果为空则默认为5
            siteInfoVO.setTaskThreadNum(site.getTaskThreadNum() == null ? 5 : site.getTaskThreadNum());

            // 设置脚本信息
            // 通过scriptlabelId获取关联的脚本信息
            if (ScriptTypeEnum.batch.getCode().equals(site.getSiteType())) {
                Integer scriptlabelId = site.getScriptlabelId();
                if (scriptlabelId != null) {
                    // 获取脚本标签关联的脚本列表
                    List<ScriptVO> scriptList = tbDdsScriptlabelService.scriptListOfLabel(scriptlabelId);
                    if (scriptList != null && !scriptList.isEmpty()) {
                        // 将脚本信息转换为JSON对象
                        final List<ScriptInfoDTO> scriptJsons = new ArrayList<>();
                        for (ScriptVO script : scriptList) {
                            final ScriptInfoDTO dto = new ScriptInfoDTO();
                            dto.setScriptIdStr(script.getScriptId().toString());
                            dto.setScriptMD5(script.getScriptMd5());

                            final long scriptFileId = Long.parseLong(script.getScriptFileId());
                            final TbDdsFile ddsFile = tbDdsFileService.getById(scriptFileId);
                            dto.setScriptFileName(ddsFile.getFileName());
                            dto.setScriptServicePath(StrUtil.format("/{}/{}", script.getScriptId(), ddsFile.getFileName()));
                            dto.setOrder(script.getSort());
                            scriptJsons.add(dto);
                        }
                        siteInfoVO.setScript(scriptJsons);
                    }
                }
            } else {
                siteInfoVO.setMsg("该站点不支持批量任务");

            }
            return siteInfoVO;
        } catch (NumberFormatException e) {
            throw new ServiceException("站点ID格式错误");
        } catch (Exception e) {
            log.error("获取站点信息失败", e);
            throw new ServiceException("获取站点信息失败：" + e.getMessage());
        }
    }

    @Override
    public void sendTaskMessage(String taskId, String msg) {
        appendTaskMessage(tbDdsTaskLogMapper, taskId, msg);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult uploadTaskResult(final String siteId, final String taskId, final MultipartFile resultFile, final MultipartFile md5File, final Long docId) {
        // 参数验证
        if (StrUtil.isBlank(siteId) || StrUtil.isBlank(taskId) || docId == null) {
            return AjaxResult.error("参数不完整");
        }
        if (taskId.contains("..")) {
            return AjaxResult.error("非法的任务id");
        }
        if (resultFile == null || resultFile.isEmpty() || md5File == null || md5File.isEmpty()) {
            return AjaxResult.error("文件不能为空");
        }

        final String nameOfZip = resultFile.getOriginalFilename();
        if (nameOfZip == null) {
            return AjaxResult.error("上传失败：zip文件名不能为空");
        }

        final String nameOfMd5 = md5File.getOriginalFilename();
        if (nameOfMd5 == null) {
            return AjaxResult.error("上传失败：md5文件名不能为空");
        }

        File dir = null;
        InputStream resultFileInputStream = null;
        InputStream md5FileInputStream = null;
        try {
            // 仅上传文件
            final File uploadTempDir = initUploadTempDir();
            final String absPath = StrUtil.format("{}/{}/{}/{}", siteId, taskId, docId, IdUtil.fastSimpleUUID());
            dir = new File(uploadTempDir, absPath);
            if (!dir.exists()) {
                dir.mkdirs();
            }

            final File uploadZipFile = new File(dir, nameOfZip);
            resultFileInputStream = resultFile.getInputStream();
            FileUtil.writeFromStream(resultFileInputStream, uploadZipFile);
//            resultFile.transferTo(uploadZipFile);

            final File uploadMd5File = new File(dir, nameOfMd5);
            md5FileInputStream = md5File.getInputStream();
            FileUtil.writeFromStream(md5FileInputStream, uploadMd5File);
//            md5File.transferTo(uploadMd5File);
            final String uploadPath = absPath + "/" + nameOfZip;
            final String uploadMd5Path = absPath + "/" + nameOfMd5;

            if (!MyHashUtil.md5(uploadZipFile).equals(FileUtil.readUtf8String(uploadMd5File))) {
                FileUtil.del(dir);
                return AjaxResult.error("上传失败：文件md5校验失败");
            }

            final Map<String, String> data = new HashMap<>();
            data.put("uploadPath", uploadPath);
            data.put("uploadMd5Path", uploadMd5Path);
            return AjaxResult.success("上传成功", data);
        } catch (Exception e) {
            FileUtil.del(dir);
            log.error("上传任务结果失败", e);
            return AjaxResult.error("上传失败：" + e.getMessage());
        } finally {
            IoUtil.close(resultFileInputStream);
            IoUtil.close(md5FileInputStream);
        }
    }

    @Override
    public ApiResultVO notice2UpdateTask(Integer siteId, NoticeApiDTO info) {
        try {
            doNotice2UpdateTask(siteId, info);

            // 成功，没有异常则 删除 Ftp 上的文件
            final String uploadPath = info.getUploadPath();
            if (StrUtil.isNotBlank(uploadPath) && !uploadPath.contains("..")) {
                FileUtil.del(new File(initUploadTempDir(), uploadPath).getParentFile());
//                FileUtil.del(new File(ftpRootDir, uploadPath.replace(".zip", ".md5")));
//                FileUtil.del(new File(ftpRootDir, uploadPath.replace(".zip", "")));
            }

            return ApiResultVO.success("任务状态已更新");
        } catch (ServiceException e1) {
            log.error("更新任务状态失败", e1);
            return ApiResultVO.error(e1.getMessage());
        } catch (Exception e2) {
            log.error("更新任务状态异常", e2);
            return ApiResultVO.error(getErrMessage(e2));
        }
    }


    private void doNotice2UpdateTask(final Integer siteId, final NoticeApiDTO info) {
        if (info == null || info.getDocId() == null || siteId == null) {
            throw new ServiceException("参数错误，请指定 doc_id 及 siteId");
        }

        final String taskId = info.getTaskId();
        if (StrUtil.isBlank(taskId)) {
            throw new ServiceException("参数错误，请指定 taskId");
        }
        final TbDdsTask ddsTask = tbDdsTaskService.getById(taskId);
        if (ddsTask == null) {
            throw new ServiceException("该任务不存在");
        }

        if (StrUtil.isBlank(info.getStatus())) {
            info.setStatus("failed");
        }
        log.info(JSON.toJSONString(info));
        final TbDdsSite site = tbDdsSiteService.getById(siteId);
        if (site == null) {
            throw new ServiceException(String.format("站点未找到 %d", siteId));
        }

        final Long docId = info.getDocId();
        final TbDdsTaskPaper paper = tbDdsTaskService.findTaskPaper(taskId, docId);
        if (paper == null) {
            log.warn(String.format("未找到该任务 %s %d", taskId, docId));
            return;
        }

        final TbDdsTaskSchedule schedule = tbDdsTaskService.findTaskSchedule(paper.getId(), siteId);
        if (schedule == null) {
            log.warn(String.format("未找到该任务的计划 taskId:%s paperId: %d site: %d", taskId, paper.getId(), siteId));
            return;
        }

        // 处理结果文件
        if (StrUtil.isNotBlank(info.getUploadPath())) {
            try {
                parse2TaskLinks(info.getUploadPath(), paper, ddsTask.getTestFlag());
                translateTaskLinks2Attach(paper, siteId);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                appendTaskMessage(tbDdsTaskLogMapper, taskId, String.format("拷贝文件到附件表出错，%s", e.getMessage()));
                info.setStatus("failed");
            }
        }

        try {
            Date curDate = new Date();

            // 如果成功
            if ("success".equalsIgnoreCase(info.getStatus().trim())) {
                schedule.setStatus(TaskPaperScheduleStatusEnum.success.name());

                paper.setStatus(TaskPaperStatusEnum.success_auto.name());
                paper.setUpdateTime(curDate);
                paper.setTimeComplete(curDate);
                tbDdsTaskPaperMapper.updateById(paper);

                this.updateById(schedule);

                final Set<String> notCompleteStatus = TaskPaperScheduleStatusEnum.getNotCompleteTaskPaperScheduleStatus().stream()
                        .collect(Collectors.toMap(TaskPaperScheduleStatusEnum::name, t -> t)).keySet();

                // 将其他的任务中没有完成的项目标记为已被其他节点下载
                this.baseMapper.updateNotCompleteTaskScheduleStatusByDocId(docId, TaskPaperScheduleStatusEnum.ignore.name(), notCompleteStatus);
                updateNotCompleteTaskPagerStatusByDocId(docId, TaskPaperStatusEnum.success_exist);
            } else {
                // 上传失败
                schedule.setStatus(TaskPaperScheduleStatusEnum.failed.name());
                this.updateById(schedule);

                TaskPaperStatusEnum paperStatus = TaskPaperStatusEnum.failed;
                final String siteType = site.getSiteType();
                final int retryTimes = getRetryTimesBySiteType(siteType, paper);
                final int maxRetryTimes = Math.max(PdsAppConfig.getPaperMaxRetryTimes(), 0);
                if (TaskDownloadModeEnum.complete.name().equals(ddsTask.getDownloadMode()) && retryTimes < maxRetryTimes) {
                    // 完整度优先，且重试次数小于3次
                    final List<String> supportSiteType = ddsTask.getSupportSiteType();
                    if (CollUtil.isNotEmpty(supportSiteType)) {
                        final Integer scriptlabelId = site.getScriptlabelId();
                        final List<Integer> reTrySiteIds = tbDdsSiteService.findReTrySiteIds(paper.getId(), scriptlabelId);
                        if (CollUtil.isNotEmpty(reTrySiteIds)) {
                            // 完整度优先下，存在可重试的站点时，才将任务重新设置为waiting
                            paperStatus = TaskPaperStatusEnum.waiting;
                            setRetryTimesBySiteType(siteType, paper, retryTimes + 1);
                        }
                    }
                }

                // 临时解决办法，等待分组功能做完前，加快速度，暂时不在多个站点重试
                //List<Integer> canAssignSiteIds = literatureDeliveryManager.searchCanAssignSiteIds(schedule.getTbDdsTaskPaper().getId(), site.getSiteGroup());
                //List<Integer> canAssignSiteIds = new ArrayList<>(0);
                //TaskPaperStatusEnum paperStatus = CollectionUtils.isEmpty(canAssignSiteIds) ? TaskPaperStatusEnum.failed : TaskPaperStatusEnum.waiting;

                paper.setStatus(paperStatus.name());
                paper.setUpdateTime(curDate);
                paper.setTimeComplete(curDate);
                tbDdsTaskPaperMapper.updateById(paper);
            }
        } finally {
            // 无论无何最后跟新任务状态
            tbDdsTaskService.updateTaskCompleteStatus(taskId);

            appendTaskMessage(tbDdsTaskLogMapper,
                    taskId,
                    String.format("站点 %d, 传递文献 %s: %s %d", siteId, info.getStatus(), taskId, docId)
            );
        }
    }

    public static int getRetryTimesBySiteType(String siteType, TbDdsTaskPaper paper) {
        if (ScriptTypeEnum.batch.getCode().equalsIgnoreCase(siteType)) {
            return paper.getBatchRetryTimes();
        } else if (ScriptTypeEnum.journal.getCode().equalsIgnoreCase(siteType)) {
            return paper.getJournalRetryTimes();
        } else if (ScriptTypeEnum.school.getCode().equalsIgnoreCase(siteType)) {
            return paper.getSchoolRetryTimes();
        } else {
            return 100;
        }
    }

    public static void setRetryTimesBySiteType(String siteType, TbDdsTaskPaper paper, int retryTimes) {
        if (ScriptTypeEnum.batch.getCode().equalsIgnoreCase(siteType)) {
            paper.setBatchRetryTimes(retryTimes);
        } else if (ScriptTypeEnum.journal.getCode().equalsIgnoreCase(siteType)) {
            paper.setJournalRetryTimes(retryTimes);
        } else if (ScriptTypeEnum.school.getCode().equalsIgnoreCase(siteType)) {
            paper.setSchoolRetryTimes(retryTimes);
        }
    }

    private void updateNotCompleteTaskPagerStatusByDocId(Long docId, TaskPaperStatusEnum status) {
        final Set<String> notCompleteStatusOfPaper = TaskPaperStatusEnum.getNotCompleteTaskpaperStatus().stream()
                .collect(Collectors.toMap(TaskPaperStatusEnum::name, t -> t)).keySet();
        final boolean completeFlag = TaskPaperStatusEnum.getCompleteTaskpaperStatus().contains(status);
        tbDdsTaskPaperMapper.updateNotCompleteTaskPagerStatusByDocId(docId, completeFlag, status.name(), notCompleteStatusOfPaper);
    }

    /**
     * 处理、验证用户提交的文件，并存入数据库
     */
    private void parse2TaskLinks(final String zipFilePath, final TbDdsTaskPaper paper, final Short testFlag) throws IOException {


        if (StrUtil.isBlank(zipFilePath) || paper == null) {
            throw new ServiceException("请指定附件Zip包的位置");
        }

        // 解压文件
        final File zipFile = new File(initUploadTempDir(), zipFilePath);
        if (!zipFile.exists()) {
            throw new ServiceException(String.format("上传的文件 %s, 在服务器上没找到。", zipFile.getAbsoluteFile()));
        }
        // 解压文件
        CompressUtil.decompress(zipFile, zipFile.getParentFile());

        // 读取文件内容存入任务附件表中
        final String unzipDirName = zipFile.getName().replace(".zip", "");
        final File basicDir = new File(zipFile.getParent(), unzipDirName);
        if (!basicDir.exists()) {
            throw new ServiceException(String.format("结果文件没有成功解压，或压缩文件格式错误，应当有一个与文件同名的根目录 %s", zipFile.getAbsolutePath()));
        }

        final File noteFile = new File(basicDir, "attach_note.txt");
        if (!noteFile.exists()) {
            throw new ServiceException("attach_note.txt 没有找到");
        }

        final List<String> lines = FileUtils.readLines(noteFile, "UTF-8");
        if (CollUtil.isEmpty(lines)) {
            throw new ServiceException("attach_note.txt 没有内容");
        }

        final List<TbDdsTaskLink> taskLinks = new ArrayList<>();
        for (String line : lines) {

            if (StrUtil.isBlank(line)) {
                continue;
            }

            // lineArray[0]表示文件类型，lineArray[1]表示文件名称，lineArray[2]表示文件下载地址
            final String[] lineArray = line.split("\\$##\\$");
            final String fileType = lineArray.length > 0 && StrUtil.isNotBlank(lineArray[0]) ? lineArray[0].trim() : null;
            final String fileName = lineArray.length > 1 && StrUtil.isNotBlank(lineArray[1]) ? lineArray[1].trim() : null;
            final String downPath = lineArray.length > 2 && StrUtil.isNotBlank(lineArray[2]) ? lineArray[2].trim() : null;

            if (StringUtils.isAnyBlank(fileType, fileName)) {
                throw new ServiceException("attach_note.txt 文件格式错误, 文件类型：" + fileType + ", 文件名：" + fileName);
            }

            final File file = new File(basicDir, fileName);
            if (!file.exists()) {
                throw new ServiceException("未找到文件，Path：" + file.getAbsolutePath());
            }

            final TbDdsTaskLink taskLink = new TbDdsTaskLink();

            if (StrUtil.isNotBlank(fileType)) {
                taskLink.setType(fileType.toUpperCase());
            } else {
                int beginIndex = file.getName().lastIndexOf(".");
                if (beginIndex > 0) {
                    taskLink.setType(file.getName().substring(beginIndex).toUpperCase());
                } else {
                    taskLink.setType("UNKNOW");
                }
            }
            if ("PDF".equalsIgnoreCase(taskLink.getType())) {
                if (!PDFHelper.checkPDF(file)) {
                    throw new ServiceException("PDF文件格式错误或者已经损坏: " + file.getAbsolutePath());
                }
            }

            taskLink.setIsFree(0);
            taskLink.setLinkUrl(downPath);
            taskLink.setUploadPath(zipFilePath.replace(".zip", "") + "/" + file.getName());
            taskLink.setSize(file.length());
            taskLink.setUpdateTime(new Date());
            taskLink.setPaperId(paper.getId());
            taskLinks.add(taskLink);
        }

        final boolean hasPDF = taskLinks.stream().anyMatch(t -> "PDF".equalsIgnoreCase(t.getType()));
        if (!hasPDF) {
            throw new ServiceException("上传的文件中没有包含PDF文件");
        }
        final boolean isNotTest = testFlag != null && testFlag == 0;
        if (isNotTest) {
            tbDdsTaskLinkMapper.insert(taskLinks);
        }
    }

    private List<TbDdsTaskLink> findByPaperId(Long paperId) {
        return tbDdsTaskLinkMapper.selectList(Wrappers.lambdaQuery(TbDdsTaskLink.class).eq(TbDdsTaskLink::getPaperId, paperId));
    }

    private void translateTaskLinks2Attach(final TbDdsTaskPaper paper, final Integer siteId) throws IOException, ServiceException {

        if (paper == null) {
            throw new ServiceException("参数错误");
        }
        final Long paperId = paper.getId();

        final List<TbDdsTaskLink> taskLinks = findByPaperId(paperId);

        final File dir = initUploadTempDir();
        for (final TbDdsTaskLink taskLink : taskLinks) {

            final File file = new File(dir, taskLink.getUploadPath());
            if (!file.exists()) {
                throw new ServiceException("服务器未找到附件，Path:" + taskLink.getUploadPath());
            }
            if ("PDF".equalsIgnoreCase(taskLink.getType())) {
                try (FileInputStream fileInputStream = new FileInputStream(file)) {
                    final String fileMd5 = DigestUtils.md5Hex(fileInputStream);
                    FileUploadDTO uploadDTO = new FileUploadDTO(file, FileTypeEnum.PDF, fileMd5, file.getName()
                            , ArticleAttachmentSourceEnum.site_download.name(), paper.getDocId(), siteId, true);
                    tbDdsFileService.upload(uploadDTO);
                }
            }
        }
    }

    private File initUploadTempDir() {
        final File tempDir = AppConfig.initDataHome(DirectoryEnum.upload);
        final File tempScriptDir = new File(tempDir, "upload_result");
        if (!tempScriptDir.exists()) {
            tempScriptDir.mkdirs();
        }
        // 读取tempScriptDir目录下所有文件并清理过期文件（7天前）
        /*final File[] files = tempScriptDir.listFiles(file -> isExpiredFile(file, SCRIPT_EXPIRE_TIME));
        if (ArrayUtil.isNotEmpty(files)) {
            for (File file : files) {
                file.delete();
            }
        }*/
        return tempScriptDir;
    }

}
