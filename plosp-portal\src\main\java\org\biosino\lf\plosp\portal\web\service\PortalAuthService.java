package org.biosino.lf.plosp.portal.web.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.pds.article.domain.PlospUser;
import org.biosino.lf.pds.article.service.IPlospUserService;
import org.biosino.lf.pds.common.core.redis.RedisCache;
import org.biosino.lf.pds.common.exception.ServiceException;
import org.biosino.lf.pds.common.utils.StringUtils;
import org.biosino.lf.plosp.portal.domain.PortalLoginUser;
import org.biosino.lf.plosp.portal.utils.PortalSecurityUtils;
import org.biosino.lf.plosp.portal.web.dto.auth.RegisterRequest;
import org.biosino.lf.plosp.portal.web.dto.auth.UserProfileResponse;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashSet;
import java.util.Random;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 门户认证服务
 * 处理门户的认证、注册和用户管理
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PortalAuthService {

    private final IPlospUserService userService;
    private final PortalTokenService tokenService;
    private final RedisCache redisCache;

    @Autowired(required = false)
    private JavaMailSender mailSender;

    private static final String VERIFICATION_CODE_PREFIX = "portal:verification:";
    private static final String PASSWORD_RESET_PREFIX = "portal:password_reset:";
    private static final int CODE_EXPIRE_MINUTES = 10;

    /**
     * 用户登录
     */
    public String login(String email, String password) {
        // 验证输入参数
        if (StringUtils.isEmpty(email) || StringUtils.isEmpty(password)) {
            throw new ServiceException("邮箱和密码不能为空");
        }

        // 根据邮箱查找用户
        PlospUser user = userService.selectUserByEmail(email);
        if (user == null) {
            throw new ServiceException("用户不存在");
        }

        if (user.getStatus() != null && user.getStatus() == 0) {
            throw new ServiceException("用户已被禁用");
        }

        // 验证密码
        if (!PortalSecurityUtils.matchesPassword(password, user.getPassword())) {
            throw new ServiceException("密码错误");
        }

        // 创建门户登录用户，门户用户具有基本权限
        Set<String> permissions = new HashSet<>();
        permissions.add("portal:user:view");
        permissions.add("portal:user:edit");
        PortalLoginUser loginUser = new PortalLoginUser(user.getUserId(), user, permissions);

        // 生成令牌
        String token = tokenService.createToken(loginUser);

        log.info("门户用户登录成功: {}", email);
        return token;
    }

    /**
     * 用户注册
     */
    @Transactional
    public void register(RegisterRequest request) {
        // 验证密码是否匹配
        if (!request.getPassword().equals(request.getConfirmPassword())) {
            throw new ServiceException("两次输入的密码不一致");
        }

        // 验证邮箱验证码
        if (!verifyCode(request.getEmail(), request.getVerificationCode(), VERIFICATION_CODE_PREFIX)) {
            throw new ServiceException("验证码错误或已过期");
        }

        // 检查邮箱是否已存在
        if (userService.selectUserByEmail(request.getEmail()) != null) {
            throw new ServiceException("该邮箱已被注册");
        }

        // 创建新用户
        PlospUser user = new PlospUser();
        BeanUtils.copyProperties(request, user);

        // 设置附加字段
        user.setUserName(request.getFirstName() + " " + request.getLastName());
        user.setPassword(PortalSecurityUtils.encryptPassword(request.getPassword()));
        user.setStatus(1); // 激活状态
        user.setPoints(0); // 初始积分
        user.setCreateTime(new Date());
        user.setUpdateTime(new Date());

        // 保存用户
        if (!userService.save(user)) {
            throw new ServiceException("注册失败");
        }

        // 移除验证码
        redisCache.deleteObject(VERIFICATION_CODE_PREFIX + request.getEmail());

        log.info("门户用户注册成功: {}", request.getEmail());
    }

    /**
     * 发送验证码
     */
    public void sendVerificationCode(String email) {
        if (StringUtils.isEmpty(email)) {
            throw new ServiceException("邮箱不能为空");
        }

        // 生成6位数字验证码
        String code = generateVerificationCode();

        // 存储到Redis并设置过期时间
        redisCache.setCacheObject(VERIFICATION_CODE_PREFIX + email, code, CODE_EXPIRE_MINUTES, TimeUnit.MINUTES);

        // 发送邮件
        sendVerificationEmail(email, code, "PLOSP Portal 注册验证码");

        log.info("验证码已发送至: {}", email);
    }

    /**
     * 验证邮箱验证码
     */
    public void verifyEmail(String email, String code) {
        if (!verifyCode(email, code, VERIFICATION_CODE_PREFIX)) {
            throw new ServiceException("验证码错误或已过期");
        }
    }

    /**
     * 用户退出
     */
    public void logout() {
        PortalLoginUser loginUser = PortalSecurityUtils.getLoginUser();
        if (loginUser != null) {
            tokenService.delLoginUser(loginUser.getToken());
            log.info("门户用户退出: {}", loginUser.getUsername());
        }
    }

    /**
     * 构建用户资料响应
     */
    public UserProfileResponse buildUserProfile(PlospUser user) {
        UserProfileResponse profile = new UserProfileResponse();
        BeanUtils.copyProperties(user, profile);
        return profile;
    }

    /**
     * 更新用户资料
     */
    @Transactional
    public void updateProfile(PlospUser userUpdate) {
        PlospUser existingUser = userService.getById(userUpdate.getUserId());
        if (existingUser == null) {
            throw new ServiceException("用户不存在");
        }

        // 更新允许的字段
        existingUser.setFirstName(userUpdate.getFirstName());
        existingUser.setLastName(userUpdate.getLastName());
        existingUser.setUserName(userUpdate.getFirstName() + " " + userUpdate.getLastName());
        existingUser.setOrganization(userUpdate.getOrganization());
        existingUser.setDepartment(userUpdate.getDepartment());
        existingUser.setPiName(userUpdate.getPiName());
        existingUser.setTitle(userUpdate.getTitle());
        existingUser.setPhone(userUpdate.getPhone());
        existingUser.setCountryRegion(userUpdate.getCountryRegion());
        existingUser.setStateProvince(userUpdate.getStateProvince());
        existingUser.setCity(userUpdate.getCity());
        existingUser.setUpdateTime(new Date());

        if (!userService.updateById(existingUser)) {
            throw new ServiceException("更新失败");
        }

        log.info("门户用户资料已更新: {}", existingUser.getEmail());
    }

    /**
     * 修改密码
     */
    @Transactional
    public void changePassword(Long userId, String oldPassword, String newPassword) {
        PlospUser user = userService.getById(userId);
        if (user == null) {
            throw new ServiceException("用户不存在");
        }

        // 验证旧密码
        if (!PortalSecurityUtils.matchesPassword(oldPassword, user.getPassword())) {
            throw new ServiceException("原密码错误");
        }

        // 更新密码
        user.setPassword(PortalSecurityUtils.encryptPassword(newPassword));
        user.setUpdateTime(new Date());

        if (!userService.updateById(user)) {
            throw new ServiceException("密码修改失败");
        }

        log.info("门户用户密码已修改: {}", user.getEmail());
    }

    /**
     * 发送密码重置验证码
     */
    public void sendPasswordResetCode(String email) {
        PlospUser user = userService.selectUserByEmail(email);
        if (user == null) {
            throw new ServiceException("该邮箱未注册");
        }

        // 生成验证码
        String code = generateVerificationCode();

        // 存储到Redis
        redisCache.setCacheObject(PASSWORD_RESET_PREFIX + email, code, CODE_EXPIRE_MINUTES, TimeUnit.MINUTES);

        // 发送邮件
        sendVerificationEmail(email, code, "PLOSP Portal 密码重置验证码");

        log.info("密码重置验证码已发送至: {}", email);
    }

    /**
     * 使用验证码重置密码
     */
    @Transactional
    public void resetPassword(String email, String code, String newPassword) {
        if (!verifyCode(email, code, PASSWORD_RESET_PREFIX)) {
            throw new ServiceException("验证码错误或已过期");
        }

        PlospUser user = userService.selectUserByEmail(email);
        if (user == null) {
            throw new ServiceException("用户不存在");
        }

        // 更新密码
        user.setPassword(PortalSecurityUtils.encryptPassword(newPassword));
        user.setUpdateTime(new Date());

        if (!userService.updateById(user)) {
            throw new ServiceException("密码重置失败");
        }

        // 移除重置验证码
        redisCache.deleteObject(PASSWORD_RESET_PREFIX + email);

        log.info("门户用户密码已重置: {}", email);
    }

    /**
     * 生成6位数字验证码
     */
    private String generateVerificationCode() {
        Random random = new Random();
        return String.format("%06d", random.nextInt(1000000));
    }

    /**
     * 从Redis验证验证码
     */
    private boolean verifyCode(String email, String code, String prefix) {
        String cachedCode = redisCache.getCacheObject(prefix + email);
        return StringUtils.isNotEmpty(cachedCode) && cachedCode.equals(code);
    }

    /**
     * 发送验证邮件
     */
    private void sendVerificationEmail(String email, String code, String subject) {
        if (mailSender == null) {
            log.warn("邮件发送器未配置，跳过邮件发送");
            return;
        }

        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setTo(email);
            message.setSubject(subject);
            message.setText(String.format(
                    "您的验证码是: %s\n\n验证码有效期为%d分钟，请及时使用。\n\n如果这不是您的操作，请忽略此邮件。\n\nPLOSP Portal Team",
                    code, CODE_EXPIRE_MINUTES
            ));
            log.warn("验证码：{}", code);

            // mailSender.send(message);
        } catch (Exception e) {
            log.error("发送验证邮件失败，收件人: " + email, e);
            throw new ServiceException("验证码发送失败，请稍后重试");
        }
    }


}
