package org.biosino.lf.pds.article.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.biosino.lf.pds.article.domain.Journal;
import org.biosino.lf.pds.article.dto.JournalMergeDTO;
import org.biosino.lf.pds.article.dto.JournalQueryDTO;
import org.biosino.lf.pds.article.dto.JournalUpdateDTO;
import org.biosino.lf.pds.article.dto.JournalValidationResult;
import org.biosino.lf.pds.article.mapper.ArticleMapper;
import org.biosino.lf.pds.article.mapper.JournalMapper;
import org.biosino.lf.pds.article.mapper.TbDdsScriptlabelJournalMapper;
import org.biosino.lf.pds.article.mapper.TbDdsTaskPaperMapper;
import org.biosino.lf.pds.article.service.IJournalService;
import org.biosino.lf.pds.article.service.JournalValidationService;
import org.biosino.lf.pds.common.exception.JournalValidationException;
import org.biosino.lf.pds.common.exception.ServiceException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 期刊服务实现类
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class IJournalServiceImpl extends ServiceImpl<JournalMapper, Journal> implements IJournalService {

    private final ArticleMapper articleMapper;
    private final JournalValidationService journalValidationService;
    private final TbDdsTaskPaperMapper tbDdsTaskPaperMapper;
    private final TbDdsScriptlabelJournalMapper tbDdsScriptlabelJournalMapper;

    @Override
    public List<Journal> selectJournalList(JournalQueryDTO queryDTO) {
        return this.baseMapper.selectJournalList(queryDTO);
    }

    @Override
    public Journal selectJournalById(Long id) {
        return this.baseMapper.selectJournalById(id);
    }

    @Override
    public List<Journal> findByTitleIn(List<String> journalNames) {
        journalNames = journalNames.stream().filter(StrUtil::isNotBlank).collect(Collectors.toList());
        if (CollUtil.isEmpty(journalNames)) {
            return CollUtil.newArrayList();
        }
        return this.list(Wrappers.<Journal>lambdaQuery().in(Journal::getTitle, journalNames));
    }

    @Override
    public Journal updateJournal(JournalUpdateDTO journalUpdateDTO) {
        // 验证ISSN字段唯一性
        JournalValidationResult validationResult = journalValidationService.validateJournalUpdate(journalUpdateDTO);
        if (!validationResult.isValid()) {
            throw new JournalValidationException(validationResult);
        }

        Journal journal = baseMapper.selectById(journalUpdateDTO.getId());
        BeanUtil.copyProperties(journalUpdateDTO, journal);
        // 检查是否为空对象 {}
        if (CollUtil.isEmpty(journalUpdateDTO.getUniqueHistory())) {
            journal.setUniqueHistory(null);
        } else {
            journal.setUniqueHistory(CollUtil.distinct(journalUpdateDTO.getUniqueHistory()));
        }
        if (CollUtil.isEmpty(journalUpdateDTO.getIssnHistory())) {
            journal.setIssnHistory(null);
        } else {
            journal.setIssnHistory(CollUtil.distinct(journalUpdateDTO.getIssnHistory()));
        }
        // 设置更新时间
        journal.setUpdateTime(new Date());

        this.updateById(journal);
        return journal;
    }

    @Override
    public boolean updateJournalStatus(Long id, Long status) {
        if (id == null || status == null) {
            throw new IllegalArgumentException("参数 id 和 status 不能为空");
        }
        return this.update(Wrappers.<Journal>lambdaUpdate().eq(Journal::getId, id).set(Journal::getStatus, status));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean mergeJournals(JournalMergeDTO journalMergeDTO) {
        if (CollUtil.isEmpty(journalMergeDTO.getSourceIds()) || journalMergeDTO.getTargetId() == null) {
            throw new ServiceException("未选择需要合并的期刊");
        }
        // 验证ISSN字段唯一性
        JournalValidationResult validationResult = journalValidationService.validateJournalMerge(journalMergeDTO);
        if (!validationResult.isValid()) {
            throw new JournalValidationException(validationResult);
        }

        // 检查目标期刊是否存在
        Journal targetJournal = this.getOptById(journalMergeDTO.getTargetId()).orElseThrow(() -> new ServiceException("目标期刊不存在"));

        // 检查源期刊是否存在
        List<Journal> sourceJournals = this.listByIds(journalMergeDTO.getSourceIds());

        if (CollUtil.isEmpty(sourceJournals)) {
            throw new ServiceException("部分源期刊不存在");
        }

        BeanUtil.copyProperties(journalMergeDTO, targetJournal);
        // 检查是否为空对象 {}
        if (CollUtil.isEmpty(journalMergeDTO.getUniqueHistory())) {
            targetJournal.setUniqueHistory(null);
        } else {
            targetJournal.setUniqueHistory(CollUtil.distinct(journalMergeDTO.getUniqueHistory()));
        }
        if (CollUtil.isEmpty(journalMergeDTO.getIssnHistory())) {
            targetJournal.setIssnHistory(null);
        } else {
            targetJournal.setIssnHistory(CollUtil.distinct(journalMergeDTO.getIssnHistory()));
        }

        // 更新文献中的期刊ID
        articleMapper.updateJournalIdBatch(journalMergeDTO.getTargetId(), journalMergeDTO.getSourceIds());

        // 更新任务论文表中的出版社ID
        tbDdsTaskPaperMapper.updateJournalIdBatch(journalMergeDTO.getTargetId(), journalMergeDTO.getSourceIds());

        // 删除脚本标签期刊关联表中的记录
        tbDdsScriptlabelJournalMapper.deleteByJournalIds(journalMergeDTO.getSourceIds());

        this.removeByIds(journalMergeDTO.getSourceIds());

        targetJournal.setUpdateTime(new Date());
        return this.updateById(targetJournal);
    }

}
