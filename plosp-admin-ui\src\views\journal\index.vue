<template>
  <div class="app-container">
    <!-- 查询条件表单 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="120px" class="search-form">
      <div class="form-row">
        <el-form-item label="期刊名称(多查询)" prop="titleMultiple">
          <el-autocomplete
            v-model="queryParams.titleMultiple"
            type="textarea"
            :rows="1"
            :fetch-suggestions="getJournalTitleSuggestions"
            placeholder="请输入多个出版社名称"
            clearable
            style="width: 200px;"
            @keydown="handleKeyDown"
            @select="handleJournalTitleSelect"
            @clear="handleJournalTitleClear"
          />
        </el-form-item>
        <el-form-item label="期刊名称" prop="title">
          <el-input v-model="queryParams.title" placeholder="请输入期刊名称" clearable class="search-input" @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item label="期刊简称" prop="isoabbreviation">
          <el-input v-model="queryParams.isoabbreviation" placeholder="请输入期刊简称" clearable class="search-input" @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item label="出版社名称" prop="publisher">
          <el-autocomplete
            v-model="queryParams.publisher"
            :fetch-suggestions="getPublisherSuggestions"
            placeholder="请输入出版社名称"
            clearable
            class="search-input"
            @keyup.enter="handleQuery"
            @select="handlePublisherSelect"
            @clear="handlePublisherClear"
          />
        </el-form-item>
        <el-form-item label="Unique NLM ID" prop="uniqueNlmId">
          <el-input v-model="queryParams.uniqueNlmId" placeholder="请输入Unique NLM ID" clearable class="search-input" @keyup.enter="handleQuery" />
        </el-form-item>
        
      </div>
      <div class="form-row">
        <el-form-item label="ISSN Print" prop="issnPrint">
          <el-input v-model="queryParams.issnPrint" placeholder="请输入ISSN Print" clearable class="search-input" @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item label="ISSN Electronic" prop="issnElectronic">
          <el-input v-model="queryParams.issnElectronic" placeholder="请输入ISSN Electronic" clearable class="search-input" @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item label="来源" prop="source">
          <el-select v-model="queryParams.source" placeholder="请选择来源" clearable class="search-input">
            <el-option label="PubMed" value="PubMed" />
            <el-option label="PMC" value="PMC" />
            <el-option label="JCR" value="JCR" />
            <el-option label="中科院" value="中科院" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable class="search-input">
            <el-option label="正常" value="正常" />
            <el-option label="停用" value="停用" />
          </el-select>
        </el-form-item>
        <div>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </div>

      </div>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mt10">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Sort"
          :disabled="!canMerge"
          @click="openMergeDetailDialog">
          批量合并
        </el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" :columns="columns" @query-table="getJournalList" />
    </el-row>

    <!-- 期刊表格列表 -->
    <div class="journal-list" v-loading="loading">
      <el-empty v-if="journalList.length === 0" description="暂无数据" />
      <el-table
        v-else
        :data="journalList"
        border
        style="width: 100%"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
        :row-key="row => row.id"
      >
        <el-table-column type="selection" width="45" />
        <el-table-column v-if="columns[0].visible" prop="title" sortable label="期刊名称" min-width="140">
          <template #default="scope">
            <el-popover
              placement="top"
              :width="300"
              trigger="hover"
              :content="scope.row.title"
            >
              <template #reference>
                <span class="text-ellipsis">{{ scope.row.title }}</span>
              </template>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column v-if="columns[1].visible" prop="isoabbreviation" sortable label="期刊简称" min-width="120">
          <template #default="scope">
            <el-popover
              placement="top"
              :width="250"
              trigger="hover"
              :content="scope.row.isoabbreviation"
            >
              <template #reference>
                <span class="text-ellipsis">{{ scope.row.isoabbreviation }}</span>
              </template>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column v-if="columns[2].visible" prop="publisherName" label="出版社名称" min-width="130">
          <template #default="scope">
            <el-popover
              placement="top"
              :width="280"
              trigger="hover"
              :content="scope.row.publisherName"
            >
              <template #reference>
                <span class="text-ellipsis">{{ scope.row.publisherName }}</span>
              </template>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column v-if="columns[3].visible" prop="issnPrint" label="ISSN Print" sortable min-width="100">
          <template #default="scope">
            <span>{{ Array.isArray(scope.row.issnPrint) && scope.row.issnPrint.length > 0 ? scope.row.issnPrint[0] : scope.row.issnPrint }}</span>
          </template>
        </el-table-column>
        <el-table-column v-if="columns[4].visible" prop="issnElectronic" label="ISSN Electronic" sortable min-width="120">
          <template #default="scope">
            <span>{{ Array.isArray(scope.row.issnElectronic) && scope.row.issnElectronic.length > 0 ? scope.row.issnElectronic[0] : scope.row.issnElectronic }}</span>
          </template>
        </el-table-column>
        <el-table-column v-if="columns[5].visible" label="历史ISSN" sortable width="110">
          <template #default="scope">
            <template v-if="scope.row.issnHistory && scope.row.issnHistory.length > 0">
              <span>{{ scope.row.issnHistory[0] }}</span>
              <el-tooltip v-if="scope.row.issnHistory.length > 1" placement="top">
                <template #content>
                  <div v-for="(issn, index) in scope.row.issnHistory.slice(1)" :key="index">{{ issn }}</div>
                </template>
                <el-icon class="more-icon"><Fold /></el-icon>
              </el-tooltip>
            </template>
            <span v-else class="text-gray-400"></span>
          </template>
        </el-table-column>
        <el-table-column v-if="columns[6].visible" prop="uniqueNlmId" sortable label="Unique NLM ID" min-width="120" />
        <el-table-column v-if="columns[7].visible" prop="updateTime" label="更新时间" width="160" />
        <el-table-column v-if="columns[8].visible" label="历史Unique NLM ID" width="160">
          <template #default="scope">
            <template v-if="scope.row.uniqueHistory && scope.row.uniqueHistory.length > 0">
              <span>{{ scope.row.uniqueHistory[0] }}</span>
              <el-tooltip v-if="scope.row.uniqueHistory.length > 1" placement="top">
                <template #content>
                  <div v-for="(nlmId, index) in scope.row.uniqueHistory.slice(1)" :key="index">{{ nlmId }}</div>
                </template>
                <el-icon class="more-icon"><Fold /></el-icon>
              </el-tooltip>
            </template>
            <span v-else class="text-gray-400"></span>
          </template>
        </el-table-column>
        <el-table-column v-if="columns[9].visible" prop="medlineTa" sortable label="MedlineTA" width="115">
          <template #default="scope">
            <el-popover
              placement="top"
              :width="200"
              trigger="hover"
              :content="scope.row.medlineTa"
            >
              <template #reference>
                <span class="text-ellipsis">{{ scope.row.medlineTa }}</span>
              </template>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column v-if="columns[10].visible" label="来源" width="100">
          <template #default="scope">
            <template v-if="scope.row.source && scope.row.source.length > 0">
              <el-tag v-for="src in scope.row.source" :key="src" size="small" style="margin-right: 4px;">{{ src }}</el-tag>
            </template>
            <span v-else></span>
          </template>
        </el-table-column>
        <el-table-column v-if="columns[11].visible" prop="status" label="状态" min-width="58">
          <template #default="scope">
            <el-tag v-if="scope.row.status === '正常'" type="success">正常</el-tag>
            <el-tag v-else type="info">停用</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="120" fixed="right">
          <template #default="scope">
            <el-button type="primary" link icon="Edit" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button :type="scope.row.status === '正常' ? 'danger' : 'success'" link :icon="scope.row.status === '正常' ? 'CircleClose' : 'CircleCheck'" @click="toggleStatus(scope.row)">
              {{ scope.row.status === '正常' ? '停用' : '启用' }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getJournalList" />
    </div>

    <!-- 编辑期刊弹窗 -->
    <el-dialog v-model="editDialog.visible" title="编辑期刊" width="700px" append-to-body>
      <el-form ref="editFormRef" :model="editDialog.form" label-width="150px" :rules="rules">
        <el-form-item label="期刊名称" prop="title">
          <el-input v-model="editDialog.form.title" placeholder="请输入期刊名称" />
        </el-form-item>
        <el-form-item label="期刊简称" prop="isoabbreviation">
          <el-input v-model="editDialog.form.isoabbreviation" placeholder="请输入期刊简称" />
        </el-form-item>
        <el-form-item label="出版社名称" prop="publisherName">
          <el-autocomplete
            v-model="editDialog.form.publisherName"
            :fetch-suggestions="getPublisherSuggestions"
            placeholder="请输入出版社名称"
            clearable
            style="width: 100%"
            @select="handleEditPublisherSelect"
            @clear="handleEditPublisherClear"
          />
        </el-form-item>

        <el-form-item label="ISSN Print" prop="issnPrint">
          <el-input v-model="editDialog.form.issnPrint" placeholder="请输入ISSN Print" />
        </el-form-item>
        <el-form-item label="ISSN Electronic" prop="issnElectronic">
          <el-input v-model="editDialog.form.issnElectronic" placeholder="请输入ISSN Electronic" />
        </el-form-item>

        <el-form-item label="Unique NLM ID" prop="uniqueNlmId">
          <el-input v-model="editDialog.form.uniqueNlmId" placeholder="请输入Unique NLM ID" />
        </el-form-item>
        <el-form-item label="历史ISSN" prop="issnHistory">
          <el-select
            v-model="editDialog.form.issnHistory"
            multiple
            filterable
            allow-create
            default-first-option
            placeholder="请输入或选择历史ISSN">
            <el-option
              v-for="item in editDialog.form.issnHistory"
              :key="item"
              :label="item"
              :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="历史Unique NLM ID" prop="uniqueHistory">
          <el-select
            v-model="editDialog.form.uniqueHistory"
            multiple
            filterable
            allow-create
            default-first-option
            placeholder="请输入或选择历史Unique NLM ID">
            <el-option
              v-for="item in editDialog.form.uniqueHistory"
              :key="item"
              :label="item"
              :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="MedlineTA" prop="medlineTa">
          <el-input v-model="editDialog.form.medlineTa" placeholder="请输入MedlineTA" />
        </el-form-item>

        <el-form-item label="来源" prop="source">
          <el-select v-model="editDialog.form.source" multiple placeholder="请选择来源">
            <el-option label="PubMed" value="PubMed" />
            <el-option label="PMC" value="PMC" />
            <el-option label="JCR" value="JCR" />
            <el-option label="ZKY" value="ZKY" />
          </el-select>
        </el-form-item>


      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitEdit">保存</el-button>
          <el-button @click="cancelEdit">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 合并详情弹窗 -->
    <el-dialog v-model="mergeDetailDialog.visible" title="合并详情" width="700px" append-to-body :close-on-click-modal="false">
      <el-form :model="mergeDetailDialog.form" label-width="150px">
        <el-form-item label="期刊名称">
          <el-autocomplete
            v-model="mergeDetailDialog.form.title"
            :fetch-suggestions="getSelectedJournalTitles"
            placeholder="请输入或选择期刊名称"
            style="width: 100%" />
        </el-form-item>
        <el-form-item label="期刊简称">
          <el-autocomplete
            v-model="mergeDetailDialog.form.isoabbreviation"
            :fetch-suggestions="getSelectedJournalAbbreviations"
            placeholder="请输入或选择期刊简称"
            style="width: 100%" />
        </el-form-item>
        <el-form-item label="出版社名称">
          <el-autocomplete
            v-model="mergeDetailDialog.form.publisherName"
            :fetch-suggestions="getSelectedPublisherNames"
            placeholder="请输入或选择出版社"
            style="width: 100%" />
        </el-form-item>
        <el-form-item label="ISSN Print">
          <el-autocomplete
            v-model="mergeDetailDialog.form.issnPrint"
            :fetch-suggestions="getSelectedIssnPrint"
            placeholder="请输入或选择ISSN Print"
            style="width: 100%" />
        </el-form-item>
        <el-form-item label="ISSN Electronic">
          <el-autocomplete
            v-model="mergeDetailDialog.form.issnElectronic"
            :fetch-suggestions="getSelectedIssnElectronic"
            placeholder="请输入或选择ISSN Electronic"
            style="width: 100%" />
        </el-form-item>

        <el-form-item label="Unique NLM ID">
          <el-autocomplete
            v-model="mergeDetailDialog.form.uniqueNlmId"
            :fetch-suggestions="getSelectedUniqueNlmId"
            placeholder="请输入或选择Unique NLM ID"
            style="width: 100%" />
        </el-form-item>
        <el-form-item label="历史ISSN">
          <el-select
            v-model="mergeDetailDialog.form.issnHistory"
            multiple
            filterable
            allow-create
            default-first-option
            placeholder="请输入或选择历史ISSN"
            style="width: 100%">
            <el-option
              v-for="item in getHistoricalIssnOptions()"
              :key="item"
              :label="item"
              :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="历史Unique NLM ID">
          <el-select
            v-model="mergeDetailDialog.form.uniqueHistory"
            multiple
            filterable
            allow-create
            default-first-option
            placeholder="请输入或选择历史Unique NLM ID"
            style="width: 100%">
            <el-option
              v-for="item in getHistoricalNlmIdOptions()"
              :key="item"
              :label="item"
              :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="MedlineTA">
          <el-autocomplete
            v-model="mergeDetailDialog.form.medlineTa"
            :fetch-suggestions="getSelectedMedlineTa"
            placeholder="请输入或选择MedlineTA"
            style="width: 100%" />
        </el-form-item>

        <el-form-item label="来源">
          <el-select v-model="mergeDetailDialog.form.source" multiple placeholder="请选择来源" style="width: 100%">
            <el-option label="PubMed" value="PubMed" />
            <el-option label="PMC" value="PMC" />
            <el-option label="JCR" value="JCR" />
            <el-option label="ZKY" value="ZKY" />
          </el-select>
        </el-form-item>

        <el-form-item label="脚本">
          <el-select
            v-model="mergeDetailDialog.form.scriptId"
            placeholder="请选择脚本"
            style="width: 100%"
            clearable>
            <el-option
              v-for="script in getSelectedScripts()"
              :key="script.id"
              :label="script.name"
              :value="script.id" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitMergeDetail">保存</el-button>
          <el-button @click="mergeDetailDialog.visible = false">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 期刊详情弹窗 -->
    <el-dialog v-model="detailDialog.visible" title="期刊详情" width="500px" append-to-body>
      <div class="journal-detail">
        <div class="detail-row"><span class="label">期刊名称：</span>{{ detailDialog.data.journalName }}</div>
        <div class="detail-row"><span class="label">期刊简称：</span>{{ detailDialog.data.abbreviation }}</div>
        <div class="detail-row"><span class="label">Unique NLM ID：</span>{{ detailDialog.data.nlmId }}</div>
        <div class="detail-row">
          <span class="label">ISSN Print：</span>
          <span v-if="Array.isArray(detailDialog.data.issnPrint)">{{ detailDialog.data.issnPrint.join(', ') }}</span>
          <span v-else>{{ detailDialog.data.issnPrint }}</span>
        </div>
        <div class="detail-row">
          <span class="label">ISSN Electronic：</span>
          <span v-if="Array.isArray(detailDialog.data.issnElectronic)">{{ detailDialog.data.issnElectronic.join(', ') }}</span>
          <span v-else>{{ detailDialog.data.issnElectronic }}</span>
        </div>
        <div class="detail-row"><span class="label">出版社：</span>{{ detailDialog.data.publisherName }}</div>
        <div class="detail-row">
          <span class="label">历史ISSN：</span>
          <span v-if="Array.isArray(detailDialog.data.historicalIssn)">{{ detailDialog.data.historicalIssn.join(', ') }}</span>
          <span v-else>{{ detailDialog.data.historicalIssn }}</span>
        </div>
        <div class="detail-row">
          <span class="label">历史Unique NLM ID：</span>
          <span v-if="Array.isArray(detailDialog.data.historicalNlmId)">{{ detailDialog.data.historicalNlmId.join(', ') }}</span>
          <span v-else>{{ detailDialog.data.historicalNlmId }}</span>
        </div>
        <div class="detail-row"><span class="label">MedlineTA：</span>{{ detailDialog.data.medlineTa }}</div>
        <div class="detail-row"><span class="label">来源：</span>{{ detailDialog.data.source?.join(', ') }}</div>
        <div class="detail-row"><span class="label">状态：</span>{{ detailDialog.data.status }}</div>

      </div>
    </el-dialog>
  </div>
</template>

<script setup name="Journal">
import { ref, reactive, getCurrentInstance, watch, computed } from 'vue';
import { Edit, Switch, Sort, MoreFilled, Fold } from '@element-plus/icons-vue';
import { listJournal, updateJournal, changeJournalStatus, mergeJournals } from '@/api/article/journal';
import { listPublisher } from '@/api/article/publisher';

const { proxy } = getCurrentInstance();

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  publisher: '',
  publisherId: '', 
  titleMultiple: '', 
  title: '',   
  isoabbreviation: '',
  uniqueNlmId: '',
  issnPrint: '',
  issnElectronic: '',

  source: '',
  status: '',
  orderByColumn: '',  // 排序字段
  isAsc: ''          // 排序方向：asc 或 desc
});
const showSearch = ref(true);
const loading = ref(false);
const total = ref(0);
const journalList = ref([]);
const dateRange = ref([]);



// 列显隐信息
const columns = ref([
  { key: 0, label: `期刊名称`, visible: true },
  { key: 1, label: `期刊简称`, visible: true },
  { key: 2, label: `出版社名称`, visible: true },
  { key: 3, label: `ISSN Print`, visible: true },
  { key: 4, label: `ISSN Electronic`, visible: true },
  { key: 5, label: `历史ISSN`, visible: false },
  { key: 6, label: `Unique NLM ID`, visible: false },
  { key: 7, label: `更新时间`, visible: true },
  { key: 8, label: `历史Unique NLM ID`, visible: false },
  { key: 9, label: `MedlineTA`, visible: false },
  { key: 10, label: `来源`, visible: true },
  { key: 11, label: `状态`, visible: true }
]);

// 编辑弹窗
const editDialog = ref({
  visible: false,
  form: {}
});
const rules = {
  title: [{ required: true, message: '请输入期刊名称', trigger: 'blur' }]
};

// 详情弹窗
const detailDialog = ref({
  visible: false,
  data: {}
});

// 合并期刊相关
const mergeDetailDialog = ref({
  visible: false,
  form: {
    targetId: null,           // 目标期刊ID
    sourceIds: [],            // 源期刊ID列表
    title: '',                // 合并后的期刊名称
    isoabbreviation: '',      // 合并后的期刊简称
    publisherName: '',        // 合并后的出版社名称
    publisherId: null,        // 合并后的出版社ID
    issnPrint: '',            // 合并后的ISSN Print
    issnElectronic: '',       // 合并后的ISSN Electronic
    uniqueNlmId: '',          // 合并后的Unique NLM ID
    issnHistory: [],          // 合并后的历史ISSN
    uniqueHistory: [],        // 合并后的历史Unique NLM ID
    medlineTa: '',            // 合并后的MedlineTA
    source: [],               // 合并后的来源
    scriptId: null            // 合并后的脚本ID
  }
});

// 记录所有已选中的期刊（合并用）
const selectedIds = ref([]);

function handleSelectionChange(selection) {
  selectedIds.value = selection.map(row => row.id);
}

const hasMergeSelection = computed(() => selectedIds.value.length > 0);

// 计算是否可以进行合并（至少选择1个期刊）
const canMerge = computed(() => selectedIds.value.length >= 1);

function openMergeDetailDialog() {
  // 如果没有选中任何期刊，显示提示并返回
  if (selectedIds.value.length < 2) {
    proxy.$modal.msgWarning('请至少选择两个期刊进行合并');
    return;
  }

  // 设置目标期刊ID（第一个选中的期刊）和源期刊ID列表（其余选中的期刊）
  const targetId = selectedIds.value[0];
  const sourceIds = selectedIds.value.slice(1);

  // 设置合并表单，不填入默认值，让用户通过智能建议选择
  mergeDetailDialog.value.form = {
    targetId: targetId,
    sourceIds: sourceIds,
    title: '',                // 不设置默认值
    isoabbreviation: '',      // 不设置默认值
    publisherName: '',        // 不设置默认值
    publisherId: null,
    issnPrint: '',            // 不设置默认值
    issnElectronic: '',       // 不设置默认值
    uniqueNlmId: '',          // 不设置默认值
    issnHistory: [],
    uniqueHistory: [],
    medlineTa: '',            // 不设置默认值
    source: [],
    scriptId: null            // 不设置默认值
  };

  // 显示合并详情弹窗
  mergeDetailDialog.value.visible = true;
}

// 确保值为数组
function ensureArray(value) {
  if (!value) return [];
  return Array.isArray(value) ? value : [value];
}

// 获取选中期刊的名称建议
function getSelectedJournalTitles(queryString, cb) {
  const selectedJournals = journalList.value.filter(j => selectedIds.value.includes(j.id));
  const titles = selectedJournals
    .map(j => j.title)
    .filter(title => title && (!queryString || title.toLowerCase().includes(queryString.toLowerCase())))
    .map(title => ({ value: title }));
  cb(titles);
}

// 获取选中期刊的简称建议
function getSelectedJournalAbbreviations(queryString, cb) {
  const selectedJournals = journalList.value.filter(j => selectedIds.value.includes(j.id));
  const abbreviations = selectedJournals
    .map(j => j.isoabbreviation)
    .filter(abbr => abbr && (!queryString || abbr.toLowerCase().includes(queryString.toLowerCase())))
    .map(abbr => ({ value: abbr }));
  cb(abbreviations);
}

// 获取选中期刊的出版社名称建议
function getSelectedPublisherNames(queryString, cb) {
  const selectedJournals = journalList.value.filter(j => selectedIds.value.includes(j.id));
  const publisherNames = [...new Set(selectedJournals
    .map(j => j.publisherName)
    .filter(name => name && (!queryString || name.toLowerCase().includes(queryString.toLowerCase()))))]
    .map(name => ({ value: name }));
  cb(publisherNames);
}

// 获取选中期刊的ISSN Print建议
function getSelectedIssnPrint(queryString, cb) {
  const selectedJournals = journalList.value.filter(j => selectedIds.value.includes(j.id));
  const issnPrints = [...new Set(selectedJournals
    .map(j => {
      // 处理数组和字符串两种情况，与表格显示逻辑一致
      return Array.isArray(j.issnPrint) && j.issnPrint.length > 0 ? j.issnPrint[0] : j.issnPrint;
    })
    .filter(issn => issn && typeof issn === 'string' && (!queryString || issn.includes(queryString))))]
    .map(issn => ({ value: issn }));
  cb(issnPrints);
}

// 获取选中期刊的ISSN Electronic建议
function getSelectedIssnElectronic(queryString, cb) {
  const selectedJournals = journalList.value.filter(j => selectedIds.value.includes(j.id));
  const issnElectronics = [...new Set(selectedJournals
    .map(j => {
      // 处理数组和字符串两种情况，与表格显示逻辑一致
      return Array.isArray(j.issnElectronic) && j.issnElectronic.length > 0 ? j.issnElectronic[0] : j.issnElectronic;
    })
    .filter(issn => issn && typeof issn === 'string' && (!queryString || issn.includes(queryString))))]
    .map(issn => ({ value: issn }));
  cb(issnElectronics);
}

// 获取选中期刊的Unique NLM ID建议
function getSelectedUniqueNlmId(queryString, cb) {
  const selectedJournals = journalList.value.filter(j => selectedIds.value.includes(j.id));
  const uniqueNlmIds = selectedJournals
    .map(j => j.uniqueNlmId)
    .filter(id => id && (!queryString || id.includes(queryString)))
    .map(id => ({ value: id }));
  cb(uniqueNlmIds);
}

// 获取选中期刊的MedlineTA建议
function getSelectedMedlineTa(queryString, cb) {
  const selectedJournals = journalList.value.filter(j => selectedIds.value.includes(j.id));
  const medlineTas = selectedJournals
    .map(j => j.medlineTa)
    .filter(ta => ta && (!queryString || ta.toLowerCase().includes(queryString.toLowerCase())))
    .map(ta => ({ value: ta }));
  cb(medlineTas);
}

// 获取选中期刊的脚本选项
function getSelectedScripts() {
  const selectedJournals = journalList.value.filter(j => selectedIds.value.includes(j.id));

  // 收集所有选中期刊的脚本信息，去重
  const scriptMap = new Map();
  selectedJournals.forEach(journal => {
    if (journal.scriptId && journal.scriptName) {
      scriptMap.set(journal.scriptId, {
        id: journal.scriptId,
        name: journal.scriptName
      });
    }
  });

  return Array.from(scriptMap.values());
}

// 获取历史ISSN选项
function getHistoricalIssnOptions() {
  const selectedJournals = journalList.value.filter(j => selectedIds.value.includes(j.id));

  // 收集所有选中期刊的历史ISSN值
  const allValues = [];
  selectedJournals.forEach(journal => {
    const values = ensureArray(journal.issnHistory);  // 修正字段名
    values.forEach(value => {
      if (value && !allValues.includes(value)) {
        allValues.push(value);
      }
    });
  });

  return allValues;
}

// 获取历史NLM ID选项
function getHistoricalNlmIdOptions() {
  const selectedJournals = journalList.value.filter(j => selectedIds.value.includes(j.id));

  // 收集所有选中期刊的历史NLM ID值
  const allValues = [];
  selectedJournals.forEach(journal => {
    const values = ensureArray(journal.uniqueHistory);  // 修正字段名
    values.forEach(value => {
      if (value && !allValues.includes(value)) {
        allValues.push(value);
      }
    });
  });

  return allValues;
}

function submitMergeDetail() {
  // 验证必填字段
  if (!mergeDetailDialog.value.form.title) {
    proxy.$modal.msgError('请输入期刊名称');
    return;
  }

  if (!mergeDetailDialog.value.form.targetId || mergeDetailDialog.value.form.sourceIds.length === 0) {
    proxy.$modal.msgError('合并数据异常，请重新选择期刊');
    return;
  }

  // 调用后端合并接口
  mergeJournals(mergeDetailDialog.value.form).then(response => {
    proxy.$modal.msgSuccess('期刊合并成功');
    mergeDetailDialog.value.visible = false;
    // 清空选中状态
    selectedIds.value = [];
    // 重新加载列表
    getJournalList();
  }).catch(error => {
    console.error('期刊合并失败:', error);
    proxy.$modal.msgError('期刊合并失败');
  });
}

// 状态转换函数
function statusToNumber(status) {
  if (status === '正常') return 1;  // 返回数字类型
  if (status === '停用') return 0;  // 返回数字类型
  return status; // 如果是其他值，直接返回
}

function statusToText(status) {
  // 转换为字符串进行比较，处理各种类型
  const statusStr = String(status);
  if (statusStr === '1') return '正常';
  if (statusStr === '0') return '停用';
  return status; // 如果是其他值，直接返回
}

// 查询期刊列表
function getJournalList() {
  loading.value = true;

  // 处理多个期刊名称查询
  let processedTitleMultiple = queryParams.value.titleMultiple;
  if (processedTitleMultiple) {
    // 按行分割，去除每行的首尾空白，过滤掉空行
    const lines = processedTitleMultiple.split('\n').map(item => item.trim());
    const filteredLines = lines.filter(item => item !== '');
    processedTitleMultiple = filteredLines;
  }

  // 构建查询参数
  const params = {
    pageNum: queryParams.value.pageNum,
    pageSize: queryParams.value.pageSize,
    publisherId: queryParams.value.publisherId ? Number(queryParams.value.publisherId) : null,  // 转换为数字
    publisherName: queryParams.value.publisher,  // 添加出版社名称查询
    titleMultiple: processedTitleMultiple,  // 多个期刊名称查询（处理后的数组）
    title: queryParams.value.title,
    isoabbreviation: queryParams.value.isoabbreviation,
    uniqueNlmId: queryParams.value.uniqueNlmId,
    issnPrint: queryParams.value.issnPrint,
    issnElectronic: queryParams.value.issnElectronic,
    source: queryParams.value.source,
    orderByColumn: queryParams.value.orderByColumn,
    isAsc: queryParams.value.isAsc,
    status: statusToNumber(queryParams.value.status) // 转换状态值
  };

  // 添加时间范围参数
  if (dateRange.value && dateRange.value.length === 2) {
    params.beginTime = dateRange.value[0];
    params.endTime = dateRange.value[1];
  }

  // 调试信息
  console.log('发送给后端的参数:', params);

  // 调用API
  listJournal(params).then(response => {
    // 转换后端返回的状态值为前端显示的文字
    const processedRows = response.rows.map(row => ({
      ...row,
      status: statusToText(row.status)
    }));
    journalList.value = processedRows;
    total.value = response.total;
    loading.value = false;
  }).catch(error => {
    console.error('获取期刊列表失败:', error);
    loading.value = false;
    proxy.$modal.msgError('获取期刊列表失败');
  });
}

 /** 排序触发事件 */
  function handleSortChange(column, prop, order) {
    queryParams.value.orderByColumn = column.prop;
    queryParams.value.isAsc = column.order;
    getJournalList();
  }

function handleQuery() {
  queryParams.value.pageNum = 1;
  getJournalList();
}
function resetQuery() {
  Object.keys(queryParams.value).forEach(k => {
    if (k === 'pageNum' || k === 'pageSize') return;
    queryParams.value[k] = '';
  });
  handleQuery();
}

// 出版社自动完成建议
function getPublisherSuggestions(queryString, callback) {
  if (!queryString) {
    callback([]);
    return;
  }

  // 调用出版社API获取出版社列表
  listPublisher({ name: queryString }).then(response => {
    // 将后端返回的数据转换为el-autocomplete需要的格式
    const suggestions = response.rows.map(publisher => ({
      value: publisher.name,  // 显示的值
      id: publisher.id        // 存储的ID
    }));
    callback(suggestions);
  }).catch(error => {
    console.error('获取出版社列表失败:', error);
    callback([]);
  });
}

// 处理出版社选择
function handlePublisherSelect(item) {
  queryParams.value.publisherId = item.id;  // 存储选中的出版社ID
  handleQuery();  // 自动触发查询
}

// 处理出版社清空
function handlePublisherClear() {
  queryParams.value.publisherId = '';  // 清空出版社ID
  handleQuery();  // 自动触发查询
}

// 处理键盘事件
function handleKeyDown(event) {
  // 如果是 Enter 键且没有按 Shift，则触发搜索
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault(); // 阻止默认的换行行为
    handleQuery();
  }
  // 如果是 Shift+Enter，则允许默认的换行行为
}

// 期刊名称自动完成建议
function getJournalTitleSuggestions(queryString, callback) {
  if (!queryString) {
    callback([]);
    return;
  }

  // 获取当前行的内容（处理多行输入的情况）
  const lines = queryString.split('\n');
  const currentLine = lines[lines.length - 1].trim();

  if (!currentLine) {
    callback([]);
    return;
  }

  // 从当前加载的期刊列表中获取建议
  const suggestions = journalList.value
    .filter(journal => journal.title && journal.title.toLowerCase().includes(currentLine.toLowerCase()))
    .map(journal => ({ value: journal.title }))
    .slice(0, 10); // 限制显示10个建议

  callback(suggestions);
}

// 处理期刊名称选择
function handleJournalTitleSelect(item) {
  // 将选中的期刊名称添加到当前输入中
  const currentValue = queryParams.value.titleMultiple || '';
  const lines = currentValue.split('\n');

  // 如果最后一行不为空，则在新行添加
  if (lines[lines.length - 1].trim() !== '') {
    queryParams.value.titleMultiple = currentValue + '\n' + item.value;
  } else {
    // 替换最后一行
    lines[lines.length - 1] = item.value;
    queryParams.value.titleMultiple = lines.join('\n');
  }
}

// 处理期刊名称清空
function handleJournalTitleClear() {
  // 清空后触发查询
  handleQuery();
}





// 处理编辑表单中的出版社选择
function handleEditPublisherSelect(item) {
  editDialog.value.form.publisherId = item.id;  // 存储选中的出版社ID
}

// 处理编辑表单中的出版社清空
function handleEditPublisherClear() {
  editDialog.value.form.publisherId = '';  // 清空出版社ID
}

//编辑期刊
function handleEdit(row) {
  editDialog.value.visible = true;
  // 确保数组字段是数组，并映射字段名
  const formData = {
    id: row.id,
    title: row.title,
    isoabbreviation: row.isoabbreviation,
    publisherId: row.publisherId,
    publisherName: row.publisherName,
    issnPrint: row.issnPrint,
    issnElectronic: row.issnElectronic,
    uniqueNlmId: row.uniqueNlmId,
    issnHistory: ensureArray(row.issnHistory),
    uniqueHistory: ensureArray(row.uniqueHistory),
    medlineTa: row.medlineTa || '',  // 确保不为 undefined
    source: ensureArray(row.source)
    // 移除 status 字段，编辑时不允许修改状态
  };
  editDialog.value.form = formData;
}
function submitEdit() {
  proxy.$refs.editFormRef.validate(valid => {
    if (!valid) return;

    // 直接提交表单数据，不包含状态字段
    const submitData = { ...editDialog.value.form };

    // 调用后端更新接口
    updateJournal(submitData).then(response => {
      proxy.$modal.msgSuccess('保存成功');
      editDialog.value.visible = false;
      // 重新加载列表
      getJournalList();
    }).catch(error => {
      console.error('更新期刊失败:', error);
      proxy.$modal.msgError('保存失败');
    });
  });
}
function cancelEdit() {
  editDialog.value.visible = false;
  proxy.$refs.editFormRef?.resetFields();
}
function toggleStatus(item) {
  // 确定新状态和提示信息
  const currentStatus = item.status;
  const newStatusText = currentStatus === '正常' ? '停用' : '启用';
  const newStatusValue = currentStatus === '正常' ? 0 : 1;

  // 显示确认弹框
  proxy.$modal.confirm(`确定要${newStatusText}期刊"${item.title}"吗？`).then(() => {
    // 用户确认后，调用专门的状态修改接口
    changeJournalStatus(item.id, newStatusValue).then(response => {
      proxy.$modal.msgSuccess(`期刊已${newStatusText}`);
      // 重新加载列表
      getJournalList();
    }).catch(error => {
      console.error('更新状态失败:', error);
      proxy.$modal.msgError('状态更新失败');
    });
  }).catch(() => {
    // 用户取消，不做任何操作
  });
}

// 期刊详情
function viewJournal(item) {
  detailDialog.value.data = { ...item };
  detailDialog.value.visible = true;
}

// 格式化日期
function formatDate(val) {
  if (!val) return '';
  return val.split(' ')[0];
}



getJournalList();
</script>

<style lang="scss" scoped>
.app-container {
  padding: 10px;
  padding-bottom: 40px;
}
.mb8 {
  margin-bottom: 8px;
}
.mt10 {
  margin-top: 10px;
}
.search-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
  .form-row {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 8px;
    .el-form-item {
      margin-bottom: 0;
      margin-right: 16px;
    }
    .search-input {
      width: 200px;
    }
    .action-btns {
      display: flex;
      gap: 8px;
      margin-left: auto;
    }
  }
}
.journal-list {
  margin-top: 10px;
}
.journal-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}
.journal-item {
  display: flex;
  align-items: flex-start;
  background: #fff;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 16px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
  transition: box-shadow 0.2s;
  &:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  }
  .card-checkbox {
    margin-right: 16px;
    margin-top: 4px;
  }
  .item-main {
    flex: 1;
    .item-title-row {
      display: flex;
      align-items: center;
      gap: 10px;
      font-size: 16px;
      font-weight: bold;
      color: #303133;
      margin-bottom: 6px;
      .partition, .impact-factor {
        font-size: 14px;
        font-weight: normal;
        color: #909399;
      }
      .source-tags {
        margin-left: 8px;
        display: flex;
        gap: 4px;
      }
    }
    .item-info-row {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
      font-size: 14px;
      color: #606266;
    }
  }
  .item-ops {
    width: 100px;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 8px;
    .el-button {
      padding: 4px 0;
      margin-left: 0;
    }
  }
}
// 父节点背景色
@for $i from 0 through 5 {
  .el-table__row.merge-row-bg-#{$i} > td {
    background: nth((#f6ffed, #e6f7ff, #fffbe6, #fff0f6, #f9f0ff, #f0f5ff), $i + 1) !important;
  }
}

// 隐藏表格竖直边框线
:deep(.el-table) {
  .el-table__header th,
  .el-table__body td {
    border-right: none !important;
  }

  .el-table__header th:last-child,
  .el-table__body td:last-child {
    border-right: none !important;
  }
}

// 表格内容居中对齐
:deep(.el-table) {
  .el-table__header th .cell,
  .el-table__body td .cell {
    text-align: center !important;
  }
}

// 文本省略样式
.text-ellipsis {
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
}
.merge-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}
.merge-group {
  border-radius: 4px;
  padding: 10px 12px;
  margin-bottom: 8px;
}
.merge-group-title {
  font-weight: bold;
  margin-bottom: 6px;
}
.journal-detail {
  .detail-row {
    margin-bottom: 8px;
    font-size: 15px;
    .label {
      color: #909399;
      margin-right: 8px;
    }
  }
}
.merge-bg-0 {
  background: #f6ffed !important;
}
.merge-bg-1 {
  background: #e6f7ff !important;
}

.merge-filter-form {
  margin-bottom: 10px;
  .el-form-item {
    margin-bottom: 0;
    margin-right: 0;
  }
}

.more-icon {
  top: 2px;
  margin-left: 4px;
  font-size: 14px;
  color: #7C9EFF;
  cursor: pointer;
}

.action-bar {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}
</style>

