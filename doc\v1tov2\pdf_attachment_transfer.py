#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF附件迁移脚本：从v1 MySQL数据库迁移PDF附件到v2 PostgreSQL数据库
迁移表：tb_dds_article_attachment (type='PDF') -> tb_dds_file
迁移范围：pmid >= ************ AND pmid <= ************
"""

import logging
import sys
import traceback
import os
from datetime import datetime

import pymysql
import psycopg2
import psycopg2.extras
from toollib.guid import SnowFlake

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('pdf_migration.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# 数据库配置
V1_MYSQL_CONFIG = {
    'host': '************',  # TODO: 修改为实际的MySQL主机地址
    'port': 32526,
    'user': 'root',  # TODO: 修改为实际的MySQL用户名
    'password': 'Lfgzs@2021',  # TODO: 修改为实际的MySQL密码
    'database': 'plosp_online',
    'charset': 'utf8mb4'
}

V2_PGSQL_CONFIG = {
    'host': '************',  # TODO: 修改为实际的PostgreSQL主机地址
    'port': 31909,
    'user': 'postgres',  # TODO: 修改为实际的PostgreSQL用户名
    'password': 'Biosino+2025',  # TODO: 修改为实际的PostgreSQL密码
    'database': 'plosp',  # TODO: 修改为实际的PostgreSQL数据库名
    'options': '-c search_path=public'  # TODO: 修改为实际的schema名称，如果不是public的话
}

# 迁移范围配置
PMID_MIN = ************
PMID_MAX = ************

# 运行模式配置
DRY_RUN = False  # 设置为True时只预览不实际执行迁移

# 雪花ID生成器
snow = SnowFlake(worker_id=1, datacenter_id=1)


# 用于dry_run模式的模拟ID生成器
class MockIdGenerator:
    def __init__(self, start_id=1000000000000000000):
        self.current_id = start_id

    def gen_uid(self):
        self.current_id += 1
        return self.current_id


mock_snow = MockIdGenerator()


def get_mysql_connection():
    """获取MySQL数据库连接"""
    try:
        connection = pymysql.connect(**V1_MYSQL_CONFIG)
        logger.info("MySQL数据库连接成功")
        return connection
    except Exception as e:
        logger.error(f"MySQL数据库连接失败: {e}")
        raise


def get_pgsql_connection():
    """获取PostgreSQL数据库连接"""
    try:
        connection = psycopg2.connect(**V2_PGSQL_CONFIG)
        logger.info("PostgreSQL数据库连接成功")
        return connection
    except Exception as e:
        logger.error(f"PostgreSQL数据库连接失败: {e}")
        raise


def check_migration_scope():
    """检查迁移范围内的PDF附件数量"""
    mysql_conn = None
    try:
        mysql_conn = get_mysql_connection()
        cursor = mysql_conn.cursor()

        # 检查PDF附件数量
        attachment_query = """
        SELECT count(1) FROM `tb_dds_article_attachment`
        WHERE `pmid` >= %s AND `pmid` <= %s AND type = 'PDF'
        """
        cursor.execute(attachment_query, (PMID_MIN, PMID_MAX))
        attachment_count = cursor.fetchone()[0]
        logger.info(f"待迁移PDF附件数量: {attachment_count}")

        return attachment_count

    except Exception as e:
        logger.error(f"检查迁移范围失败: {e}")
        raise
    finally:
        if mysql_conn:
            mysql_conn.close()


def get_article_id_mapping():
    """获取文章ID映射关系：v1的pmid -> v2的id"""
    pgsql_conn = None
    mapping = {}

    try:
        pgsql_conn = get_pgsql_connection()
        cursor = pgsql_conn.cursor()

        # 查询已迁移的文章，获取custom_id和id的映射关系
        query = """
        SELECT id, custom_id FROM tb_dds_article
        WHERE custom_id >= %s AND custom_id <= %s
        """
        cursor.execute(query, (PMID_MIN, PMID_MAX))

        for row in cursor.fetchall():
            article_id, custom_id = row
            mapping[custom_id] = article_id

        logger.info(f"获取到文章ID映射关系 {len(mapping)} 条")
        return mapping

    except Exception as e:
        logger.error(f"获取文章ID映射关系失败: {e}")
        raise
    finally:
        if pgsql_conn:
            pgsql_conn.close()


def calculate_file_size(file_path):
    """计算文件大小"""
    try:
        if file_path and os.path.exists(file_path):
            return os.path.getsize(file_path)
        else:
            logger.warning(f"文件不存在: {file_path}")
            return None
    except Exception as e:
        logger.warning(f"计算文件大小失败 {file_path}: {e}")
        return None


def migrate_pdf_attachments(dry_run=False):
    """迁移PDF附件数据
    
    Args:
        dry_run: 是否为预览模式，True时不实际执行插入操作
    """
    mysql_conn = None
    pgsql_conn = None
    migrated_count = 0
    failed_count = 0

    try:
        # 在dry_run模式下，创建模拟的文章ID映射关系
        if dry_run:
            logger.info("【DRY RUN模式】创建模拟的文章ID映射关系...")
            article_mapping = {}
            # 为了预览，我们创建一些模拟的映射关系
            for pmid in range(PMID_MIN, min(PMID_MIN + 1000, PMID_MAX + 1)):  # 只模拟前1000个
                article_mapping[pmid] = mock_snow.gen_uid()  # 生成模拟的文章ID
            logger.info(f"【DRY RUN模式】创建了 {len(article_mapping)} 条模拟映射关系")
        else:
            # 实际模式下获取真实的文章ID映射关系
            article_mapping = get_article_id_mapping()
            if not article_mapping:
                logger.warning("没有找到文章ID映射关系，跳过PDF附件迁移")
                return 0, 0

        mysql_conn = get_mysql_connection()
        mysql_cursor = mysql_conn.cursor(pymysql.cursors.DictCursor)

        # 只在非dry_run模式下连接PostgreSQL
        if not dry_run:
            pgsql_conn = get_pgsql_connection()
            pgsql_cursor = pgsql_conn.cursor()
        else:
            pgsql_conn = None
            pgsql_cursor = None

        # 查询v1数据库中的PDF附件数据
        select_query = """
        SELECT id, pmid, type, origin_name, local_path, file_suffix, source,
               description, create_userid, create_siteid, create_time, file_md5
        FROM `tb_dds_article_attachment`
        WHERE `pmid` >= %s AND `pmid` <= %s AND type = 'PDF'
        ORDER BY pmid, create_time
        """

        logger.info("开始查询v1数据库中的PDF附件数据...")
        mysql_cursor.execute(select_query, (PMID_MIN, PMID_MAX))

        # 准备v2数据库的插入语句
        insert_query = """
        INSERT INTO tb_dds_file (
            id, file_name, content_type, file_size, md5, file_path,
            create_time, type, doc_id, source
        ) VALUES (
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
        )
        """

        if dry_run:
            logger.info("【DRY RUN模式】开始预览PDF附件数据迁移...")
        else:
            logger.info("开始迁移PDF附件数据...")
        batch_size = 100
        batch_data = []
        preview_count = 0

        for row in mysql_cursor:
            try:
                # 检查对应的文章是否已迁移
                v1_pmid = row['pmid']
                if v1_pmid not in article_mapping:
                    logger.warning(f"PDF附件对应的文章未找到，跳过 pmid={v1_pmid}, attachment_id={row['id']}")
                    failed_count += 1
                    continue

                # 生成雪花ID作为v2的主键
                if dry_run:
                    new_id = mock_snow.gen_uid()  # 使用模拟ID生成器
                else:
                    new_id = snow.gen_uid()  # 使用真实雪花ID生成器

                # 字段映射和转换
                file_name = row['origin_name']
                content_type = row['file_suffix']  # v1的file_suffix对应v2的content_type
                
                # 计算文件大小
                file_size = None
                if not dry_run:
                    file_size = calculate_file_size(row['local_path'])
                
                md5 = row['file_md5']
                file_path = row['local_path']
                create_time = row['create_time']
                file_type = 'PDF'  # 固定为PDF类型
                doc_id = article_mapping[v1_pmid]  # 对应的文章ID
                source = row['source']

                # 准备插入数据
                insert_data = (
                    new_id, file_name, content_type, file_size, md5, file_path,
                    create_time, file_type, doc_id, source
                )

                batch_data.append(insert_data)

                # 在dry_run模式下，只预览前几条数据
                if dry_run:
                    preview_count += 1
                    if preview_count <= 5:  # 只显示前5条作为预览
                        logger.info(f"【预览】PDF附件数据 #{preview_count}:")
                        logger.info(f"  v1.pmid: {v1_pmid} -> v2.doc_id: {doc_id}")
                        logger.info(f"  文件名: {file_name}")
                        logger.info(f"  文件路径: {file_path}")
                        logger.info(f"  MD5: {md5}")
                        logger.info(f"  来源: {source}")
                    if len(batch_data) >= batch_size:
                        migrated_count += len(batch_data)
                        logger.info(f"【DRY RUN】预览PDF附件数量: {migrated_count}")
                        batch_data = []
                else:
                    # 实际执行批量插入
                    if len(batch_data) >= batch_size:
                        pgsql_cursor.executemany(insert_query, batch_data)
                        pgsql_conn.commit()
                        migrated_count += len(batch_data)
                        logger.info(f"已迁移PDF附件数量: {migrated_count}")
                        batch_data = []

            except Exception as e:
                logger.error(f"迁移PDF附件失败 pmid={row['pmid']}, attachment_id={row['id']}: {e}")
                failed_count += 1
                continue

        # 处理剩余的批次数据
        if batch_data:
            if dry_run:
                migrated_count += len(batch_data)
                logger.info(f"【DRY RUN】预览PDF附件数量: {migrated_count}")
            else:
                pgsql_cursor.executemany(insert_query, batch_data)
                pgsql_conn.commit()
                migrated_count += len(batch_data)

        if dry_run:
            logger.info(f"【DRY RUN】PDF附件预览完成! 预计迁移: {migrated_count}, 失败: {failed_count}")
        else:
            logger.info(f"PDF附件迁移完成! 成功: {migrated_count}, 失败: {failed_count}")
        return migrated_count, failed_count

    except Exception as e:
        logger.error(f"PDF附件迁移过程中发生错误: {e}")
        logger.error(traceback.format_exc())
        if pgsql_conn:
            pgsql_conn.rollback()
        raise
    finally:
        if mysql_conn:
            mysql_conn.close()
        if pgsql_conn:
            pgsql_conn.close()


def main():
    """主函数：执行PDF附件迁移流程"""
    start_time = datetime.now()
    logger.info("=" * 60)
    if DRY_RUN:
        logger.info("【DRY RUN模式】开始预览PDF附件迁移任务")
    else:
        logger.info("开始执行PDF附件迁移任务")
    logger.info(f"迁移范围: pmid >= {PMID_MIN} AND pmid <= {PMID_MAX}")
    logger.info(f"开始时间: {start_time}")
    if DRY_RUN:
        logger.info("注意：当前为预览模式，不会实际修改数据库")
    logger.info("=" * 60)

    try:
        # 步骤1: 检查迁移范围
        logger.info("步骤1: 检查迁移范围内的PDF附件数量...")
        attachment_count = check_migration_scope()

        if attachment_count == 0:
            logger.warning("没有找到需要迁移的PDF附件数据，退出程序")
            return

        # 确认是否继续
        logger.info(f"即将迁移 {attachment_count} 个PDF附件")

        # 步骤2: 迁移PDF附件数据
        if DRY_RUN:
            logger.info("步骤2: 开始预览PDF附件数据...")
        else:
            logger.info("步骤2: 开始迁移PDF附件数据...")
        attachment_migrated, attachment_failed = migrate_pdf_attachments(dry_run=DRY_RUN)

        # 步骤3: 输出迁移结果
        end_time = datetime.now()
        duration = end_time - start_time

        logger.info("=" * 60)
        if DRY_RUN:
            logger.info("【DRY RUN模式】PDF附件迁移预览完成!")
        else:
            logger.info("PDF附件迁移任务完成!")
        logger.info(f"结束时间: {end_time}")
        logger.info(f"总耗时: {duration}")
        if DRY_RUN:
            logger.info("预览结果统计:")
            logger.info(f"  PDF附件: 预计迁移 {attachment_migrated}, 失败 {attachment_failed}")
            logger.info("注意：以上为预览结果，未实际修改数据库")
        else:
            logger.info("迁移结果统计:")
            logger.info(f"  PDF附件: 成功 {attachment_migrated}, 失败 {attachment_failed}")
        logger.info("=" * 60)

        # 如果有失败的记录，提醒检查日志
        if attachment_failed > 0:
            logger.warning("存在迁移失败的记录，请检查日志文件 pdf_migration.log 获取详细信息")

        # DRY RUN模式的额外提示
        if DRY_RUN:
            logger.info("如需实际执行迁移，请将脚本中的 DRY_RUN 设置为 False")

    except Exception as e:
        logger.error(f"PDF附件迁移任务执行失败: {e}")
        logger.error(traceback.format_exc())
        sys.exit(1)


def verify_migration():
    """验证PDF附件迁移结果"""
    logger.info("开始验证PDF附件迁移结果...")

    mysql_conn = None
    pgsql_conn = None

    try:
        mysql_conn = get_mysql_connection()
        pgsql_conn = get_pgsql_connection()

        mysql_cursor = mysql_conn.cursor()
        pgsql_cursor = pgsql_conn.cursor()

        # 验证PDF附件数量
        mysql_cursor.execute(
            "SELECT count(1) FROM `tb_dds_article_attachment` WHERE `pmid` >= %s AND `pmid` <= %s AND type = 'PDF'",
            (PMID_MIN, PMID_MAX)
        )
        v1_attachment_count = mysql_cursor.fetchone()[0]

        pgsql_cursor.execute(
            """SELECT count(1) FROM tb_dds_file f
               JOIN tb_dds_article a ON f.doc_id = a.id
               WHERE a.custom_id >= %s AND a.custom_id <= %s AND f.type = 'PDF'""",
            (PMID_MIN, PMID_MAX)
        )
        v2_attachment_count = pgsql_cursor.fetchone()[0]

        logger.info("PDF附件迁移结果验证:")
        logger.info(f"  v1 PDF附件数量: {v1_attachment_count}")
        logger.info(f"  v2 PDF附件数量: {v2_attachment_count}")

        if v1_attachment_count == v2_attachment_count:
            logger.info("✓ PDF附件迁移验证通过，数据量一致")
        else:
            logger.warning("✗ PDF附件迁移验证失败，数据量不一致，请检查迁移过程")

        # 验证文件大小统计
        pgsql_cursor.execute(
            """SELECT count(1) as total_files,
                      count(file_size) as files_with_size,
                      sum(file_size) as total_size
               FROM tb_dds_file f
               JOIN tb_dds_article a ON f.doc_id = a.id
               WHERE a.custom_id >= %s AND a.custom_id <= %s AND f.type = 'PDF'""",
            (PMID_MIN, PMID_MAX)
        )
        size_stats = pgsql_cursor.fetchone()
        total_files, files_with_size, total_size = size_stats

        logger.info("文件大小统计:")
        logger.info(f"  总文件数: {total_files}")
        logger.info(f"  有大小信息的文件数: {files_with_size}")
        logger.info(f"  总大小: {total_size if total_size else 0} 字节")

    except Exception as e:
        logger.error(f"验证PDF附件迁移结果失败: {e}")
    finally:
        if mysql_conn:
            mysql_conn.close()
        if pgsql_conn:
            pgsql_conn.close()


if __name__ == "__main__":
    # 检查必要的依赖包
    try:
        import pymysql
        import psycopg2
    except ImportError as e:
        logger.error(f"缺少必要的依赖包: {e}")
        logger.error("请安装依赖包: pip install pymysql psycopg2-binary")
        sys.exit(1)

    # 执行迁移
    main()

    # 验证迁移结果（仅在非DRY_RUN模式下执行）
    if not DRY_RUN:
        verify_migration()
    else:
        logger.info("【DRY RUN模式】跳过迁移结果验证")
