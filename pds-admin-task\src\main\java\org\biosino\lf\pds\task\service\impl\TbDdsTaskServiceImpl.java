package org.biosino.lf.pds.task.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.lang.PatternPool;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Validator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.biosino.lf.pds.article.custbean.dto.ArticleViewQueryDTO;
import org.biosino.lf.pds.article.custbean.dto.FileUploadDTO;
import org.biosino.lf.pds.article.custbean.dto.TaskPublishDTO;
import org.biosino.lf.pds.article.custbean.dto.TaskTaskDTO;
import org.biosino.lf.pds.article.custbean.vo.ArticleViewVO;
import org.biosino.lf.pds.article.custbean.vo.SelectVO;
import org.biosino.lf.pds.article.custbean.vo.StatInfoVO;
import org.biosino.lf.pds.article.custbean.vo.TbDdsTaskVO;
import org.biosino.lf.pds.article.domain.*;
import org.biosino.lf.pds.article.mapper.*;
import org.biosino.lf.pds.article.service.ITbDdsFileService;
import org.biosino.lf.pds.article.service.IArticleService;
import org.biosino.lf.pds.common.config.AppConfig;
import org.biosino.lf.pds.common.constant.CacheConstants;
import org.biosino.lf.pds.common.constant.DictTypeConstants;
import org.biosino.lf.pds.common.core.controller.BaseController;
import org.biosino.lf.pds.common.core.domain.entity.SysDictData;
import org.biosino.lf.pds.common.core.domain.entity.SysRole;
import org.biosino.lf.pds.common.core.domain.entity.SysUser;
import org.biosino.lf.pds.common.core.domain.model.LoginUser;
import org.biosino.lf.pds.common.core.mail.MailService;
import org.biosino.lf.pds.common.core.mail.MailTemplateEnum;
import org.biosino.lf.pds.common.core.page.PageDomain;
import org.biosino.lf.pds.common.core.redis.RedisCache;
import org.biosino.lf.pds.common.enums.AppNameEnum;
import org.biosino.lf.pds.common.enums.DirectoryEnum;
import org.biosino.lf.pds.common.enums.RoleEnum;
import org.biosino.lf.pds.common.enums.StatusEnums;
import org.biosino.lf.pds.common.enums.task.*;
import org.biosino.lf.pds.common.exception.ServiceException;
import org.biosino.lf.pds.common.utils.CompressUtil;
import org.biosino.lf.pds.common.utils.DateUtils;
import org.biosino.lf.pds.common.utils.MyHashUtil;
import org.biosino.lf.pds.common.utils.bean.BeanValidators;
import org.biosino.lf.pds.common.utils.file.FileUtils;
import org.biosino.lf.pds.common.utils.task.JobIdCreator;
import org.biosino.lf.pds.system.mapper.SysUserMapper;
import org.biosino.lf.pds.system.service.ISysDictTypeService;
import org.biosino.lf.pds.task.config.PdsAppConfig;
import org.biosino.lf.pds.task.service.ITbDdsSiteService;
import org.biosino.lf.pds.task.service.ITbDdsTaskService;
import org.springframework.http.MediaType;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;

import java.io.*;
import java.util.*;
import java.util.Arrays;
import java.util.stream.Collectors;
import jakarta.servlet.ServletOutputStream;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static org.biosino.lf.pds.article.service.impl.TbDdsFileServiceImpl.initDiskFile;
import static org.biosino.lf.pds.task.service.impl.TaskApiServiceImpl.getRetryTimesBySiteType;
import static org.biosino.lf.pds.task.service.impl.TaskApiServiceImpl.setRetryTimesBySiteType;


/**
 * 文献传递任务服务实现
 * 处理任务创建、分配、状态更新等核心业务逻辑
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TbDdsTaskServiceImpl extends ServiceImpl<TbDdsTaskMapper, TbDdsTask> implements ITbDdsTaskService {

    private final static Pattern PMC_PATTERN = Pattern.compile("^PMC([0-9]+)$");

    private final ITbDdsFileService tbDdsFileService;
    private final ITbDdsSiteService tbDdsSiteService;
    private final MailService mailService;
    private final ISysDictTypeService sysDictTypeService;
    private final IArticleService articleService;

    private final TbDdsTaskMapper tbDdsTaskMapper;
    private final TbDdsTaskLogMapper tbDdsTaskLogMapper;
    private final TbDdsTaskPaperMapper tbDdsTaskPaperMapper;
    private final TbDdsTaskScheduleMapper tbDdsTaskScheduleMapper;
    private final TbDdsTaskLinkMapper tbDdsTaskLinkMapper;
    private final ArticleMapper articleMapper;
    private final JournalMapper journalMapper;

    private final TbDdsSiteMapper tbDdsSiteMapper;

    private final SysUserMapper sysUserMapper;
    private final RedisCache redisCache;

    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;

    private final Validator validator;
    private static final Map<String, Object> THREAD_RUN_MAP = new ConcurrentHashMap<>();
    private static final String ID_TYPE_CONN = "_";

    /*@Override
    public AjaxResult uploadIdExcel(MultipartFile file) {
        InputStream inputStream = null;
        ExcelReader reader = null;
        try {
            if (file == null) {
                return AjaxResult.error("请选择要上传的文件");
            }

            inputStream = file.getInputStream();
            // 使用hutool读取Excel文件
            reader = ExcelUtil.getReader(inputStream);
            final List<List<Object>> readAll = reader.read();

            // 处理数据，跳过表头行
            List<List<String>> resultData = new ArrayList<>();
            boolean skipHeader = false;
            final int size = CollUtil.size(readAll);
            if (size > 5000) {
                return AjaxResult.error("上传的Excel文件不能超过5000行");
            }

            for (List<Object> row : readAll) {
                // 检查是否是表头行
                if (!skipHeader && !row.isEmpty() &&
                        (StrUtil.containsIgnoreCase(String.valueOf(row.get(0)), "PMID") ||
                                StrUtil.containsIgnoreCase(String.valueOf(row.get(1)), "PMCID") ||
                                StrUtil.containsIgnoreCase(String.valueOf(row.get(2)), "DOI"))) {
                    skipHeader = true;
                    continue;
                }

                // 处理数据行
                List<String> dataRow = new ArrayList<>();
                for (Object cell : row) {
                    dataRow.add(cell == null ? StrUtil.EMPTY : StrUtil.trimToEmpty(String.valueOf(cell)));
                }
                resultData.add(dataRow);
            }

            Map<String, Object> result = new HashMap<>();
            result.put("data", resultData);

            return AjaxResult.success("Excel文件解析成功", result);
        } catch (Exception e) {
            return AjaxResult.error("Excel文件解析失败: " + e.getMessage());
        } finally {
            IoUtil.close(reader);
            IoUtil.close(inputStream);
        }
        return null;
    }*/

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TbDdsTask publishTask(TaskPublishDTO dto) {
        final Long userId = dto.getTaskUserId();
        if (userId == null) {
            throw new ServiceException("提交任务的用户ID不能为空");
        }

        final String taskSourceFlag = dto.getTaskSourceFlag();
        if (StrUtil.isBlank(taskSourceFlag)) {
            throw new ServiceException("任务来源不能为空");
        } else {
            if (!TaskSourceEnum.allNames().contains(taskSourceFlag)) {
                throw new ServiceException("任务来源错误");
            }
        }

        // 校验节点类型
        final List<String> scriptTypeDictValues = sysDictTypeService.selectDictDataByType(DictTypeConstants.SCRIPT_TYPE)
                .stream().map(SysDictData::getDictValue).toList();
        final List<String> nodeTypes = dto.getNodeTypes();
        if (!CollUtil.containsAll(scriptTypeDictValues, nodeTypes)) {
            throw new ServiceException("节点类型错误");
        }

        final Integer testFlag = dto.getTestFlag();
        final short testFlagVal = testFlag == null ? 0 : testFlag.shortValue();
        Integer siteId = null;
        if (testFlagVal == 1) {
            // 测试任务
            final String siteIdStr = dto.getSiteId();
            final String[] split = siteIdStr.split(ID_TYPE_CONN);
            final String siteType = split[1];
            if (!scriptTypeDictValues.contains(siteType)) {
                throw new ServiceException("测试任务选中的站点类型错误");
            } else {
                siteId = Integer.parseInt(split[0]);
                final TbDdsSite tbDdsSite = tbDdsSiteMapper.selectById(siteId);
                if (tbDdsSite == null) {
                    throw new ServiceException("测试任务选中的站点不存在");
                }
                if (!tbDdsSite.getStatus().equals(StatusEnums.ENABLE.getCode().toString())) {
                    throw new ServiceException("测试任务选中的站点已停用");
                }

                // 清空用户选中“支持的节点类型”，添加当前节点类型
                nodeTypes.clear();
                nodeTypes.add(siteType);
            }
        }


        // 获取顺序pmid > pmcid > doi
        final List<List<String>> literatureData = dto.getLiteratureData();
        final Set<String> idSet = new LinkedHashSet<>();
        for (List<String> item : literatureData) {
            String id = null;

            for (int i = 0; i < item.size(); i++) {
                final String s = StrUtil.trimToNull(item.get(i));
                int length = StrUtil.length(s);
                if (length > 0) {
                    if (length > 200) {
                        throw new ServiceException("太长的行，确定是 doi/pmid？ " + s);
                    }
                    if (i == 1) {
                        // pmcid
                        id = s.startsWith("PMC") ? s : "PMC" + s;
                    } else {
                        id = s;
                    }
                    break;
                }
            }

            for (String s : item) {
                s = StrUtil.trimToNull(s);
                int length = StrUtil.length(s);
                if (length > 0) {
                    if (length > 200) {
                        throw new ServiceException("太长的行，确定是 doi/pmid？ " + s);
                    }
                    id = s;
                    break;
                }
            }

            if (id != null) {
                idSet.add(id);
                if (idSet.size() > 5000) {
                    throw new ServiceException("文献标识符数据不能超过5000条");
                }
            }
        }
        if (CollUtil.isEmpty(idSet)) {
            throw new ServiceException("文献标识符数据不能为空");
        }

        // 校验下载模式
        final List<SysDictData> sysDictData = sysDictTypeService.selectDictDataByType(DictTypeConstants.TASK_DOWNLOAD_MODE);
        boolean isValid = false;
        for (SysDictData dictData : sysDictData) {
            if (dto.getDownloadMode().equals(dictData.getDictValue())) {
                isValid = true;
                break;
            }
        }
        if (!isValid) {
            throw new ServiceException("下载模式值错误");
        }

        // 校验通过
        final Date now = new Date();
        final String taskId = JobIdCreator.generateCode();
        final Long fileId = initTaskInputFile(taskId, now, idSet);

        // 创建任务
        TbDdsTask task = new TbDdsTask();
        task.setId(taskId);
        // 页面发布时任务名称为空
        task.setName(dto.getTaskName());
        task.setDescription(dto.getTaskDesc());
        task.setFileId(fileId);

        task.setPriority(dto.getPriority().shortValue());
        // 设置支持的节点类型
        task.setSupportSiteType(nodeTypes);

        task.setTestFlag(testFlagVal);

        task.setDownloadMode(dto.getDownloadMode());
        task.setRetryInterval(dto.getRetryInterval());
        task.setStatus(TaskStatusEnum.create.name()); // 初始状态：create-待分配
        task.setCreator(userId);
        task.setCreateTime(now);
        task.setSource(taskSourceFlag);

        // 设置指定节点ID（用于测试任务）
        task.setSiteId(siteId);

        // 保存任务
        tbDdsTaskMapper.insert(task);
        return task;
    }

    /**
     * 分配任务
     * 将任务拆分成子任务并分配给相应的处理节点
     */
    @Override
    public void schedule(String taskId) {
        final TbDdsTask task = this.getById(taskId);
        if (task == null) {
            log.error("未找到任务：{}", taskId);
            return;
        }
        try {
            // 分配中
            appendTaskMessage(tbDdsTaskLogMapper, task.getId(), String.format("开始分配任务 %s ......", task.getId()));
            task.setStatus(TaskStatusEnum.assigning.name());
            this.updateById(task);

            //任务分配
            fillTaskSchedule(task);

            // 分配完了查看是否有需要执行的任务，如果没有则任务本任务已完成
            /*final Set<String> status = TaskPaperStatusEnum.getNotCompleteTaskpaperStatus().stream().map(Enum::name).collect(Collectors.toSet());
            long count = tbDdsTaskPaperMapper.selectCount(Wrappers.lambdaQuery(TbDdsTaskPaper.class)
                    .eq(TbDdsTaskPaper::getTaskId, taskId).in(TbDdsTaskPaper::getStatus, status));*/
            if (!hasPaperByTaskIdAndStatus(taskId, TaskPaperStatusEnum.getNotCompleteTaskpaperStatus())) {
                task.setStatus(TaskStatusEnum.complete.name());
            } else {
                task.setStatus(TaskStatusEnum.assigned.name());
            }

            this.updateById(task);
        } catch (Exception e) {
            log.error(e.getMessage(), e);

            task.setStatus(TaskStatusEnum.assign_error.name());

            appendTaskMessage(tbDdsTaskLogMapper, taskId, String.format("任务分配失败。<br>%s", e.toString()));
            throw e;
        }
    }

    @Override
    public boolean hasPaperByTaskIdAndStatus(String taskId, List<TaskPaperStatusEnum> statusList) {
        if (StrUtil.isBlank(taskId) || CollUtil.isEmpty(statusList)) {
            return false;
        }

        final Set<String> status = statusList.stream().map(Enum::name).collect(Collectors.toSet());
        final TbDdsTaskPaper one = tbDdsTaskPaperMapper.findOne(Wrappers.lambdaQuery(TbDdsTaskPaper.class)
                .eq(TbDdsTaskPaper::getTaskId, taskId)
                .in(TbDdsTaskPaper::getStatus, status)
                .select(TbDdsTaskPaper::getId)
        );
        return one != null;
    }


    @Override
    public TbDdsTaskPaper findTaskPaper(String taskId, Long docId) {
        if (taskId == null || docId == null) {
            return null;
        }
        return tbDdsTaskPaperMapper.findOne(Wrappers.lambdaQuery(TbDdsTaskPaper.class)
                .eq(TbDdsTaskPaper::getTaskId, taskId)
                .eq(TbDdsTaskPaper::getDocId, docId));
    }

    @Override
    public TbDdsTaskSchedule findTaskSchedule(Long paperId, Integer siteId) {
        if (paperId == null || siteId == null) {
            return null;
        }
        return tbDdsTaskScheduleMapper.findOne(Wrappers.lambdaQuery(TbDdsTaskSchedule.class)
                .eq(TbDdsTaskSchedule::getPaperId, paperId).eq(TbDdsTaskSchedule::getSiteId, siteId));
    }

    /**
     * 任务跟踪列表查询
     */
    @Override
    public TbDdsTaskVO searchTaskTrackList(final TaskTaskDTO queryDto, final PageDomain pageDomain, final LoginUser loginUser) {
        final SysUser user = sysUserMapper.selectUserByUserName(loginUser.getUsername());
        final List<SysRole> roles = user.getRoles();

        final List<TbDdsTaskVO.TaskItem> data = new ArrayList<>();
        Long total = 0L;
        if (CollUtil.isNotEmpty(roles)) {
            final LambdaQueryWrapper<TbDdsTask> queryWrapper = Wrappers.lambdaQuery(TbDdsTask.class);
            final String id = queryDto.getId();
            if (StrUtil.isNotBlank(id)) {
                queryWrapper.apply("id ILIKE {0}", "%" + id + "%");
            }

            final String description = queryDto.getDescription();
            if (StrUtil.isNotBlank(description)) {
                queryWrapper.apply("description ILIKE {0}", "%" + description + "%");
            }

            final String beginTime = queryDto.getBeginTime();
            if (StrUtil.isNotBlank(beginTime)) {
                queryWrapper.ge(TbDdsTask::getCreateTime, DateUtil.beginOfDay(DateUtils.parseDate(beginTime)));
            }

            final String endTime = queryDto.getEndTime();
            if (StrUtil.isNotBlank(endTime)) {
                queryWrapper.le(TbDdsTask::getCreateTime, DateUtil.endOfDay(DateUtils.parseDate(endTime)));
            }

            final boolean isAdmin = roles.stream().anyMatch(role -> {
                final String roleKey = role.getRoleKey();
                return "admin".equals(roleKey) || "pds".equals(roleKey);
            });

            if (isAdmin) {
                // 管理员可查看所有人的任务
                final Long creatorId = queryDto.getCreator();
                if (creatorId != null) {
                    queryWrapper.eq(TbDdsTask::getCreator, creatorId);
                }
            } else {
                // 非管理员只能查看自己创建的任务
                queryWrapper.eq(TbDdsTask::getCreator, loginUser.getUserId());
            }

            total = tbDdsTaskMapper.selectCount(queryWrapper);
            if (total > 0) {
                queryWrapper.orderByDesc(TbDdsTask::getCreateTime);
                final List<TbDdsTask> tbDdsTasks = tbDdsTaskMapper.selectList(PageDTO.of(pageDomain.getPageNum(), pageDomain.getPageSize()), queryWrapper);
                if (CollUtil.isNotEmpty(tbDdsTasks)) {
                    final Set<Long> creatorIds = tbDdsTasks.stream().map(TbDdsTask::getCreator).collect(Collectors.toSet());
                    final Map<Long, String> userNameMap = initUserNameMap(creatorIds);

                    final Map<String, String> taskStatusMap = TaskStatusEnum.toMap();
                    for (TbDdsTask tbDdsTask : tbDdsTasks) {
                        final String taskId = tbDdsTask.getId();
                        Map<TaskPaperStatusEnum, Integer> map = statisticsTaskPaperNumByStatus(taskId);
                        final TbDdsTaskVO.TaskItem item = new TbDdsTaskVO.TaskItem();
                        item.setTaskId(tbDdsTask.getId());
                        item.setTaskName(tbDdsTask.getDescription());
                        item.setTotalCount(tbDdsTask.getTotal());
                        item.setStatus(tbDdsTask.getStatus());
                        item.setStatusStr(taskStatusMap.get(tbDdsTask.getStatus()));

                        item.setStockCount(map.get(TaskPaperStatusEnum.success_exist));
                        item.setDownloadedCount(map.get(TaskPaperStatusEnum.success_auto));
                        item.setManUploadCount(map.get(TaskPaperStatusEnum.success_man));
                        item.setFailedCount(map.get(TaskPaperStatusEnum.failed));
                        int waitingNum = map.get(TaskPaperStatusEnum.executing) + map.get(TaskPaperStatusEnum.waiting);
                        item.setPendingCount(waitingNum);

                        //最后更新时间
                        TbDdsTaskPaper paper = tbDdsTaskPaperMapper.findOne(Wrappers.lambdaQuery(TbDdsTaskPaper.class)
                                .eq(TbDdsTaskPaper::getTaskId, taskId)
                                .orderByDesc(TbDdsTaskPaper::getUpdateTime)
                                .select(TbDdsTaskPaper::getUpdateTime));
                        if (paper != null) {
                            Date lastUpdateTime = paper.getUpdateTime();
                            item.setUpdateTime(lastUpdateTime);
                        }

                        item.setCreateTime(tbDdsTask.getCreateTime());
                        item.setPriority(tbDdsTask.getPriority());

                        final Long creator = tbDdsTask.getCreator();
                        item.setCreator(userNameMap.get(creator));
                        item.setCreatorId(creator);
                        item.setTestFlag(tbDdsTask.getTestFlag() == 1);
                        item.setNodeTypes(tbDdsTask.getSupportSiteType());
                        data.add(item);
                    }
                }
            }

        }

        final TbDdsTaskVO vo = new TbDdsTaskVO();
        vo.setTableDataInfo(BaseController.initTableDataInfo(data, total));
        vo.setCurrUserEmail(StrUtil.trimToNull(user.getEmail()));

        final Long downloadingTotal = tbDdsTaskPaperMapper.selectCount(Wrappers.lambdaQuery(TbDdsTaskPaper.class)
                .eq(TbDdsTaskPaper::getStatus, TaskPaperStatusEnum.executing.name()));
        vo.setDownloadingTotal(numStr(downloadingTotal));

        final Long waitingTotal = tbDdsTaskPaperMapper.selectCount(initNotFinisQuery());
        vo.setWaitingTotal(numStr(waitingTotal));

        return vo;
    }

    private Map<Long, String> initUserNameMap(final Set<Long> creatorIds) {
        List<SysUser> sysUsers = sysUserMapper.selectList(Wrappers.lambdaQuery(SysUser.class).in(SysUser::getUserId, creatorIds));
        Map<Long, String> map = new HashMap<>();
        if (CollUtil.isNotEmpty(sysUsers)) {
            for (SysUser sysUser : sysUsers) {
                map.put(sysUser.getUserId(), sysUser.getNickName());
            }
        }
        return map;
    }

    public Map<TaskPaperStatusEnum, Integer> statisticsTaskPaperNumByStatus(String taskId) {
        final Map<TaskPaperStatusEnum, Integer> map = new HashMap<>();
        for (TaskPaperStatusEnum e : TaskPaperStatusEnum.values()) {
            map.put(e, 0);
        }
        if (StringUtils.isBlank(taskId)) {
            return map;
        }
        final List<StatInfoVO> list = tbDdsTaskPaperMapper.statisticsTaskPaperNumByStatus(taskId);
        if (list != null && !list.isEmpty()) {
            for (StatInfoVO infoVO : list) {
                map.put(TaskPaperStatusEnum.valueOf(infoVO.getStatus()), infoVO.getNum());
            }
        }
        return map;
    }

    @Override
    public List<SelectVO> allPdsUsers() {
        List<SysUser> users = sysUserMapper.selectAllUsersByAppName(AppNameEnum.PDS.name().toLowerCase());
        if (CollUtil.isNotEmpty(users)) {
            return users.stream().map(user -> new SelectVO(user.getUserId().toString(), user.getNickName())).collect(Collectors.toList());
        }
        return List.of();
    }

    @Override
    public List<SelectVO> allPdsSites() {
        final LambdaQueryWrapper<TbDdsSite> lambdaQuery = Wrappers.lambdaQuery(TbDdsSite.class)
                .eq(TbDdsSite::getStatus, StatusEnums.ENABLE.getCode().toString()).orderByAsc(TbDdsSite::getId);
        final List<TbDdsSite> tbDdsSites = tbDdsSiteMapper.selectList(lambdaQuery);
        if (CollUtil.isNotEmpty(tbDdsSites)) {
            final Map<String, String> map = ScriptTypeEnum.toMap();
            return tbDdsSites.stream().map(site -> {
                return new SelectVO(StrUtil.format("{}{}{}", site.getId(), ID_TYPE_CONN, site.getSiteType()), StrUtil.format("{}-{} ({})", site.getId(), site.getSiteName(), map.get(site.getSiteType())));
            }).collect(Collectors.toList());
        }
        return List.of();
    }

    @Override
    public List<ArticleViewVO> taskArticleViewPage(ArticleViewQueryDTO queryDto) {
        return findTaskArticleViewPage(queryDto, true);
    }

    private List<ArticleViewVO> findTaskArticleViewPage(ArticleViewQueryDTO queryDto, final boolean searchSchedule) {
        final String taskId = queryDto.getTaskId();
        if (StrUtil.isBlank(taskId)) {
            throw new ServiceException("参数错误，请指定 taskId");
        }

        final List<TbDdsTaskPaper> list = tbDdsTaskPaperMapper.searchTaskPaper(queryDto);
        final List<ArticleViewVO> data = new ArrayList<>();
        if (CollUtil.isNotEmpty(list)) {
            final Map<String, String> map = TaskPaperStatusEnum.toMap();
            final Map<String, String> scheduleMap = TaskPaperScheduleStatusEnum.toMap();
            final Map<String, String> scriptTypeMap = ScriptTypeEnum.toMap();
            final Set<String> successTaskPaperStatus = TaskPaperStatusEnum.getSuccessTaskPaperStatus().stream().map(TaskPaperStatusEnum::name).collect(Collectors.toSet());

            for (TbDdsTaskPaper tbDdsTaskPaper : list) {
                ArticleViewVO vo = new ArticleViewVO();
                vo.setPaperId(numStr(tbDdsTaskPaper.getId()));
                vo.setDocId(numStr(tbDdsTaskPaper.getDocId()));
                vo.setPmcId(numStr(tbDdsTaskPaper.getPmcId()));
                vo.setPmid(numStr(tbDdsTaskPaper.getPmid()));
                vo.setTitle(tbDdsTaskPaper.getTitle());
                final String status = tbDdsTaskPaper.getStatus();
                vo.setTaskType(map.get(status));
                if (successTaskPaperStatus.contains(status)) {
                    vo.setDownloadStatus("完成");
                } else if (TaskPaperStatusEnum.failed.name().equals(status)) {
                    vo.setDownloadStatus("失败");
                } else {
                    vo.setDownloadStatus("正在进行");
                }

                if (searchSchedule) {
                    final Long paperId = tbDdsTaskPaper.getId();
                    final List<TbDdsTaskSchedule> scheduleList = tbDdsTaskScheduleMapper.getTaskScheduleByPaperId(paperId);
                    final List<String> executeLog = new ArrayList<>();
                    for (TbDdsTaskSchedule schedule : scheduleList) {
                        executeLog.add(StrUtil.format("[{}]  {}  {}={}({})",
                                DateFormatUtils.format(schedule.getTimeExecute(), DatePattern.NORM_DATETIME_PATTERN),
                                scheduleMap.get(schedule.getStatus()), schedule.getSiteId(),
                                schedule.getSiteName(), scriptTypeMap.get(schedule.getSiteType())));
                    }
                    vo.setExecuteLog(executeLog);
                }

                data.add(vo);
            }
        }
        return data;
    }

    @Override
    public void taskArticleDownloadFile(ArticleViewQueryDTO queryDto, HttpServletRequest request, HttpServletResponse response) {
        final String downloadStatus = queryDto.getDownloadStatus();
        List<String> status;
        if ("success".equals(downloadStatus)) {
            status = TaskPaperStatusEnum.getSuccessTaskPaperStatus().stream().map(TaskPaperStatusEnum::name).toList();
        } else if ("failed".equals(downloadStatus)) {
            status = CollUtil.toList(TaskPaperStatusEnum.failed.name());
        } else {
            throw new ServiceException("未知的下载类型：" + downloadStatus);
        }
        queryDto.setStatusList(status);

        PrintWriter writer = null;
        try {
            response.setContentType(MediaType.TEXT_PLAIN_VALUE);
            response.setCharacterEncoding("UTF-8");
            FileUtils.setAttachmentResponseHeader(response, StrUtil.format("{}_{}.tsv", queryDto.getTaskId(), downloadStatus));

            final String rowFormat = "{}\t{}\t{}\t{}\t{}\n";
            writer = response.getWriter();
            writer.write(StrUtil.format(rowFormat, "docId", "pmid", "pmcId", "doi", "title"));

            List<ArticleViewVO> articleViews;
            int pageNo = 1, pageSize = 100;
            do {
                int custOffset = (pageNo - 1) * pageSize;
                pageNo++;
                queryDto.setCustOffset(custOffset);
                queryDto.setCustLimit(pageSize);
                articleViews = findTaskArticleViewPage(queryDto, false);

                if (articleViews == null || articleViews.size() == 0) {
                    break;
                }
                for (ArticleViewVO view : articleViews) {
                    writer.write(StrUtil.format(rowFormat, view.getDocId(), view.getPmid(), view.getPmcId(), view.getDoi(), view.getTitle()));
                }
                writer.flush();
            } while (CollUtil.isNotEmpty(articleViews));
        } catch (Exception e) {
            throw new ServiceException("导出出错:" + e.getMessage());
        } finally {
            IoUtil.close(writer);
        }
    }

    private String numStr(Number n) {
        return n == null ? null : n.toString();
    }

    /**
     * 处理任务分配的具体实现
     * 从数据库读取任务输入文件内容，逐行处理文献标识符，创建子任务
     *
     * @param task 要分配的任务对象
     */
    private void fillTaskSchedule(TbDdsTask task) {
        final Long fileId = task.getFileId();
        if (fileId == null) {
            log.error("任务输入文件ID为空: {}", task.getId());
            return;
        }

        // 从数据库获取文件内容
        final TbDdsFileContent fileContent = tbDdsFileService.findContentById(fileId);
        if (fileContent == null || fileContent.getFileData() == null) {
            log.error("任务输入文件内容不存在: fileId={}", fileId);
            return;
        }

        // 创建错误文件路径
        final String errorFilePath = "task/" + DateFormatUtils.format(task.getCreateTime(), "yyyyMMdd") + "/" + task.getId() + ".err";
        final File errorFile = new File(AppConfig.getTaskDir(), errorFilePath);
        if (errorFile.exists()) {
            errorFile.delete();
        }

        // 读取文件内容, 已分配数量、库中已存在不再分配数量、ID/DOI在库中未找到数量
        int assignNum = 0, existsNum = 0;

        List<String> notFoundLines = new ArrayList<>();

        // 将字节数组转换为字符串，然后按行分割
        final String fileContentStr = new String(fileContent.getFileData(), java.nio.charset.StandardCharsets.UTF_8);
        final List<String> lines = Arrays.asList(fileContentStr.split("\\r?\\n"));
        for (String line : lines) {
            line = StrUtil.trimToNull(line);
            if (line == null) {
                continue;
            }

            final Article article = getArticleInfo(line);

            if (article == null) {
                notFoundLines.add(line);
                log.info("文献 {} 的题录信息未找到，忽略。", line);
                continue;
            }

            // 相同的doc_id可分配多篇，因为优先级可能不同
            TbDdsTaskPaper taskPaper = findTaskPaper(task.getId(), article.getId());

            // 如果任务已经存在，直接跳过
            if (taskPaper != null) {
                log.warn("文献 {} 已经在该任务 {} 下，不再重复分配，忽略。", article.getPmid(), task.getId());
                continue;
            }

            taskPaper = createTaskPaper(task, article);

            // 文献已经存在，不再进行分配
            // 测试任务不做判断
            if (!TaskPaperStatusEnum.waiting.name().equalsIgnoreCase(taskPaper.getStatus())) {
                existsNum++;
                log.info("文献 {} 全文已存在，不再进行分配。", line);
                continue;
            }

            assignNum++;

            log.info("已分配 {} 篇文献, {}", assignNum, line);
        }

        task.setTotal(notFoundLines.size() + existsNum + assignNum);

        this.updateById(task);

        FileUtil.writeUtf8Lines(notFoundLines, errorFile);

        String errorDownload = "";
        if (errorFile.exists() && errorFile.length() > 0) {
            errorDownload = "<a href='" + AppConfig.getTaskDirPreUrl() + "/" + errorFilePath + "' target=\"_blank\" >[下载]</a>";
        }

        appendTaskMessage(
                tbDdsTaskLogMapper,
                task.getId(),
                String.format("任务分配完成，已分配 %d 篇，全文在库中已存在 %d 篇，库中没有找到 %d 篇&nbsp;&nbsp;%s", assignNum, existsNum, notFoundLines.size(), errorDownload)
        );
    }


    /**
     * 创建任务文献对象
     * 检查文献全文是否已存在，设置适当的状态
     *
     * @param task    任务对象
     * @param article 文献对象
     * @return 创建的任务文献对象
     */
    private TbDdsTaskPaper createTaskPaper(final TbDdsTask task, final Article article) {
        // 判断 PDF 是否已经存在了
        final List<TbDdsFile> pdfs = tbDdsFileService.findByDocIdAndType(article.getId(), FileTypeEnum.PDF.name());

        boolean fulltextExist = false;
        if (CollUtil.isNotEmpty(pdfs)) {
            for (TbDdsFile pdf : pdfs) {
                final File destFile = initDiskFile(pdf.getFilePath());
                if (FileUtil.exist(destFile) && destFile.isFile()) {
                    fulltextExist = true;
                    break;
                }
            }
        }

//        final String doi = articleOtherIdMapper.getArticleDoiByDocId(article.getId());
        final String doi = article.getDoi();

        Date curDate = new Date();

        final TbDdsTaskPaper paper = new TbDdsTaskPaper();
        paper.setTaskId(task.getId());
        paper.setDocId(article.getId());
        paper.setDoi(doi);
        paper.setStatus(fulltextExist && task.getTestFlag() == 0 ? TaskPaperStatusEnum.success_exist.name() : TaskPaperStatusEnum.waiting.name());
        paper.setCreateTime(curDate);
        paper.setUpdateTime(curDate);
        paper.setCreator(task.getCreator());
//        paper.setSiteId(task.getSiteId());
        final Long journalId = article.getJournalId();
        if (journalId != null) {
            final Journal journal = journalMapper.selectById(journalId);
            if (journal != null) {
                paper.setJournalId(journalId);
                paper.setPublisherId(journal.getPublisherId());
            } else {
                log.error("期刊ID错误： {}", journalId);
            }
        }

        tbDdsTaskPaperMapper.insert(paper);
        return paper;
    }

    /**
     * 根据提供的标识符获取文献信息
     * 支持PMC、PMID和DOI三种标识符格式
     *
     * @param line 文献标识符
     * @return 文献对象，未找到则返回null
     */
    @Override
    public Article getArticleInfo(final String line) {
        if (StringUtils.isBlank(line)) {
            return null;
        }

        // 先判断 PMC?
        final Matcher matcher = PMC_PATTERN.matcher(line.toUpperCase());

        Article article = null;
        if (matcher.matches()) {
            final String group = matcher.group(1);
            long pmcId = Long.parseLong(group);
            final LambdaQueryWrapper<Article> lambdaQuery = Wrappers.lambdaQuery(Article.class)
                    .eq(Article::getPmcId, pmcId)
                    .orderByAsc(Article::getPmid);
            article = articleMapper.findOne(lambdaQuery);
        }

        if (article != null) {
            return article;
        }

        // 不是 PMC？
        try {
            // PMID
            Long pmid = Long.parseLong(line);
            final LambdaQueryWrapper<Article> lambdaQuery = Wrappers.lambdaQuery(Article.class)
                    .eq(Article::getPmid, pmid);
            article = articleMapper.findOne(lambdaQuery);
        } catch (NumberFormatException e) {
            // 不是数字类型作为doi处理
            /*final Long docId = articleOtherIdMapper.getArticleIdByDoi(line);
            article = articleMapper.selectById(docId);*/

            final LambdaQueryWrapper<Article> lambdaQuery = Wrappers.lambdaQuery(Article.class)
                    .eq(Article::getDoi, line);
            article = articleMapper.findOne(lambdaQuery);
        }

        return article;
    }


    /**
     * 初始化任务输入文件
     * 创建存储任务标识符的文件，上传到数据库并删除磁盘文件
     *
     * @param taskId 任务ID
     * @param now    创建时间
     * @param idSet  文献标识符集合
     * @return 文件ID
     */
    private Long initTaskInputFile(final String taskId, final Date now, final Set<String> idSet) {
        // 创建临时文件
        final String tempDir = DirectoryEnum.temp.name();
        final File dir = new File(taskInputDir(), tempDir);
        if (!dir.exists()) {
            dir.mkdirs();
        }

        final File tempFile = new File(dir, taskId + ".in");
        if (tempFile.exists()) {
            throw new ServiceException("生成文献标识符数据失败");
        }

        try {
            // 写入临时文件
            FileUtil.writeUtf8Lines(idSet, tempFile);

            // 计算文件MD5
            final String fileMd5 = MyHashUtil.md5(tempFile);

            // 上传文件到数据库
            FileUploadDTO uploadDTO = new FileUploadDTO(
                    tempFile,
                    FileTypeEnum.TASK,
                    fileMd5,
                    taskId + ".in",
                    false
            );
            final TbDdsFile uploadedFile = tbDdsFileService.upload(uploadDTO);

            // 删除临时文件
            if (tempFile.exists()) {
                tempFile.delete();
            }

            return uploadedFile.getId();
        } catch (Exception e) {
            // 确保临时文件被删除
            if (tempFile.exists()) {
                tempFile.delete();
            }
            throw new ServiceException("上传任务输入文件失败: " + e.getMessage());
        }
    }

    /**
     * 获取任务输入文件目录
     */
    private File taskInputDir() {
        return AppConfig.initDataHome(null);
    }

    /**
     * 生成任务编号
     * 格式：PDS + 年月日
     */
    private String generateTaskName() {
        return "PDS_" + DateFormatUtils.format(new Date(), "yyyyMMdd");
    }

    /**
     * 获取任务日志
     *
     * @param taskId 任务ID
     * @return 日志列表，按创建时间降序排序
     */
    @Override
    public List<TbDdsTaskLog> getTaskLogs(String taskId) {
        if (StrUtil.isBlank(taskId)) {
            throw new ServiceException("任务ID不能为空");
        }

        // 使用MyBatis Plus查询日志，按创建时间降序排序
        return tbDdsTaskLogMapper.selectList(
                Wrappers.lambdaQuery(TbDdsTaskLog.class)
                        .eq(TbDdsTaskLog::getTaskId, taskId)
                        .orderByDesc(TbDdsTaskLog::getCreateTime)
        );
    }

    @Override
    public boolean updateTaskStatus(final String taskId, final String status) {
        if (StringUtils.isAnyBlank(taskId, status)) {
            throw new ServiceException("参数错误");
        }

        final TbDdsTask task = this.getById(taskId);
        if (task == null) {
            throw new ServiceException("未找到当前任务");
        }

        // 除了在 分配完成 和 暫停 狀態之間切換，其他的狀態不允許修改
        if (!StringUtils.equalsAnyIgnoreCase(status, TaskStatusEnum.assigned.name(), TaskStatusEnum.paused.name())) {
            throw new ServiceException("无法修改为当前状态");
        }
        if (!StringUtils.equalsAnyIgnoreCase(task.getStatus(), TaskStatusEnum.assigned.name(), TaskStatusEnum.paused.name())) {
            throw new ServiceException("无法修改为当前状态");
        }

        task.setStatus(status);
//        task.setUpdateTime(new Date());
        this.updateById(task);

        return true;
    }

    @Override
    public String downloadTaskPDF(final String taskId, final String username) {
        final String key = "dlPDF_" + taskId + "_" + username;
        final Object val = THREAD_RUN_MAP.get(key);
        if (val != null) {
            throw new ServiceException("文献传递任务结果文件正在生成中");
        }

        THREAD_RUN_MAP.put(key, true);
        final SysUser user = sysUserMapper.selectUserByUserName(username);
        if (user == null) {
            THREAD_RUN_MAP.remove(key);
            throw new ServiceException("用户不存在");
        }
        final String email = StrUtil.trimToNull(user.getEmail());
        if (email == null) {
            THREAD_RUN_MAP.remove(key);
            throw new ServiceException("用户邮箱不存在");
        }
        if (!ReUtil.isMatch(PatternPool.EMAIL, email)) {
            THREAD_RUN_MAP.remove(key);
            throw new ServiceException("用户邮箱格式错误");
        }

        threadPoolTaskExecutor.execute(() -> {
            try {
                downloadTaskPDFProcess(taskId, username, email);
            } finally {
                THREAD_RUN_MAP.remove(key);
            }
        });
        return email;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteTask(String taskId) {
        if (StrUtil.isBlank(taskId)) {
            throw new ServiceException("任务ID不能为空");
        }
        final TbDdsTask ddsTask = this.getById(taskId);
        if (ddsTask == null) {
            throw new ServiceException("该任务不存在");
        }

        tbDdsTaskLinkMapper.deleteByTaskId(taskId);
        tbDdsTaskScheduleMapper.deleteByTaskId(taskId);
        tbDdsTaskPaperMapper.delete(Wrappers.lambdaQuery(TbDdsTaskPaper.class).eq(TbDdsTaskPaper::getTaskId, taskId));
        this.removeById(taskId, false);
        return true;
    }

    private void downloadTaskPDFProcess(final String taskId, final String username, final String email) {

        final TbDdsTask task = this.getById(taskId);
        if (task == null) {
            throw new ServiceException("该任务不存在");
        }

        final String dirName = IdUtil.fastSimpleUUID();
        final File pdsResultDir = AppConfig.getPdsResultDir();
        final File dir = new File(pdsResultDir, dirName);
        if (!dir.exists()) {
            dir.mkdirs();
        }

        final File zipFile = new File(pdsResultDir, dirName + ".zip");
        try {
            final StringBuilder info = new StringBuilder();

            final Map<Long, String> journalTitleMap = new HashMap<>();

            int pageNo = 1, pageSize = 100;
            final ArticleViewQueryDTO queryDTO = new ArticleViewQueryDTO();
            queryDTO.setTaskId(taskId);
            do {
                final int custOffset = (pageNo - 1) * pageSize;
                pageNo++;
                queryDTO.setCustOffset(custOffset);
                queryDTO.setCustLimit(pageSize);
                final List<TbDdsTaskPaper> taskPapers = tbDdsTaskPaperMapper.searchTaskPaper(queryDTO);

                if (CollUtil.isEmpty(taskPapers)) {
                    break;
                }

                final Map<Long, TbDdsTaskPaper> taskPaperMap = new LinkedHashMap<>();
                taskPapers.forEach(tbDdsTaskPaper -> {
                    taskPaperMap.put(tbDdsTaskPaper.getDocId(), tbDdsTaskPaper);
                });

                final List<TbDdsFile> ddsFileList = tbDdsFileService.findByDocIdsAndType(taskPaperMap.keySet(), FileTypeEnum.PDF);
                if (CollUtil.isEmpty(ddsFileList)) {
                    continue;
                }
                final Map<Long, List<TbDdsFile>> attachmentMap = new LinkedHashMap<>();
                final Set<Long> toSearchJournalIds = new HashSet<>();
                for (TbDdsFile ddsFile : ddsFileList) {
                    Long docId = ddsFile.getDocId();
                    if (docId != null) {
                        List<TbDdsFile> list = attachmentMap.get(docId);
                        if (list == null) {
                            list = new ArrayList<>();
                        }
                        list.add(ddsFile);
                        attachmentMap.put(docId, list);

                        final TbDdsTaskPaper paper = taskPaperMap.get(docId);
                        final Long journalId = paper.getJournalId();
                        if (journalId != null && !journalTitleMap.containsKey(journalId)) {
                            toSearchJournalIds.add(journalId);
                        }
                    }
                }

                if (CollUtil.isNotEmpty(toSearchJournalIds)) {
                    final List<Journal> journals = journalMapper.selectList(Wrappers.lambdaQuery(Journal.class)
                            .in(Journal::getId, toSearchJournalIds)
                            .select(Journal::getId, Journal::getTitle));
                    if (CollUtil.isNotEmpty(journals)) {
                        journals.forEach(journal -> {
                            journalTitleMap.put(journal.getId(), StrUtil.trimToNull(journal.getTitle()));
                        });
                    }
                }

                for (Long docId : attachmentMap.keySet()) {
                    List<TbDdsFile> tbDdsArticleAttachments = attachmentMap.get(docId);
                    if (CollectionUtils.isEmpty(tbDdsArticleAttachments)) {
                        continue;
                    }

                    // 每篇文献取最新提交的导出
                    final TbDdsFile dbAttachment = tbDdsArticleAttachments.stream().sorted(Comparator.comparing(TbDdsFile::getCreateTime).reversed()).findFirst().get();
                    final File aFile = initDiskFile(dbAttachment.getFilePath());
                    if (FileUtil.exist(aFile) && aFile.isFile()) {
                        final File pdfFile = new File(dir, dbAttachment.getDocId() + ".pdf");
                        org.apache.commons.io.FileUtils.copyFile(aFile, pdfFile);

                        final TbDdsTaskPaper paper = taskPaperMap.get(docId);
                        final Long journalId = paper.getJournalId();
                        String journalTitle = null;
                        if (journalId != null) {
                            journalTitle = journalTitleMap.get(journalId);
                        }
                        // 写入 文献基本信息
                        info.append(StrUtil.format(
                                "{}\t{}\t{}\t{}\t{}\t{}\n",
                                docId,
                                paper.getPmid(),
                                paper.getTitle(),
                                joinStrVal(paper.getAuthor()),
                                journalTitle,
                                pdfFile.getName()
                        ));
                    }
                }

            } while (true);

            final File infoFile = new File(dir, "download.info");
            FileUtil.writeUtf8String(info.toString(), infoFile);

            // 打包文件
            CompressUtil.zip(dir.listFiles(), zipFile);
        } catch (IOException e) {
            throw new ServiceException("生成结果文件压缩包失败：" + e.getMessage());
        } finally {
            //发邮件，出现异常也发送邮件，此时文件无法下载
            final Map<String, Object> params = new HashMap<>();
            params.put("file_uri", AppConfig.getPdsResultrPreUrl() + "/" + zipFile.getName());
            params.put("task_name", StringUtils.isBlank(task.getName()) ? task.getId() : task.getName());
            params.put("task_desc", task.getDescription());
            params.put("create_time", task.getCreateTime());
            mailService.sendMailWithTemplate(email, MailTemplateEnum.TASK_PDF_DOWNLOAD, params);
        }
    }

    private String joinStrVal(List<String> list) {
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        return CollUtil.join(list, "|||");
    }


    /**
     * 如果等待执行的任务没有其他站点执行 或 正在执行的任务超过一个小时了 直接失败掉
     */
    @Override
    public void scheduleExecutingTaskTimeout() {
        log.info("开始清理执行超时的任务...");

        //查询超时时间，执行1个小时没有完成的视为超时
        final Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        c.add(Calendar.HOUR_OF_DAY, -1);

        final LambdaQueryWrapper<TbDdsTaskPaper> queryWrapper = initNotFinisQuery();
        final int maxRetryTimes = Math.max(PdsAppConfig.getPaperMaxRetryTimes(), 0);
        int pageNo = 1, pageSize = 100;
        do {
            final List<TbDdsTaskPaper> pagers = tbDdsTaskPaperMapper.selectList(PageDTO.of(pageNo++, pageSize), queryWrapper);
            if (CollUtil.isEmpty(pagers)) {
                break;
            }

            for (TbDdsTaskPaper paper : pagers) {
                // 获取 该paper下 所有的节点的分组
                final List<TbDdsTaskSchedule> tbDdsTaskSchedules = tbDdsTaskScheduleMapper.selectList(Wrappers.lambdaQuery(TbDdsTaskSchedule.class)
                        .eq(TbDdsTaskSchedule::getPaperId, paper.getId()));
//                Set<String> siteGroups = new HashSet<>();
                final TbDdsTask ddsTask = findByIdWithCache(paper.getTaskId());
                final boolean isCompleteTask = TaskDownloadModeEnum.complete.name().equals(ddsTask.getDownloadMode());

                boolean canReTry = false;
                if (CollUtil.isNotEmpty(tbDdsTaskSchedules)) {
                    final Map<String, Integer> siteRetryTimesMap = new HashMap<>();
                    for (TbDdsTaskSchedule item : tbDdsTaskSchedules) {
                        final TbDdsSite site = tbDdsSiteService.findByIdWithCache(item.getSiteId());
                        final String siteType = site.getSiteType();
                        int retryTimes = getRetryTimesBySiteType(siteType, paper);
                        if (isCompleteTask && retryTimes < maxRetryTimes) {
                            final Integer scriptlabelId = site.getScriptlabelId();
                            final List<Integer> reTrySiteIds = tbDdsSiteService.findReTrySiteIds(paper.getId(), scriptlabelId);
                            if (CollUtil.isNotEmpty(reTrySiteIds)) {
                                // 完整度优先下，存在可重试的站点时，才将任务重新设置为waiting
                                canReTry = true;
                                if (!siteRetryTimesMap.containsKey(siteType)) {
                                    setRetryTimesBySiteType(siteType, paper, ++retryTimes);
                                    siteRetryTimesMap.put(siteType, retryTimes);
                                }
                            }
                        }
                    }
                }

//                List<Integer> canAssignSiteIds = literatureDeliveryManager.searchCanAssignSiteIds(paper.getId(), siteGroups.toArray(new String[siteGroups.size()]));

                switch (TaskPaperStatusEnum.valueOf(paper.getStatus())) {
                    case waiting:
                        if (canReTry) {
                            break;
                        }
                        paper.setStatus(TaskPaperStatusEnum.failed.name());
                        paper.setTimeComplete(new Date());
                        tbDdsTaskPaperMapper.updateById(paper);
                        break;
                    case executing:
                        // 还未超过一个小时，任务就继续等待执行完成
                        if (c.getTime().before(paper.getTimeExecute())) {
                            break;
                        }
                        // 超过一个小时
                        if (canReTry) {
                            paper.setStatus(TaskPaperStatusEnum.waiting.name());
                            paper.setTimeComplete(null);
                        } else {
                            paper.setStatus(TaskPaperStatusEnum.failed.name());
                            paper.setTimeComplete(new Date());
                        }
                        tbDdsTaskPaperMapper.updateById(paper);
                        break;
                    default:
                        break;
                }

                updateTaskCompleteStatus(ddsTask.getId());
            }
        } while (true);

        log.info("清理超时任务结束");
    }

    private LambdaQueryWrapper<TbDdsTaskPaper> initNotFinisQuery() {
        return Wrappers.lambdaQuery(TbDdsTaskPaper.class)
                .in(TbDdsTaskPaper::getStatus, TaskPaperStatusEnum.getNotCompleteTaskpaperStatus().stream().map(TaskPaperStatusEnum::name).toList());
    }

    private TbDdsTask findByIdWithCache(String taskId) {
        if (taskId == null) {
            return null;
        }
        final String cacheKey = getTaskCacheKey(taskId);
        TbDdsTask data = redisCache.getCacheObject(cacheKey);
        if (data == null) {
            data = getById(taskId);
            redisCache.setCacheObject(cacheKey, data, 10, TimeUnit.MINUTES);
        }
        return data;
    }

    public static String getTaskCacheKey(String siteId) {
        return CacheConstants.TASK_INFO_KEY + siteId;
    }


    @Override
    public boolean updateTaskCompleteStatus(String taskId) {
        if (StrUtil.isBlank(taskId)) {
            return false;
        }
        final TbDdsTask task = getById(taskId);
        if (task == null) {
            return false;
        }
        if (TaskStatusEnum.complete.name().equalsIgnoreCase(task.getStatus())) {
            return true;
        }
        boolean hasNotComplete = hasPaperByTaskIdAndStatus(taskId, TaskPaperStatusEnum.getNotCompleteTaskpaperStatus());
        if (!hasNotComplete) {
            task.setStatus(TaskStatusEnum.complete.name());
            updateById(task);
            appendTaskMessage(tbDdsTaskLogMapper, taskId, "任务执行完成，完成时间：" + new Date());
            return true;
        }
        return false;
    }


    /**
     * 定时任务，系统空闲时，自动给批量站点分配任务
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void scheduleAutoCreateTask() {
        log.info("开始自动分配任务");

        // 判断是否还有 没执行完成的任务。
        final TbDdsTaskPaper taskPaper = tbDdsTaskPaperMapper.findOne(initNotFinisQuery().select(TbDdsTaskPaper::getId));
        if (taskPaper != null) {
            log.info("还有文献待下载，自动分配结束");
            return;
        }

        final List<Integer> sites = searchCanAutoAssignSiteIds();
        if (CollUtil.isEmpty(sites)) {
            log.info("未找到合适站点，自动分配结束");
            return;
        }

        // 3 个月前
        final int monthBefore = 3;
        // 根据分区顺序分配
        // final String[] wosQuartiles = new String[]{"Q1", "Q2", "Q3", "Q4"};

        int pageSize = 100;
        long startTime = System.currentTimeMillis();
        // 查询3个月前录入的信息， 自动分配任务排除pmc文献，pmc应该批量下载入库
        /*List<Article> articles = null;
        for (String wosQuartile : wosQuartiles) {
            articles = searchArticleByNotInTaskPaper(monthBefore, wosQuartile, pageSize);
            if (CollUtil.isNotEmpty(articles)) {
                break;
            }
        }*/

        // wos 分区都没有文献要传递了
        /*if (CollUtil.isEmpty(articles)) {
            articles = searchArticleByNotInTaskPaper(monthBefore, null, pageSize);
        }*/
        final List<Article> articles = searchArticleByNotInTaskPaper(monthBefore, null, pageSize);

        if (CollUtil.isEmpty(articles)) {
            log.error("没有找到待分配的文献，自动分配结束");
            return;
        }

        SysUser account = sysUserMapper.getFirstAccountByRole(RoleEnum.pdsAdmin.name(), StatusEnums.ENABLE.getCode().toString());
        if (account == null) {
            log.error("没有找到管理员帐号，无法生成文献加载任务");
            return;
        }

        final TaskPublishDTO dto = new TaskPublishDTO();
        dto.setTaskName("自动创建任务_" + DateFormatUtils.format(new Date(), DatePattern.PURE_DATE_PATTERN));
        dto.setTaskDesc("系统自动创建任务");

        dto.setPriority(1);
        dto.setNodeTypes(CollUtil.toList(ScriptTypeEnum.batch.getCode()));
        dto.setTestFlag(0);
        dto.setDownloadMode(TaskDownloadModeEnum.complete.name());
        dto.setRetryInterval(5);

        final List<List<String>> literatureData = new ArrayList<>();
        for (Article article : articles) {
            final List<String> item = CollUtil.toList(numStr(article.getPmid()), numStr(article.getPmcId()), article.getDoi());
            literatureData.add(item);
        }
        dto.setLiteratureData(literatureData);

        dto.setTaskSourceFlag(TaskSourceEnum.PDS.name());
        dto.setTaskUserId(account.getUserId());

        try {
            BeanValidators.validateWithException(validator, dto);
            publishTask(dto);
            long endTime = System.currentTimeMillis();
            log.info("自动分配任务 {}篇文献, 耗时：{}", articles.size(), endTime - startTime);
        } catch (Exception e) {
            log.info("自动分配任务结束:{}", e.getMessage());
            throw new ServiceException(e.getMessage());
        }
    }

    private List<Integer> searchCanAutoAssignSiteIds() {
        final String aliveHandshakeTime = TbDdsSiteServiceImpl.initAliveHandshakeTime(PdsAppConfig.getHandshakeInterval());
        return tbDdsSiteMapper.searchCanAutoAssignSiteIds(StatusEnums.ENABLE.getCode().toString(), aliveHandshakeTime, ScriptTypeEnum.batch.getCode());
    }

    public List<Article> searchArticleByNotInTaskPaper(int monthBefore, String wosQuartile, int pageSize) {
        final Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.MONTH, -monthBefore);
        return articleMapper.searchArticleNotInTaskPaper(DateFormatUtils.format(calendar.getTime(), DatePattern.NORM_DATE_PATTERN), wosQuartile, pageSize);
    }

    @Override
    public Map<String, Object> validateLiterature(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new ServiceException("请上传Excel文件");
        }

        try {
            // 读取Excel文件
            ExcelReader reader = ExcelUtil.getReader(file.getInputStream());
            List<List<Object>> rows = reader.read();

            if (CollUtil.isEmpty(rows)) {
                throw new ServiceException("Excel文件内容为空");
            }

            // 获取表头
            List<Object> headers = rows.get(0);
            int pmidIndex = -1, pmcidIndex = -1, doiIndex = -1;

            for (int i = 0; i < headers.size(); i++) {
                String header = String.valueOf(headers.get(i)).trim().toUpperCase();
                if ("PMID".equals(header)) {
                    pmidIndex = i;
                } else if ("PMCID".equals(header)) {
                    pmcidIndex = i;
                } else if ("DOI".equals(header)) {
                    doiIndex = i;
                }
            }

            if (pmidIndex == -1 && pmcidIndex == -1 && doiIndex == -1) {
                throw new ServiceException("Excel文件必须包含PMID、PMCID、DOI任意一列");
            }

            // 检查数据行数限制（排除表头）
            int dataRowCount = rows.size() - 1;
            if (dataRowCount > 5000) {
                throw new ServiceException("数据行数超过限制，最多允许5000条数据，当前有" + dataRowCount + "条数据");
            }

            if (dataRowCount == 0) {
                throw new ServiceException("Excel文件没有数据行");
            }

            // 批量校验数据并生成结果
            List<Map<String, Object>> resultData = batchValidateRows(rows, pmidIndex, pmcidIndex, doiIndex);

            // 统计有效和无效数据
            int validCount = 0, invalidCount = 0;
            for (Map<String, Object> resultRow : resultData) {
                if (StrUtil.isBlank((String) resultRow.get("错误信息"))) {
                    validCount++;
                } else {
                    invalidCount++;
                }
            }

            // 生成结果Excel文件
            String resultFilePath = generateResultExcel(resultData);

            // 返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("summary", Map.of(
                "total", resultData.size(),
                "valid", validCount,
                "invalid", invalidCount
            ));
            result.put("fileUrl", "/task/downloadValidationResult?filePath=" + resultFilePath);

            return result;

        } catch (Exception e) {
            log.error("文献校验失败", e);
            throw new ServiceException("文献校验失败：" + e.getMessage());
        }
    }

    /**
     * 从行数据中获取指定索引的值
     */
    private String getValueFromRow(List<Object> row, int index) {
        if (index == -1 || index >= row.size()) {
            return "";
        }
        Object value = row.get(index);
        return value == null ? "" : String.valueOf(value).trim();
    }

    /**
     * 生成结果Excel文件
     */
    private String generateResultExcel(List<Map<String, Object>> resultData) {
        String fileName = "literature_validation_result_" + System.currentTimeMillis() + ".xlsx";
        String filePath = System.getProperty("java.io.tmpdir") + File.separator + fileName;

        ExcelWriter writer = ExcelUtil.getWriter(filePath);

        // 写入表头
        List<String> headers = Arrays.asList("PMID", "PMCID", "DOI", "PMID存在", "PMCID存在", "DOI存在", "错误信息");
        writer.writeHeadRow(headers);

        // 写入数据
        for (int i = 0; i < resultData.size(); i++) {
            Map<String, Object> row = resultData.get(i);
            List<Object> rowData = new ArrayList<>();
            for (String header : headers) {
                rowData.add(row.get(header));
            }
            writer.writeRow(rowData);
        }

        writer.close();
        return fileName;
    }

    /**
     * 批量校验数据行
     */
    private List<Map<String, Object>> batchValidateRows(List<List<Object>> rows, int pmidIndex, int pmcidIndex, int doiIndex) {
        List<Map<String, Object>> resultData = new ArrayList<>();

        // 收集所有需要查询的数据
        Set<Long> pmidsToQuery = new HashSet<>();
        Set<Long> pmcIdsToQuery = new HashSet<>();
        Set<String> doisToQuery = new HashSet<>();

        // 预处理数据，收集所有有效的标识符
        List<Map<String, Object>> preprocessedData = new ArrayList<>();
        for (int i = 1; i < rows.size(); i++) {
            List<Object> row = rows.get(i);
            Map<String, Object> rowData = preprocessRow(row, pmidIndex, pmcidIndex, doiIndex);
            preprocessedData.add(rowData);

            // 如果格式校验通过，收集需要查询的数据
            if (StrUtil.isBlank((String) rowData.get("formatError"))) {
                String pmid = (String) rowData.get("pmid");
                String pmcid = (String) rowData.get("pmcid");
                String doi = (String) rowData.get("doi");

                if (StrUtil.isNotBlank(pmid)) {
                    pmidsToQuery.add(Long.valueOf(pmid));
                }
                if (StrUtil.isNotBlank(pmcid)) {
                    // 去掉PMC前缀
                    pmcIdsToQuery.add(Long.valueOf(pmcid.substring(3)));
                }
                if (StrUtil.isNotBlank(doi)) {
                    doisToQuery.add(doi);
                }
            }
        }

        // 批量查询数据库
        Map<Long, Article> pmidArticleMap = new HashMap<>();
        Map<Long, Article> pmcIdArticleMap = new HashMap<>();
        Map<String, List<Article>> doiArticleMap = new HashMap<>();

        if (!pmidsToQuery.isEmpty()) {
            List<Article> articles = articleService.list(
                Wrappers.lambdaQuery(Article.class).in(Article::getPmid, pmidsToQuery)
            );
            pmidArticleMap = articles.stream().collect(Collectors.toMap(Article::getPmid, a -> a));
        }

        if (!pmcIdsToQuery.isEmpty()) {
            List<Article> articles = articleService.list(
                Wrappers.lambdaQuery(Article.class).in(Article::getPmcId, pmcIdsToQuery)
            );
            pmcIdArticleMap = articles.stream().collect(Collectors.toMap(Article::getPmcId, a -> a));
        }

        if (!doisToQuery.isEmpty()) {
            List<Article> articles = articleService.list(
                Wrappers.lambdaQuery(Article.class).in(Article::getDoi, doisToQuery)
            );
            doiArticleMap = articles.stream().collect(Collectors.groupingBy(Article::getDoi));
        }

        // 处理每行数据
        for (Map<String, Object> rowData : preprocessedData) {
            Map<String, Object> result = processRowWithCache(rowData, pmidArticleMap, pmcIdArticleMap, doiArticleMap);
            resultData.add(result);
        }

        return resultData;
    }

    /**
     * 预处理单行数据，进行格式校验
     */
    private Map<String, Object> preprocessRow(List<Object> row, int pmidIndex, int pmcidIndex, int doiIndex) {
        Map<String, Object> rowData = new HashMap<>();

        String pmid = getValueFromRow(row, pmidIndex);
        String pmcid = getValueFromRow(row, pmcidIndex);
        String doi = getValueFromRow(row, doiIndex);

        rowData.put("pmid", pmid);
        rowData.put("pmcid", pmcid);
        rowData.put("doi", doi);

        StringBuilder formatError = new StringBuilder();

        // 检查是否只填写了一列
        int filledCount = 0;
        if (StrUtil.isNotBlank(pmid)) filledCount++;
        if (StrUtil.isNotBlank(pmcid)) filledCount++;
        if (StrUtil.isNotBlank(doi)) filledCount++;

        if (filledCount > 1) {
            formatError.append("PMID/PMCID/DOI只能填写其中一列；");
        } else if (filledCount == 0) {
            formatError.append("PMID/PMCID/DOI至少需要填写一列；");
        } else {
            // 格式校验
            if (StrUtil.isNotBlank(pmid) && !ReUtil.isMatch("^\\d+$", pmid)) {
                formatError.append("PMID格式不对，参考1234567；");
            }
            if (StrUtil.isNotBlank(pmcid) && !ReUtil.isMatch("^PMC\\d+$", pmcid)) {
                formatError.append("PMCID格式不正确，参考PMC12678；");
            }
            if (StrUtil.isNotBlank(doi) && !ReUtil.isMatch("^10\\.\\d+/.+", doi)) {
                formatError.append("DOI格式不正确；");
            }
        }

        rowData.put("formatError", formatError.toString().replaceAll("；$", ""));
        return rowData;
    }

    /**
     * 使用缓存数据处理单行
     */
    private Map<String, Object> processRowWithCache(Map<String, Object> rowData,
                                                   Map<Long, Article> pmidArticleMap,
                                                   Map<Long, Article> pmcIdArticleMap,
                                                   Map<String, List<Article>> doiArticleMap) {
        Map<String, Object> result = new HashMap<>();

        String pmid = (String) rowData.get("pmid");
        String pmcid = (String) rowData.get("pmcid");
        String doi = (String) rowData.get("doi");
        String formatError = (String) rowData.get("formatError");

        result.put("PMID", pmid);
        result.put("PMCID", pmcid);
        result.put("DOI", doi);
        result.put("PMID存在", "否");
        result.put("PMCID存在", "否");
        result.put("DOI存在", "否");

        StringBuilder errorMsg = new StringBuilder();

        // 如果有格式错误，直接返回
        if (StrUtil.isNotBlank(formatError)) {
            result.put("错误信息", formatError);
            return result;
        }

        Article foundArticle = null;

        // 根据输入的标识符查找对应的文章
        if (StrUtil.isNotBlank(pmid)) {
            foundArticle = pmidArticleMap.get(Long.valueOf(pmid));
            if (foundArticle != null) {
                result.put("PMID存在", "是");
            }
        } else if (StrUtil.isNotBlank(pmcid)) {
            Long pmcIdLong = Long.valueOf(pmcid.substring(3));
            foundArticle = pmcIdArticleMap.get(pmcIdLong);
            if (foundArticle != null) {
                result.put("PMCID存在", "是");
            }
        } else if (StrUtil.isNotBlank(doi)) {
            List<Article> articles = doiArticleMap.get(doi);
            if (CollUtil.isNotEmpty(articles)) {
                result.put("DOI存在", "是");
                foundArticle = articles.get(0);

                if (articles.size() > 1) {
                    errorMsg.append("对应的").append(doi).append("对应了").append(articles.size()).append("条数据；");
                }
            }
        }

        // 如果找到了文章，检查其他字段
        if (foundArticle != null) {
            if (foundArticle.getPmid() != null && foundArticle.getPmid() > 0) {
                result.put("PMID存在", "是");
            }
            if (foundArticle.getPmcId() != null && foundArticle.getPmcId() > 0) {
                result.put("PMCID存在", "是");
            }
            if (StrUtil.isNotBlank(foundArticle.getDoi())) {
                result.put("DOI存在", "是");
            }
        }

        result.put("错误信息", errorMsg.toString().replaceAll("；$", ""));
        return result;
    }

    @Override
    public void downloadValidationResult(String filePath, HttpServletResponse response) {
        try {
            String fullPath = System.getProperty("java.io.tmpdir") + File.separator + filePath;
            File file = new File(fullPath);

            if (!file.exists()) {
                throw new ServiceException("文件不存在");
            }

            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=\"" + filePath + "\"");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setContentLengthLong(file.length());

            // 使用缓冲流分块传输文件，避免OOM
            try (FileInputStream fis = new FileInputStream(file);
                 BufferedInputStream bis = new BufferedInputStream(fis);
                 ServletOutputStream sos = response.getOutputStream();
                 BufferedOutputStream bos = new BufferedOutputStream(sos)) {

                byte[] buffer = new byte[8192]; // 8KB缓冲区
                int bytesRead;
                while ((bytesRead = bis.read(buffer)) != -1) {
                    bos.write(buffer, 0, bytesRead);
                }
                bos.flush();
            }

            // 下载完成后删除临时文件
            file.delete();

        } catch (Exception e) {
            log.error("下载校验结果文件失败", e);
            throw new ServiceException("下载失败：" + e.getMessage());
        }
    }

}
