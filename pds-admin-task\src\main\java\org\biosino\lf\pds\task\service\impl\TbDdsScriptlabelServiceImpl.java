package org.biosino.lf.pds.task.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.pds.article.custbean.dto.JournalScriptDTO;
import org.biosino.lf.pds.article.custbean.dto.SelectJournalDTO;
import org.biosino.lf.pds.article.custbean.dto.TbDdsScriptlabelDTO;
import org.biosino.lf.pds.article.custbean.dto.TbDdsScriptlabelScriptDTO;
import org.biosino.lf.pds.article.custbean.vo.ScriptVO;
import org.biosino.lf.pds.article.custbean.vo.SelectJournalVO;
import org.biosino.lf.pds.article.custbean.vo.SelectVO;
import org.biosino.lf.pds.article.custbean.vo.TbDdsScriptlabelVO;
import org.biosino.lf.pds.article.domain.*;
import org.biosino.lf.pds.article.mapper.*;
import org.biosino.lf.pds.common.core.domain.AjaxResult;
import org.biosino.lf.pds.common.enums.StatusEnums;
import org.biosino.lf.pds.common.enums.task.ScriptTypeEnum;
import org.biosino.lf.pds.common.exception.ServiceException;
import org.biosino.lf.pds.common.utils.SecurityUtils;
import org.biosino.lf.pds.task.config.PdsAppConfig;
import org.biosino.lf.pds.task.service.ITbDdsScriptlabelService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 脚本标签Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TbDdsScriptlabelServiceImpl extends ServiceImpl<TbDdsScriptlabelMapper, TbDdsScriptlabel> implements ITbDdsScriptlabelService {
    private final TbDdsScriptlabelMapper tbDdsScriptlabelMapper;
    private final TbDdsSiteMapper tbDdsSiteMapper;
    private final TbDdsScriptlabelScriptMapper tbDdsScriptlabelScriptMapper;
    private final TbDdsScriptlabelJournalMapper tbDdsScriptlabelJournalMapper;

    private final PublisherMapper publisherMapper;
    private final JournalMapper journalMapper;
    private final TbDdsJournalScriptMapper tbDdsJournalScriptMapper;

    /**
     * 查询脚本标签列表
     */
    @Override
    public List<TbDdsScriptlabelVO> selectTbDdsScriptlabelList(TbDdsScriptlabelDTO dto) {
        return tbDdsScriptlabelMapper.selectTbDdsScriptlabelList(dto);
    }

    /**
     * 源刊、高校待分配期刊列表
     */
    @Override
    public List<SelectJournalVO> toSelectJournalList(SelectJournalDTO dto) {
        final String scriptType = dto.getScriptType();
        if (!"2".equals(scriptType) && !"3".equals(scriptType)) {
            throw new ServiceException("脚本类型参数错误：" + scriptType);
        }
        return journalMapper.findToSelectJournal(dto);
    }

    /**
     * 已分配期刊列表
     */
    @Override
    public List<SelectJournalVO> assignedJournalList(SelectJournalDTO dto) {
        final String scriptType = dto.getScriptType();
        if (!"2".equals(scriptType) && !"3".equals(scriptType)) {
            throw new ServiceException("脚本类型参数错误：" + scriptType);
        }

        // 确保labelId不为空
        if (dto.getLabelId() == null) {
            throw new ServiceException("标签ID不能为空");
        }

        return journalMapper.findAssignedJournalList(dto);
    }

    /**
     * 新增/编辑脚本标签
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult saveScriptLabel(TbDdsScriptlabelDTO dto, final Long userId) {
        if (dto == null) {
            return AjaxResult.error("参数不能为空");
        }

        if (StrUtil.isBlank(dto.getLabelName())) {
            return AjaxResult.error("标签名称不能为空");
        }

        final String labelType = dto.getLabelType();
        if (StrUtil.isBlank(labelType)) {
            return AjaxResult.error("标签类型不能为空");
        }
        final List<String> allTypes = ScriptTypeEnum.allCode();
        if (!allTypes.contains(labelType)) {
            return AjaxResult.error("标签类型错误");
        }

        try {
            // 检查标签名称是否已存在
            TbDdsScriptlabel existingByName = tbDdsScriptlabelMapper.findOne(Wrappers.lambdaQuery(TbDdsScriptlabel.class)
                    .eq(TbDdsScriptlabel::getName, dto.getLabelName())
                    .ne(dto.getLabelId() != null, TbDdsScriptlabel::getId, dto.getLabelId()));

            if (existingByName != null) {
                return AjaxResult.error("标签名称已存在，请更换名称");
            }

            TbDdsScriptlabel label;
            final boolean isUpdate = dto.getLabelId() != null;

            final Date now = new Date();
            if (isUpdate) {
                // 修改操作，先查询原记录
                label = getById(dto.getLabelId());
                if (label == null) {
                    return AjaxResult.error("标签不存在");
                }

                // 不允许修改标签类型
                if (!labelType.equals(label.getType())) {
                    return AjaxResult.error("标签类型不允许修改");
                }

                label.setUpdateTime(now);
            } else {
                // 新增操作
                label = new TbDdsScriptlabel();
                label.setCreator(SecurityUtils.getUserId());
                label.setCreateTime(now);
                label.setUpdateTime(now);
                label.setCreator(userId);
            }

            // 设置属性
            label.setName(dto.getLabelName());
            label.setType(labelType);

            // 简化标志位处理，删除脚本和脚本标签处理逻辑
            // label.setStatus(dto.getStatus());
            label.setStatus(StatusEnums.ENABLE.getCode().toString());
            label.setRemark(dto.getRemark());

            // 保存标签记录
            boolean success = saveOrUpdate(label);
            if (!success) {
                return AjaxResult.error(isUpdate ? "修改失败" : "添加失败");
            }

            return AjaxResult.success(isUpdate ? "修改成功" : "添加成功", label.getId());
        } catch (Exception e) {
            log.error("保存脚本标签失败", e);
            return AjaxResult.error("操作失败：" + e.getMessage());
        }
    }


    /**
     * 批量删除脚本标签
     *
     * @param ids 需要删除的脚本标签主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult deleteTbDdsScriptlabelByIds(final Integer[] ids) {
        if (ArrayUtil.isEmpty(ids)) {
            return AjaxResult.error("标签ID不能为空");
        }

        try {
            final List<Integer> idList = Arrays.asList(ids);
            // 查询标签信息
            final List<TbDdsScriptlabel> labels = listByIds(idList);
            if (CollUtil.isEmpty(labels)) {
                return AjaxResult.error("标签不存在");
            }

            // 检查标签是否被节点使用
            final List<TbDdsSite> tbDdsSites = tbDdsSiteMapper.selectList(Wrappers.lambdaQuery(TbDdsSite.class)
                    .in(TbDdsSite::getScriptlabelId, idList)
                    .orderByAsc(TbDdsSite::getId));
            if (CollUtil.isNotEmpty(tbDdsSites)) {
                final Map<Integer, String> lableNameMap = labels.stream().collect(Collectors.toMap(TbDdsScriptlabel::getId, TbDdsScriptlabel::getName));
                final List<String> siteNames = tbDdsSites.stream().sorted(Comparator.comparingInt(x -> idList.indexOf(x.getScriptlabelId())))
                        .map(tbDdsSite -> lableNameMap.get(tbDdsSite.getScriptlabelId()) + "--" + tbDdsSite.getSiteName()).toList();

                if (CollUtil.isNotEmpty(siteNames)) {
                    return AjaxResult.error(StrUtil.format("以下节点已关联该标签，无法删除，标签名和节点名：{}", CollUtil.join(siteNames, ", ")));
                }
            }
            // 删除标签记录
            removeByIds(idList);
            return AjaxResult.success("删除成功");
        } catch (Exception e) {
            log.error("删除脚本标签失败", e);
            throw new ServiceException("删除失败：" + e.getMessage());
        }
    }

    /**
     * 获取脚本标签关联站点信息
     *
     * @param scriptlabelId 脚本标签ID
     * @return 站点列表
     */
    @Override
    public List<TbDdsSite> siteInfo(final Integer scriptlabelId) {
        if (scriptlabelId == null) {
            throw new ServiceException("脚本标签ID不能为空");
        }

        // 查询关联的站点信息
        return tbDdsSiteMapper.selectList(
                Wrappers.lambdaQuery(TbDdsSite.class)
                        .eq(TbDdsSite::getScriptlabelId, scriptlabelId)
                        .orderByAsc(TbDdsSite::getId));
    }

    /**
     * 保存脚本标签与脚本的关联关系
     *
     * @param dto    脚本标签与脚本关联DTO
     * @param userId 用户ID
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult saveLabelAndScript(TbDdsScriptlabelScriptDTO dto, Long userId) {
        if (dto == null) {
            return AjaxResult.error("参数不能为空");
        }

        final List<TbDdsScriptlabelScript> items = dto.getItems();
        if (CollUtil.isEmpty(items)) {
            return AjaxResult.error("脚本列表不能为空");
        }

        // 校验脚本标签ID是否存在
        final Integer scriptlabelId = items.get(0).getScriptlabellId();
        if (scriptlabelId == null) {
            return AjaxResult.error("脚本标签ID不能为空");
        }

        final TbDdsScriptlabel label = getById(scriptlabelId);
        if (label == null) {
            return AjaxResult.error("脚本标签不存在");
        }

        // 判断脚本id是否存在
        final List<Integer> scriptIds = new ArrayList<>();
        for (TbDdsScriptlabelScript item : items) {
            final Integer scriptId = item.getScriptId();
            if (scriptId == null) {
                throw new ServiceException("脚本ID不能为空");
            }
            if (!scriptlabelId.equals(item.getScriptlabellId())) {
                throw new ServiceException("标签ID不一致");
            }
            scriptIds.add(scriptId);
        }

        // 判断脚本id是否有效
        final List<TbDdsJournalScript> scripts = tbDdsJournalScriptMapper.selectByIds(scriptIds);
        if (CollUtil.size(scripts) != CollUtil.size(scriptIds)) {
            throw new ServiceException(StrUtil.format("脚本id存在错误: {}", JSON.toJSONString(scriptIds)));
        }

        // 判断脚本类型和脚本标签类型是否匹配
        final String type = label.getType();
        for (TbDdsJournalScript script : scripts) {
            if (!script.getType().contains(type)) {
                throw new ServiceException(StrUtil.format("脚本类型错误，要求类型为: {}", type));
            }
        }

        try {
            // 先删除原有关联关系
            tbDdsScriptlabelScriptMapper.delete(
                    Wrappers.lambdaQuery(TbDdsScriptlabelScript.class)
                            .eq(TbDdsScriptlabelScript::getScriptlabellId, scriptlabelId)
            );

            // 重新添加关联关系
            Date now = new Date();
            List<TbDdsScriptlabelScript> newItems = new ArrayList<>();

            for (TbDdsScriptlabelScript item : items) {
                // 设置必要的字段
                item.setId(null); // 确保ID为空，让数据库自动生成
                item.setScriptlabellId(scriptlabelId);
                item.setCreator(userId);
                item.setCreateTime(now);

                newItems.add(item);
            }

            // 批量插入新的关联关系
            boolean success = false;
            if (!newItems.isEmpty()) {
                for (TbDdsScriptlabelScript item : newItems) {
                    tbDdsScriptlabelScriptMapper.insert(item);
                }
                success = true;
            }

            if (success) {
                return AjaxResult.success("保存成功");
            } else {
                return AjaxResult.error("保存失败");
            }
        } catch (Exception e) {
            log.error("保存脚本标签与脚本关联关系失败", e);
            throw new ServiceException("保存失败：" + e.getMessage());
        }
    }

    @Override
    public List<ScriptVO> scriptListOfLabel(Integer scriptlabelId) {
        if (scriptlabelId == null) {
            return new ArrayList<>();
        }
        return tbDdsScriptlabelMapper.findScriptListOfLabel(scriptlabelId);
    }

    /**
     * 根据名称查询出版社数据（组成下拉框数据）
     */
    @Override
    public List<SelectVO> findPublisherByName(String name) {
        final LambdaQueryWrapper<Publisher> queryWrapper = Wrappers.lambdaQuery(Publisher.class)
//                .apply(StrUtil.isNotBlank(name), "LOWER(name) LIKE LOWER({0})", "%" + name + "%")
                .apply(StrUtil.isNotBlank(name), "name ILIKE {0}", "%" + name + "%")
                .select(Publisher::getId, Publisher::getName)
                .orderByAsc(Publisher::getName);
        // 下拉框仅取前100个
        final List<Publisher> publishers = publisherMapper.selectList(PageDTO.of(1, 100), queryWrapper);
        final List<SelectVO> data = new ArrayList<>();
        if (CollUtil.isNotEmpty(publishers)) {
            for (Publisher publisher : publishers) {
                data.add(new SelectVO(publisher.getId().toString(), publisher.getName()));
            }
        }
        return data;
    }

    /**
     * 保存期刊与脚本关联
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult saveJournalScript(final JournalScriptDTO dto) {
        if (dto == null || dto.getScriptId() == null) {
            return AjaxResult.error("参数不能为空");
        }
        final Integer scriptId = dto.getScriptId();
        final TbDdsJournalScript script = tbDdsJournalScriptMapper.selectById(scriptId);
        if (script == null) {
            return AjaxResult.error("脚本不存在");
        }

        final Integer labelId = dto.getLabelId();
        if (labelId == null) {
            return AjaxResult.error("请选择脚本标签");
        }
        // 检查标签是否存在
        final TbDdsScriptlabel scriptlabel = getById(labelId);
        if (scriptlabel == null) {
            return AjaxResult.error("标签不存在");
        }
        if (!script.getType().contains(scriptlabel.getType())) {
            throw new ServiceException("脚本类型错误，请选择正确的脚本标签");
        }

        // 当前时间
        final Date now = new Date();

        List<Long> journalIds;
        // 选中所有页模式
        if (Boolean.TRUE.equals(dto.getSelectAllPage())) {
            final List<SelectJournalVO> journals = findJournals(dto);
            // 提取期刊ID列表
            journalIds = parseJournalId(journals);

            // 所有页模式中，分配期刊的脚本时，需要判断是否超出最大添加数量，防止误操作更新大量期刊脚本数据
            final Integer maxAddScriptNum = PdsAppConfig.getMaxAddScriptNum();
            if (CollUtil.size(journalIds) > maxAddScriptNum) {
                return AjaxResult.error(StrUtil.format("最多只能选择{}个期刊", maxAddScriptNum));
            }
        }
        // 指定期刊ID模式
        else if (CollUtil.isNotEmpty(dto.getJournalIds())) {
            // 批量更新期刊脚本ID
            journalIds = dto.getJournalIds().stream().distinct().toList();
        } else {
            return AjaxResult.error("未指定期刊");
        }

        // 批量更新期刊脚本ID
        int successCount = journalMapper.batchUpdateScriptId(journalIds, scriptId, now);

        if (successCount > 0) {
            return AjaxResult.success("成功为 " + successCount + " 个期刊分配脚本");
        } else {
            return AjaxResult.error("分配脚本失败");
        }
    }

    /**
     * 移除期刊脚本关联
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult removeJournalScript(final JournalScriptDTO dto) {
        if (dto == null) {
            return AjaxResult.error("参数不能为空");
        }

        // 当前时间
        final Date now = new Date();

        List<Long> journalIds;
        // 选中所有页模式
        if (Boolean.TRUE.equals(dto.getSelectAllPage())) {
            // 提取期刊ID列表
            final List<SelectJournalVO> journals = findJournals(dto);
            journalIds = parseJournalId(journals);
        }
        // 指定期刊ID模式
        else if (CollUtil.isNotEmpty(dto.getJournalIds())) {
            journalIds = dto.getJournalIds();
            // 批量更新期刊脚本ID为null
        } else {
            return AjaxResult.error("未指定期刊");
        }

        // 批量更新期刊脚本ID为null
        int successCount = journalMapper.batchUpdateScriptId(journalIds, null, now);

        if (successCount > 0) {
            return AjaxResult.success("成功移除 " + successCount + " 个期刊的脚本");
        } else {
            return AjaxResult.error("移除脚本失败");
        }
    }

    private List<SelectJournalVO> findJournals(final SelectJournalDTO dto) {
        // 检查查询条件是否至少有一个参数存在
        boolean hasCondition = CollUtil.isNotEmpty(dto.getPublisher())
                || StrUtil.isNotBlank(dto.getJournalName())
                || StrUtil.isNotBlank(dto.getIssnPrint())
                || StrUtil.isNotBlank(dto.getIssnElectronic())
                || StrUtil.isNotBlank(dto.getHasScript())
                || StrUtil.isNotBlank(dto.getScriptName());

        if (!hasCondition) {
            throw new ServiceException("请至少指定一个查询条件");
        }

        // 使用查询条件查询所有符合条件的期刊
        List<SelectJournalVO> journals = toSelectJournalList(dto);
        if (CollUtil.isEmpty(journals)) {
            throw new ServiceException("未找到符合条件的期刊");
        }

        return journals;
    }

    private List<Long> parseJournalId(List<SelectJournalVO> journals) {
        if (CollUtil.isEmpty(journals)) {
            return new ArrayList<>();
        }
        // 提取期刊ID列表
        return journals.stream()
                .map(journal -> Long.parseLong(journal.getJournalId()))
                .distinct()
                .toList();
    }

    private List<Integer> parseScriptId(List<SelectJournalVO> journals) {
        if (CollUtil.isEmpty(journals)) {
            return new ArrayList<>();
        }
        // 提取脚本ID列表
        return journals.stream()
                .map(SelectJournalVO::getScriptId)
                .filter(Objects::nonNull)
                .distinct()
                .toList();
    }

    /**
     * 应用期刊到标签
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult applyJournalToLabel(final JournalScriptDTO dto, final Long userId) {
        if (dto == null || dto.getLabelId() == null) {
            return AjaxResult.error("参数不能为空");
        }
        final Integer labelId = dto.getLabelId();
        // 检查标签是否存在
        final TbDdsScriptlabel label = getById(labelId);
        if (label == null) {
            return AjaxResult.error("标签不存在");
        }

        // 当前时间
        final Date now = new Date();

        List<Long> journalIds;
        // 选中所有页模式
        if (Boolean.TRUE.equals(dto.getSelectAllPage())) {
            // 提取期刊ID列表，排除已关联的
            final List<SelectJournalVO> journals = findJournals(dto);
            journalIds = parseJournalId(journals);
        }
        // 指定期刊ID模式
        else if (CollUtil.isNotEmpty(dto.getJournalIds())) {
            journalIds = dto.getJournalIds().stream().distinct().toList();
        } else {
            return AjaxResult.error("未指定期刊");
        }

        if (CollUtil.isEmpty(journalIds)) {
            return AjaxResult.error("期刊列表为空");
        }

        // 使用增量方式应用标签
        int successCount = incrementalApplyJournalToLabel(journalIds, labelId, now, userId);

        if (successCount > 0) {
            return AjaxResult.success("成功应用 " + successCount + " 个期刊到标签");
        } else {
            return AjaxResult.error("应用期刊到标签失败，可能是期刊没有脚本或已经应用到标签");
        }
    }

    /**
     * 增量应用期刊到标签
     *
     * @param journalIds 期刊ID列表
     * @param labelId    标签ID
     * @param now        当前时间
     * @param userId
     * @return 成功应用的期刊数量
     */
    private int incrementalApplyJournalToLabel(List<Long> journalIds, Integer labelId, Date now, Long userId) {
        // 批量查询期刊对应的期刊ID，排除已关联的
        final List<Long> journalIdsToSave = journalMapper.findUnApplyJournalIds(journalIds, labelId);
        if (CollUtil.isEmpty(journalIdsToSave)) {
            return 0;
        }

        final List<TbDdsScriptlabelJournal> data = new ArrayList<>();

        for (Long journalId : journalIdsToSave) {
            // 创建新的关联
            final TbDdsScriptlabelJournal relation = new TbDdsScriptlabelJournal();
            relation.setScriptlabellId(labelId);
            relation.setJournalId(journalId);
            relation.setCreator(userId);
            relation.setCreateTime(now);

            // 保存关联
            data.add(relation);
        }

        final int successCount = CollUtil.size(data);
        if (successCount > 0) {
            tbDdsScriptlabelJournalMapper.insert(data, Constants.DEFAULT_BATCH_SIZE);
        }

        return successCount;
    }

    /**
     * 移除标签的期刊分配
     *
     * @param dto    期刊与标签关联DTO
     * @param userId 用户ID
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult removeJournalApply(final JournalScriptDTO dto, final Long userId) {
        if (dto == null || dto.getLabelId() == null) {
            return AjaxResult.error("参数不能为空");
        }
        final Integer labelId = dto.getLabelId();
        // 检查标签是否存在
        final TbDdsScriptlabel label = getById(labelId);
        if (label == null) {
            return AjaxResult.error("标签不存在");
        }

        List<Long> journalIds;
        // 选中所有页模式
        if (Boolean.TRUE.equals(dto.getSelectAllPage())) {
            // 提取期刊ID列表
            final List<SelectJournalVO> journals = journalMapper.findAssignedJournalList(dto);
            journalIds = parseJournalId(journals);
        }
        // 指定期刊ID模式
        else if (CollUtil.isNotEmpty(dto.getJournalIds())) {
            journalIds = dto.getJournalIds().stream().distinct().toList();
        } else {
            return AjaxResult.error("未指定期刊");
        }

        if (CollUtil.isEmpty(journalIds)) {
            return AjaxResult.error("期刊列表为空");
        }

        // 删除标签与期刊的关联
        final LambdaQueryWrapper<TbDdsScriptlabelJournal> queryWrapper = Wrappers.lambdaQuery(TbDdsScriptlabelJournal.class)
                .eq(TbDdsScriptlabelJournal::getScriptlabellId, labelId)
                .in(TbDdsScriptlabelJournal::getJournalId, journalIds);

        int deletedCount = tbDdsScriptlabelJournalMapper.delete(queryWrapper);

        if (deletedCount > 0) {
            return AjaxResult.success("成功移除 " + deletedCount + " 个期刊与标签的关联");
        } else {
            return AjaxResult.error("移除期刊与标签的关联失败");
        }
    }

    /**
     * 根据标签类型查询标签列表
     */
    @Override
    public List<TbDdsScriptlabelVO> selectLabelsByType(final String type) {
        if (StrUtil.isBlank(type)) {
            return new ArrayList<>();
        }

        final List<ScriptTypeEnum> groupByType = ScriptTypeEnum.getGroupByType(type);
        if (CollUtil.isEmpty(groupByType)) {
            throw new ServiceException("标签类型不存在");
        }
        final List<String> labelTypes = groupByType.stream().map(ScriptTypeEnum::getCode).toList();

        // 构建查询条件
        final TbDdsScriptlabelDTO dto = new TbDdsScriptlabelDTO();
        dto.setLabelTypes(labelTypes);
        // dto.setLabelType(type);
        // 只查询状态正常的标签
        dto.setStatus(StatusEnums.ENABLE.getCode().toString());

        // 调用查询方法
        return selectTbDdsScriptlabelList(dto);
    }

}
