package org.biosino.lf.pds.article.service;

import org.biosino.lf.pds.article.domain.Publisher;
import org.biosino.lf.pds.article.dto.PublisherMergeDTO;
import org.biosino.lf.pds.article.dto.PublisherValidationResult;

import java.util.List;

/**
 * 出版社验证服务接口
 *
 * <AUTHOR>
 */
public interface PublisherValidationService {

    /**
     * 验证出版社更新时的名称和别名唯一性
     *
     * @param publisher 出版社实体
     * @return 验证结果
     */
    PublisherValidationResult validatePublisherUpdate(Publisher publisher);

    /**
     * 验证出版社合并时的名称和别名唯一性
     *
     * @param publisherMergeDTO 出版社合并DTO
     * @return 验证结果
     */
    PublisherValidationResult validatePublisherMerge(PublisherMergeDTO publisherMergeDTO);

    /**
     * 验证出版社保存时的名称和别名唯一性
     *
     * @param publisher 出版社实体
     * @param excludeIds 要排除的出版社ID列表
     * @return 验证结果
     */
    PublisherValidationResult validatePublisherSave(Publisher publisher, List<Long> excludeIds);

    /**
     * 验证单个名称或别名的唯一性
     *
     * @param fieldName 字段名称
     * @param value 要验证的值
     * @param excludeIds 要排除的出版社ID列表
     * @return 验证结果
     */
    PublisherValidationResult validateSingleValue(String fieldName, String value, List<Long> excludeIds);
}
