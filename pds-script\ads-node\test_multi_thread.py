# -*- coding: utf-8 -*-
"""
多线程任务处理器测试脚本
Author: yhju
Date: 2025/7/16
Version: 1.0.0
"""

import logging
import os
import queue
import sys
import threading
import time

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import util
import multi_thread_task

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(threadName)s] %(levelname)s: %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_multi_thread.log', encoding="utf8")
    ]
)

logger = logging.getLogger(__name__)


class MockApiInvoker:
    """模拟API调用类"""

    def __init__(self):
        self.task_counter = 0
        self.max_tasks = 20
        self.journals = ['journal_001', 'journal_002', 'journal_003', 'journal_004', 'journal_005', 'journal_006']
        self.script_ids = ['script_A', 'script_B', 'script_C', 'script_D', 'script_E', 'script_F']

    def next_task_info(self, active_script_ids=None):
        """模拟获取下一个任务"""
        if self.task_counter >= self.max_tasks:
            return {"status": "none"}

        self.task_counter += 1
        journal_id = self.journals[self.task_counter % len(self.journals)]
        script_id = self.script_ids[self.task_counter % len(self.script_ids)]

        # 模拟后台优先返回不同脚本ID的任务
        if active_script_ids:
            logger.info(f"[API] 收到活跃脚本ID列表: {active_script_ids}")
            # 尝试找一个不在活跃列表中的脚本ID
            for sid in self.script_ids:
                if sid not in active_script_ids:
                    script_id = sid
                    break

        return {
            "status": "success",
            "taskId": f"TASK_{self.task_counter:03d}",
            "docId": f"DOC_{self.task_counter:03d}",
            "pmid": f"PMID_{self.task_counter:03d}" if self.task_counter % 3 != 0 else "",  # 部分任务没有pmid
            "journalId": journal_id,
            "currSiteType": "2",  # 期刊类型
            "scriptServicePath": "test_script.pyscript",
            "scriptMD5": "test_md5_hash",
            "scriptIdStr": script_id
        }

    def send_task_message(self, task_id, message):
        """模拟发送任务消息"""
        logger.info(f"[API] 任务消息 {task_id}: {message}")

    def site_info(self):
        """模拟获取站点信息"""
        return {
            "siteType": "2",
            "script": [
                {
                    "scriptServicePath": "test_script.pyscript",
                    "scriptMD5": "test_md5_hash",
                    "scriptIdStr": "test_script_id"
                }
            ]
        }

    def get_task_script_file(self, script_id_str, to_file_path):
        """模拟下载脚本文件"""
        # 创建一个简单的测试脚本
        script_content = '''# -*- coding: utf-8 -*-
import threading
import time
import os

class Script(threading.Thread):
    def __init__(self, site_id, task, api_invoker):
        super().__init__()
        self.site_id = site_id
        self.task = task
        self.api_invoker = api_invoker
    
    def can_download(self):
        return True
    
    def run(self):
        # 模拟下载过程
        journal_id = self.task.get("journalId", "unknown")
        task_id = self.task.get("taskId", "unknown")
        doc_id = self.task.get("docId", "unknown")
        script_id = self.task.get("scriptIdStr", "unknown")
        pmid = self.task.get("pmid", "")

        if pmid:
            print(f"[脚本] 开始下载任务 {task_id} (期刊: {journal_id}, 脚本ID: {script_id}, docId: {doc_id}, PMID: {pmid})")
        else:
            print(f"[脚本] 开始下载任务 {task_id} (期刊: {journal_id}, 脚本ID: {script_id}, docId: {doc_id})")

        # 模拟下载时间（1-3秒）
        import random
        download_time = random.uniform(1, 3)
        time.sleep(download_time)

        # 创建结果文件
        result_dir = self.task.get("resultDirPath")
        if result_dir and not os.path.exists(result_dir):
            os.makedirs(result_dir)

        # 创建必需的attach_note.txt文件
        attach_file = os.path.join(result_dir, "attach_note.txt")
        with open(attach_file, "w", encoding="utf-8") as f:
            content = f"任务 {task_id} 下载完成\\ndocId: {doc_id}\\n期刊: {journal_id}\\n脚本ID: {script_id}\\n下载时间: {download_time:.2f}秒"
            if pmid:
                content += f"\\nPMID: {pmid}"
            f.write(content)

        # 创建一个模拟的PDF文件
        pdf_file = os.path.join(result_dir, f"{doc_id}.pdf")
        with open(pdf_file, "w", encoding="utf-8") as f:
            f.write("模拟PDF内容")

        print(f"[脚本] 任务 {task_id} (docId: {doc_id}) 下载完成，耗时 {download_time:.2f}秒")
    
    def close(self):
        pass
'''

        with open(to_file_path, 'w', encoding='utf-8') as f:
            f.write(script_content)


class MockDbFile:
    """模拟数据库类"""

    def __init__(self):
        self.items = []
        self.lock = threading.Lock()

    def insert_item(self, task_info):
        """插入任务信息"""
        with self.lock:
            self.items.append({
                'id': len(self.items) + 1,
                'task_info': task_info,
                'create_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                'upload_num': 0
            })
            logger.info(f"[DB] 保存任务信息: {task_info.get('taskId', 'Unknown')}")

    def close(self):
        pass


def test_multi_thread_processor():
    """测试多线程任务处理器"""
    logger.info("=" * 60)
    logger.info("开始测试多线程任务处理器")
    logger.info("=" * 60)

    # 创建模拟对象
    api_invoker = MockApiInvoker()
    db_file = MockDbFile()

    # 创建多线程任务处理器
    processor = multi_thread_task.MultiThreadTaskProcessor(
        site_id=123,
        db_file=db_file,
        api_invoker=api_invoker
    )

    # 启动处理器
    processor.start()

    # 运行30秒
    logger.info("任务处理器已启动，将运行30秒...")

    # 每5秒打印一次统计信息
    for i in range(6):
        time.sleep(5)
        stats = processor.get_stats()
        queue_status = processor.get_queue_status()
        logger.info(f"统计信息 ({i * 5 + 5}秒): {stats}")
        logger.info(f"队列状态 ({i * 5 + 5}秒): {queue_status}")

    # 停止处理器
    logger.info("停止任务处理器...")
    processor.stop()
    processor.join(timeout=10)

    # 最终统计
    final_stats = processor.get_stats()
    logger.info("=" * 60)
    logger.info("测试完成")
    logger.info(f"最终统计: {final_stats}")
    logger.info(f"数据库中的任务数: {len(db_file.items)}")
    logger.info("=" * 60)

    # 清理测试文件
    cleanup_test_files()


def cleanup_test_files():
    """清理测试产生的文件"""
    import shutil

    try:
        # 清理data目录
        data_dir = os.path.join(util.program_root_dir(), "data")
        if os.path.exists(data_dir):
            shutil.rmtree(data_dir)
            logger.info("清理测试数据目录")

        # 清理script目录中的测试脚本
        script_dir = os.path.join(util.program_root_dir(), "script")
        if os.path.exists(script_dir):
            for file in os.listdir(script_dir):
                if file.startswith("test_") or file.startswith("sit_"):
                    file_path = os.path.join(script_dir, file)
                    os.remove(file_path)
                    logger.info(f"清理测试脚本: {file}")

    except Exception as e:
        logger.warning(f"清理测试文件时出错: {e}")


def test_script_id_isolation():
    """测试脚本ID隔离功能"""
    logger.info("=" * 60)
    logger.info("测试脚本ID隔离功能")
    logger.info("=" * 60)

    # 创建只有3个脚本ID的API模拟器
    api_invoker = MockApiInvoker()
    api_invoker.script_ids = ['script_X', 'script_Y', 'script_Z']  # 只有3个脚本ID
    api_invoker.journals = ['journal_A', 'journal_B', 'journal_C', 'journal_D']  # 多个期刊
    api_invoker.max_tasks = 12

    db_file = MockDbFile()

    processor = multi_thread_task.MultiThreadTaskProcessor(
        site_id=456,
        db_file=db_file,
        api_invoker=api_invoker
    )

    processor.start()

    # 运行15秒观察脚本ID隔离效果
    for i in range(3):
        time.sleep(5)
        stats = processor.get_stats()
        logger.info(f"脚本ID隔离测试 ({i * 5 + 5}秒): {stats}")

    processor.stop()
    processor.join(timeout=10)

    logger.info("脚本ID隔离测试完成")
    cleanup_test_files()


def test_queue_protection():
    """测试队列保护机制"""
    logger.info("=" * 60)
    logger.info("测试队列保护机制")
    logger.info("=" * 60)

    # 创建一个会产生大量任务的API模拟器
    api_invoker = MockApiInvoker()
    api_invoker.script_ids = ['script_A']  # 只有1个脚本ID，会导致大量任务排队
    api_invoker.max_tasks = 100  # 大量任务

    db_file = MockDbFile()

    # 创建一个小队列的处理器来测试队列保护
    processor = multi_thread_task.MultiThreadTaskProcessor(
        site_id=789,
        db_file=db_file,
        api_invoker=api_invoker
    )

    # 修改队列大小为较小值以便测试
    processor._MultiThreadTaskProcessor__task_queue = queue.Queue(maxsize=10)

    processor.start()

    # 运行20秒观察队列保护效果
    for i in range(4):
        time.sleep(5)
        stats = processor.get_stats()
        queue_status = processor.get_queue_status()
        logger.info(f"队列保护测试 ({i * 5 + 5}秒)")
        logger.info(f"  统计信息: {stats}")
        logger.info(f"  队列状态: {queue_status}")

    processor.stop()
    processor.join(timeout=10)

    logger.info("队列保护测试完成")
    cleanup_test_files()


if __name__ == "__main__":
    try:
        # 测试1: 基本多线程功能
        test_multi_thread_processor()

        time.sleep(2)

        # 测试2: 脚本ID隔离功能
        test_script_id_isolation()

        time.sleep(2)

        # 测试3: 队列保护机制
        test_queue_protection()

    except KeyboardInterrupt:
        logger.info("测试被用户中断")
    except Exception as e:
        logger.exception(f"测试过程中出现异常: {e}")
    finally:
        cleanup_test_files()
        logger.info("测试结束")
