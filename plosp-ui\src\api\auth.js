import request from '@/utils/request'

/**
 * 用户登录
 * @param {Object} data - 登录数据
 * @param {string} data.email - 邮箱
 * @param {string} data.password - 密码
 * @param {boolean} data.rememberMe - 记住我
 */
export function login(data) {
  return request({
    url: '/api/auth/login',
    method: 'post',
    data
  })
}

/**
 * 用户注册
 * @param {Object} data - 注册数据
 */
export function register(data) {
  return request({
    url: '/api/auth/register',
    method: 'post',
    data
  })
}

/**
 * 发送验证码
 * @param {string} email - 邮箱地址
 */
export function sendVerificationCode(email) {
  return request({
    url: '/api/auth/send-verification-code',
    method: 'post',
    params: { email }
  })
}

/**
 * 验证邮箱
 * @param {string} email - 邮箱地址
 * @param {string} code - 验证码
 */
export function verifyEmail(email, code) {
  return request({
    url: '/api/auth/verify-email',
    method: 'post',
    params: { email, code }
  })
}

/**
 * 用户退出
 */
export function logout() {
  return request({
    url: '/api/auth/logout',
    method: 'post'
  })
}

/**
 * 获取用户资料
 */
export function getUserProfile() {
  return request({
    url: '/api/auth/profile',
    method: 'get'
  })
}

/**
 * 更新用户资料
 * @param {Object} data - 用户资料数据
 */
export function updateUserProfile(data) {
  return request({
    url: '/api/auth/profile',
    method: 'put',
    data
  })
}

/**
 * 修改密码
 * @param {string} oldPassword - 旧密码
 * @param {string} newPassword - 新密码
 */
export function changePassword(oldPassword, newPassword) {
  return request({
    url: '/api/auth/change-password',
    method: 'put',
    params: { oldPassword, newPassword }
  })
}

/**
 * 忘记密码 - 发送重置码
 * @param {string} email - 邮箱地址
 */
export function forgotPassword(email) {
  return request({
    url: '/api/auth/forgot-password',
    method: 'post',
    params: { email }
  })
}

/**
 * 重置密码
 * @param {string} email - 邮箱地址
 * @param {string} code - 验证码
 * @param {string} newPassword - 新密码
 */
export function resetPassword(email, code, newPassword) {
  return request({
    url: '/api/auth/reset-password',
    method: 'post',
    params: { email, code, newPassword }
  })
}
