package org.biosino.lf.pds.common.enums.task;

import lombok.Getter;

import java.util.*;

/**
 * 脚本类型枚举
 *
 * <AUTHOR>
 */
@Getter
public enum ScriptTypeEnum {
    batch("批次", "1"),
    journal("源刊", "2"),
    school("高校", "3");

    private final String text;
    private final String code;

    ScriptTypeEnum(String text, String code) {
        this.text = text;
        this.code = code;
    }

    public static List<String> allCode() {
        List<String> list = new ArrayList<>();
        ScriptTypeEnum[] values = values();
        for (ScriptTypeEnum value : values) {
            list.add(value.getCode());
        }
        return list;
    }

    public static Map<String, String> toMap() {
        Map<String, String> map = new LinkedHashMap<>();
        ScriptTypeEnum[] values = values();
        for (ScriptTypeEnum value : values) {
            map.put(value.getCode(), value.getText());
        }
        return map;
    }

    public static Optional<ScriptTypeEnum> getByCode(String code) {
        if (code == null) {
            return Optional.empty();
        }

        ScriptTypeEnum[] values = values();
        for (ScriptTypeEnum value : values) {
            if (value.getCode().equals(code)) {
                return Optional.of(value);
            }
        }
        return Optional.empty();
    }

    /**
     * 根据类型获取分组
     * 源刊、高校一组，批次单独一组
     */
    public static List<ScriptTypeEnum> getGroupByType(String code) {
        final List<ScriptTypeEnum> list = new ArrayList<>();
        if (code == null) {
            return list;
        }

        final ScriptTypeEnum[] values = values();
        for (ScriptTypeEnum value : values) {
            if (value.getCode().equals(code)) {
                if (value.equals(ScriptTypeEnum.batch)) {
                    list.add(ScriptTypeEnum.batch);
                } else {
                    list.add(ScriptTypeEnum.journal);
                    list.add(ScriptTypeEnum.school);
                }
                break;
            }
        }
        return list;
    }

}
