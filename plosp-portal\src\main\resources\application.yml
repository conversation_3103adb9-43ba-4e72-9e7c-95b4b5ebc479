# 项目相关配置
app:
  # 名称
  name: PLOSP Portal
  # 版本
  version: 2.0.0
  # 版权年份
  copyrightYear: 2025
  # 获取ip地址开关
  addressEnabled: true
  # 验证码类型 math 数字计算 char 字符验证
  captchaType: math

# 开发环境配置
server:
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 500
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 20

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
  # 应用名称
  application:
    name: plosp-portal
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: dev
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 10MB
      # 设置总上传的文件大小
      max-request-size: 20MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  # 邮件配置
  mail:
    # 默认编码
    default-encoding: UTF-8
    host: mail.cstnet.cn
    username: <EMAIL>
    password: nXAaY*7H&^Cgu3%q
    port: 994
    properties.mail.smtp:
      ssl:
        enable: true
      socketFactory:
        class: javax.net.ssl.SSLSocketFactory

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥 (生产环境请更换为更安全的密钥)
  secret: plosp-portal-jwt-secret-key-2025-v2.0.0
  # 令牌有效期（单位分钟）
  expireTime: 720

# MyBatis配置
mybatis-plus:
  # 搜索指定包别名
  typeAliasesPackage: org.biosino.lf.pds.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml
  global-config:
    db-config:
      logic-delete-field: delFlag
      logic-delete-value: 1
      logic-not-delete-value: 0
      update-strategy: always

# PageHelper分页插件
pagehelper:
  helperDialect: postgresql
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql

# Springdoc配置
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  group-configs:
    - group: 'portal-auth'
      display-name: 'Portal Authentication APIs'
      paths-to-match: '/api/auth/**'
      packages-to-scan: org.biosino.lf.plosp.portal.web.controller.auth
    - group: 'portal-public'
      display-name: 'Portal Public APIs'
      paths-to-match: '/api/public/**'
      packages-to-scan: org.biosino.lf.plosp.portal.web.controller.public
    - group: 'portal-user'
      display-name: 'Portal User APIs'
      paths-to-match: '/api/user/**'
      packages-to-scan: org.biosino.lf.plosp.portal.web.controller.user

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /api/auth/register,/api/auth/login
  # 匹配链接
  urlPatterns: /api/*

# 日志配置
logging:
  level:
    org.biosino.lf.plosp.portal: info
    org.biosino.lf.pds: info
    org.springframework.security: debug
    org.springframework.web: info
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized
