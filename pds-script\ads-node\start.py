# coding=utf8
__author__ = 'yhju'

import argparse
import json
import logging
import logging.config
import os
import sys
from logging.handlers import RotatingFileHandler

import api
import db
import handshake
import task
import multi_thread_task
import upload
import util


def str2bool(v):
    if isinstance(v, bool):
        return v
    if v.lower() in ('yes', 'true', 't', 'y', '1'):
        return True
    elif v.lower() in ('no', 'false', 'f', 'n', '0'):
        return False
    else:
        raise argparse.ArgumentTypeError('Boolean value expected.')


def __logger_config():
    """
       日志默認配置
    """
    # 日志輸出到文件
    if not os.path.exists("log"):
        os.makedirs("log")

    # 首先使用配置文件中 的定义
    log_config_file = os.path.abspath("./conf/logger.conf")
    if os.path.exists(log_config_file):
        logging.config.fileConfig(fname=log_config_file)
        logging.info("日志加载完成，配置文件 {}".format(log_config_file))
        return

    # 配置文件中没有配置log， 使用默认 logger
    logger = logging.getLogger()
    logger.setLevel(logging.NOTSET)

    formatter = logging.Formatter(
        fmt='%(asctime)s - %(name)s - %(levelname)s - %(module)s : %(lineno)d - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # 日志輸出到 控制台
    ch = logging.StreamHandler()
    ch.setLevel(logging.DEBUG)
    ch.setFormatter(formatter)
    logger.addHandler(ch)

    fh = RotatingFileHandler(
        filename="./log/ads.log",
        maxBytes=30 * 1024 * 1024,
        backupCount=10,
        encoding="utf8"
    )
    fh.setLevel(logging.WARNING)
    fh.setFormatter(formatter)
    logger.addHandler(fh)
    logger.info("日志加载完成（使用默认配置）")


def __run():
    """
    启动脚本执行
    :return:
    """
    logger = logging.getLogger()

    try:
        # 初始化参数构造器
        parser = argparse.ArgumentParser(description='PDS-V2 文献下载系统')
        # 在参数构造器中添加命令行参数
        parser.add_argument('--siteId', type=int, help='站点ID')
        parser.add_argument('--apiPath', type=str, help='API服务器地址')
        parser.add_argument('--apiTimeout', type=int, help='API超时时间(秒)')
        parser.add_argument('--apiEncoding', type=str, help='API编码格式')
        parser.add_argument('--handshakeIntervalTime', type=int, help='握手间隔时间(秒)')
        parser.add_argument('--ftpPath', type=str, help='FTP服务器地址')
        parser.add_argument('--ftpPort', type=int, help='FTP端口')
        parser.add_argument('--ftpUsername', type=str, help='FTP用户名')
        parser.add_argument('--ftpPassword', type=str, help='FTP密码')
        parser.add_argument('--ftpTimeout', type=int, help='FTP超时时间(秒)')
        parser.add_argument('--ftpPasv', type=str2bool, help='FTP被动模式')
        # 获取所有的命令行参数
        args = parser.parse_args()

        # 站点配置
        site_conf = util.update_site_config(args.__dict__)
        logger.info(f"启动参数：\n{json.dumps(site_conf, indent=True)}")

        # 初始化 api 调用类
        api_invoker = api.ApiInvoker(site_conf["apiPath"],
                                    site_conf["siteId"],
                                    site_conf["apiToken"],
                                    encoding=site_conf["apiEncoding"],
                                    timeout=site_conf["apiTimeout"])

        # 初始化sqlite数据库
        __db_file = db.DbFile()
        logger.info("数据库初始化完成")

        # 启动任务执行器 - 根据站点类型选择单线程或多线程处理器
        # 首先获取站点信息来判断站点类型
        site_info = api_invoker.site_info()
        if not site_info:
            logger.warning("获取站点信息失败，无法启动任务处理器")
            raise ValueError("获取站点信息失败，无法启动任务处理器")
        site_type = site_info.get("siteType", None)
        if not site_type:
            logger.warning("站点信息中未包含站点类型，无法启动任务处理器")
            raise ValueError("站点信息中未包含站点类型，无法启动任务处理器")

        try:

            logger.info(f"获取到站点信息，站点类型: {site_type}")

            if site_type in ['2', '3']:
                # 期刊类型站点，使用多线程处理器
                logger.info(f"站点类型为 {site_type}，启用多线程任务处理器")
                task_processor = multi_thread_task.MultiThreadTaskProcessor(
                    site_id=site_conf["siteId"],
                    db_file=__db_file,
                    api_invoker=api_invoker
                )
            else:
                # 其他类型站点，使用单线程处理器
                logger.info(f"站点类型为 {site_type}，使用单线程任务处理器")
                task_processor = task.TaskProcessor(
                    site_id=site_conf["siteId"],
                    db_file=__db_file,
                    api_invoker=api_invoker
                )
        except Exception as e:
            logger.warning(f"获取站点信息失败，使用默认单线程处理器: {e}")
            task_processor = task.TaskProcessor(
                site_id=site_conf["siteId"],
                db_file=__db_file,
                api_invoker=api_invoker
            )

        # 启动任务处理线程
        task_processor.start()
        logger.info("任务处理线程已启动")

        # 启动上传结果线程
        upload_processor = upload.Uploader(site_conf=site_conf, db_file=__db_file, api_invoker=api_invoker)
        upload_processor.start()
        logger.info("上传处理线程已启动")

        # 启动握手线程
        hs = handshake.Handshake(site_id=site_conf["siteId"],
                                 api_invoker=api_invoker,
                                 interval=site_conf["handshakeIntervalTime"])
        hs.start()
        logger.info("握手线程已启动")

        logger.info("程序启动成功，所有线程已启动")

        # 主线程一直运行，直到任务处理线程退出
        task_processor.join()

    except KeyboardInterrupt:
        logger.info("接收到中断信号，程序正在退出...")
    except Exception as e:
        logger.exception(f"程序启动失败: {e}")
        raise
    finally:
        # 清理资源
        logger.info("开始清理资源...")
        try:
            if 'task_processor' in locals():
                task_processor.stop()
            if 'upload_processor' in locals():
                upload_processor.stop()
            if '__db_file' in locals():
                __db_file.close()
            if 'hs' in locals():
                hs.stop()
        except Exception as e:
            logger.warning(f"清理资源时出错: {e}")
        logger.info("程序已退出")


if __name__ == "__main__":
    """
    传递程序启动类
    """
    # 判断 python 版本
    if sys.version_info < (3, 6):
        raise Exception("请使用 Python 3.6 及以上运行本程，当前版本 " + sys.version)

    # 设置工作目录
    os.chdir(util.program_root_dir())

    # 初始化日志配置
    __logger_config()

    # 启动脚本
    __run()
