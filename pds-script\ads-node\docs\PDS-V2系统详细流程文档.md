# PDS-V2 文献下载系统详细流程文档

## 项目概述

PDS-V2 是一个基于 Python 3.8.10 的分布式文献下载系统，主要功能是从服务端获取下载任务，动态获取和执行 Python 脚本，从指定网站下载 PDF 文件，并将结果上传到服务器。系统采用多线程架构，支持期刊类型站点的并发处理，具有完善的容错机制和队列保护功能。

## 系统架构

### 核心模块结构
```
pds-script/ads-node/
├── start.py              # 主启动脚本，程序入口
├── task.py               # 单线程任务处理器
├── multi_thread_task.py  # 多线程任务处理器
├── task_executor.py      # 任务执行器基类
├── api.py                # API通信模块
├── script.py             # 脚本管理模块
├── upload.py             # 文件上传模块
├── handshake.py          # 握手通信模块
├── db.py                 # SQLite数据库操作模块
├── util.py               # 工具函数模块
├── ftp.py                # FTP客户端模块
└── conf/                 # 配置文件目录
    ├── site.json         # 站点配置
    ├── logger.conf       # 日志配置
    └── selenium.json     # Selenium配置
```

### 系统线程架构
系统采用多线程架构，包含以下核心线程：
- **主线程**: 负责程序启动、资源管理和线程协调
- **任务处理线程**: 循环获取和执行下载任务
- **上传线程**: 循环上传执行结果到服务器
- **握手线程**: 定期发送系统状态信息

## 程序启动流程 (start.py)

### 1. 系统初始化

#### 1.1 环境检查
```python
# 检查Python版本
if sys.version_info < (3, 6):
    raise Exception("请使用 Python 3.6 及以上运行本程序")

# 设置工作目录
os.chdir(util.program_root_dir())
```

#### 1.2 日志系统初始化
```python
def __logger_config():
    # 优先使用配置文件 ./conf/logger.conf
    log_config_file = os.path.abspath("./conf/logger.conf")
    if os.path.exists(log_config_file):
        logging.config.fileConfig(fname=log_config_file)
    else:
        # 使用默认日志配置
        # 控制台输出 + 文件轮转日志
```

#### 1.3 命令行参数解析
```python
parser = argparse.ArgumentParser(description='PDS-V2 文献下载系统')
parser.add_argument('--siteId', type=int, help='站点ID')
parser.add_argument('--apiPath', type=str, help='API服务器地址')
parser.add_argument('--apiTimeout', type=int, help='API超时时间(秒)')
# ... 其他参数
args = parser.parse_args()
```

#### 1.4 核心组件初始化
```python
# 1. 更新站点配置
site_conf = util.update_site_config(args.__dict__)

# 2. 初始化API调用类
api_invoker = api.ApiInvoker(
    site_conf["apiPath"],
    site_conf["siteId"], 
    site_conf["apiToken"],
    encoding=site_conf["apiEncoding"],
    timeout=site_conf["apiTimeout"]
)

# 3. 初始化SQLite数据库
__db_file = db.DbFile()
```

### 2. 任务处理器选择

系统根据站点类型自动选择合适的任务处理器：

```python
# 获取站点信息判断站点类型
site_info = api_invoker.site_info()
site_type = site_info.get("siteType", "1")

if site_type in ['2', '3']:
    # 期刊类型站点，使用多线程处理器
    task_processor = multi_thread_task.MultiThreadTaskProcessor(
        site_id, __db_file, api_invoker
    )
else:
    # 其他类型站点，使用单线程处理器
    task_processor = task.TaskProcessor(site_id, __db_file, api_invoker)
```

### 3. 线程启动

```python
# 启动任务处理线程
task_processor.start()

# 启动上传结果线程
upload_processor = upload.Uploader(site_conf, __db_file, api_invoker)
upload_processor.start()

# 启动握手线程
handshake = handshake.Handshake(site_id, api_invoker, interval)
handshake.start()

# 主线程等待任务处理线程结束
task_processor.join()
```

## 脚本执行机制说明

### 脚本类型和优先级

#### 1. 任务级脚本（期刊专用脚本）
- **优先级**: 最高
- **命名规则**: `jur_{journalId}.pyscript`
- **特点**: 针对特定期刊定制的专用脚本
- **执行策略**: 如果存在，只执行这一个脚本

#### 2. 站点级脚本（通用脚本）
- **优先级**: 次级
- **命名规则**: `sit_{scriptMD5}.pyscript`
- **特点**: 站点配置的通用脚本，可能有多个
- **执行策略**: 按配置顺序依次执行，直到成功

### 脚本获取和执行流程

```python
def iter_script_modules(self, task):
    # 1. 优先使用任务指定的脚本（期刊专用脚本）
    script = self.__get_task_script_module(task)
    if script is not None:
        yield script  # 只返回一个脚本
        return

    # 2. 使用站点默认脚本（可能有多个）
    yield from self.__get_site_script_module()
```

### 多脚本容错执行机制

当使用站点级脚本时，系统会依次执行多个脚本，直到有一个成功：

```python
script_modules = self._script_context.iter_script_modules(task)
for name, md5, module in script_modules:
    # 1. 执行脚本
    self.run_script(name=name, module=module, task=task)
    
    # 2. 验证执行结果
    has_result = util.check_result_file(result_dir_path)
    if has_result:
        logger.info("任务下载成功，等待上传")
        return True  # 成功则停止，不再尝试其他脚本
    else:
        logger.error("脚本执行失败，尝试下一个脚本")
        util.remove_result_dir(task["resultDirPath"])  # 清理失败结果
        continue  # 继续尝试下一个脚本
```

### 脚本下载和更新机制

```python
def __refresh_script_module_from_remote(self, script_path, script_md5, module_name, script_id_str):
    # 检查本地脚本是否需要更新
    module_info = self.__script_module_context.get(module_name)
    if module_info is not None and module_info[0] == script_md5:
        return module_name, module_info[0], module_info[1]
    
    # 需要更新：下载新脚本
    self.__remove_script_module(module_name)
    script_file = self.__download_script_file(script_path, script_md5, script_id_str)
    
    # 重命名为.py文件并加载
    script_file_path = script_file.replace(".pyscript", ".py")
    os.rename(script_file, script_file_path)
    
    # 加载模块并验证MD5
    md5, module = self.__init_script_module(script_file_path)
    self.__script_module_context[module_name] = (md5, module)
    
    return module_name, md5, module
```

## 期刊节点任务多线程执行机制

### 多线程处理器特性

当站点类型(`currSiteType`)为'2'或'3'时，系统启用多线程任务处理器：

#### 1. 智能线程管理
- **最大并发数**: 最多5个工作线程同时运行
- **脚本ID隔离**: 同一脚本ID同时只能有一个线程处理，避免IP封禁
- **动态调度**: 任务完成后自动处理队列中等待的任务
- **后台优化**: 向后台发送当前活跃脚本ID列表，后台优先返回不同脚本ID的任务

#### 2. 任务分发策略

```python
def __submit_task_to_thread_pool(self, task_info):
    script_id = task_info.get("scriptIdStr", "unknown")
    
    with self.__active_script_ids_lock:
        # 检查该脚本ID是否已经在处理中
        if script_id in self.__active_script_ids:
            # 将任务放入队列等待
            self.__put_task_to_queue(task_info)
            return
        
        # 检查是否达到最大并发数
        if len(self.__active_script_ids) >= self.__max_workers:
            # 将任务放入队列等待
            self.__put_task_to_queue(task_info)
            return
        
        # 标记脚本ID为处理中并提交到线程池
        self.__active_script_ids.add(script_id)
    
    future = self.__executor.submit(self.__execute_task_multi_thread, task_info)
    future.add_done_callback(lambda f: self.__task_completed(script_id, f))
```

## 队列保护机制说明

### 队列保护策略

为防止任务丢失，系统实现了完善的队列保护机制：

#### 1. 队列容量控制
```python
# 任务队列最大容量为50
self.__task_queue = queue.Queue(maxsize=50)

# 队列使用率超过80%时暂停获取新任务
if self.__task_queue.qsize() >= self.__task_queue.maxsize * 0.8:
    logger.warning("任务队列使用率过高，暂停获取新任务")
    self.safe_sleep(10)
    continue
```

#### 2. 阻塞式任务入队
```python
def __put_task_to_queue(self, task_info):
    """将任务放入队列，使用阻塞方式确保任务不丢失"""
    while True:
        try:
            # 使用较短的超时时间，避免长时间阻塞
            self.__task_queue.put(task_info, timeout=5)
            break
        except queue.Full:
            if self.__stop:
                break
            logger.warning("任务队列已满，等待5秒后重试")
            self.safe_sleep(5)
```

#### 3. 智能任务调度
```python
def __process_queued_tasks(self):
    """处理队列中等待的任务"""
    while not self.__task_queue.empty():
        task_info = self.__task_queue.get_nowait()
        script_id = task_info.get("scriptIdStr", "unknown")
        
        with self.__active_script_ids_lock:
            if (script_id not in self.__active_script_ids and
                    len(self.__active_script_ids) < self.__max_workers):
                # 可以处理这个任务
                self.__active_script_ids.add(script_id)
                future = self.__executor.submit(self.__execute_task_multi_thread, task_info)
                future.add_done_callback(lambda f: self.__task_completed(script_id, f))
            else:
                # 还不能处理，放回队列头部（优先处理）
                self.__put_task_to_queue_front(task_info)
                break
```

### 后台任务优化

系统向后台发送当前活跃脚本ID列表，帮助后台优先返回不同脚本ID的任务：

```python
# 获取当前活跃的脚本ID列表
with self.__active_script_ids_lock:
    active_script_ids = list(self.__active_script_ids)

# 获取下一个任务，传递活跃脚本ID列表
task_info = self._api_invoker.next_task_info(active_script_ids)
```

API模块中的实现：
```python
def next_task_info(self, active_script_ids=None):
    params = {
        "siteId": self.__site_id,
        "apiToken": self.__api_token
    }
    
    # 如果提供了活跃脚本ID列表，添加到参数中
    if active_script_ids and isinstance(active_script_ids, list):
        params["activeScriptIds"] = ",".join(str(script_id) for script_id in active_script_ids)
    
    return self.__get(url, params=params)
```

## 任务处理流程详解

### 单线程任务处理 (task.py)

适用于非期刊类型站点(`currSiteType`不为'2'或'3')：

```python
def run(self):
    while True:
        if self.__stop:
            return

        # 获取下一个任务
        task_info = self._api_invoker.next_task_info()

        # 检查任务状态
        if task_info.get("status") == "none":
            # 没有任务，休眠30秒
            self.safe_sleep(30)
            continue

        if task_info.get("status") != "success":
            # 获取任务失败
            continue

        # 验证任务信息
        is_valid, error_msg = self.validate_task_info(task_info)
        if not is_valid:
            logger.warning(f"任务信息无效: {error_msg}")
            continue

        # 执行任务
        try:
            result = self.execute_task_core(task_info)
            self.__task_end(task_info)  # 任务结束休眠
        except Exception as e:
            self.handle_task_error(task_info, e, "单线程")
```

### 多线程任务处理 (multi_thread_task.py)

适用于期刊类型站点，支持最多5个不同脚本ID的并发处理：

```python
def run(self):
    """主线程：负责获取任务并分发到线程池"""
    while True:
        if self.__stop:
            self.__executor.shutdown(wait=True)
            return

        # 检查任务队列是否已满
        if self.__task_queue.qsize() >= self.__task_queue.maxsize * 0.8:
            logger.warning("任务队列使用率过高，暂停获取新任务")
            self.safe_sleep(10)
            continue

        # 获取当前活跃的脚本ID列表
        with self.__active_script_ids_lock:
            active_script_ids = list(self.__active_script_ids)

        # 获取下一个任务
        task_info = self._api_invoker.next_task_info(active_script_ids)

        # 处理任务状态
        task_status = task_info.get("status", "")
        if task_status == "none":
            self.safe_sleep(30)
            continue
        if task_status != "success":
            continue

        # 验证任务信息
        is_valid, error_msg = self.validate_task_info(task_info)
        if not is_valid:
            continue

        # 根据站点类型选择处理方式
        curr_site_type = task_info.get("currSiteType", "")
        if curr_site_type in ['2', '3']:
            # 期刊类型，使用多线程处理
            self.__submit_task_to_thread_pool(task_info)
        else:
            # 非期刊类型，使用单线程处理
            self.__execute_task_single_thread(task_info)
```

### 任务执行核心逻辑 (task_executor.py)

所有任务处理器共享的核心执行逻辑：

```python
def execute_task_core(self, task) -> bool:
    """任务执行的核心逻辑"""
    task_id = task.get('taskId', 'Unknown')
    doc_id = task.get('docId', 'Unknown')
    pmid = task.get('pmid', '')

    # 创建结果文件夹路径
    result_dir_path = self.create_result_dir(task)
    task["resultDirPath"] = result_dir_path

    # 获取脚本模块，按顺序执行直到成功或遍历完成
    try:
        script_modules = self._script_context.iter_script_modules(task)
        for name, md5, module in script_modules:
            # 1. 执行脚本
            self.run_script(name=name, module=module, task=task)

            # 2. 验证执行结果
            if not util.dict_has_value(task, "resultDirPath"):
                logger.error(f"脚本执行失败 {name}，没有获取到结果")
                continue

            # 3. 验证本地文件是否存在
            has_result = util.check_result_file(result_dir_path=task["resultDirPath"])
            if has_result:
                logger.info(f"任务下载成功，等待上传 {name}")
                return True  # 成功
            else:
                logger.error(f"脚本执行失败 {name}，没有获取到结果")
                util.remove_result_dir(task["resultDirPath"])

        return False  # 所有脚本都失败

    finally:
        # 无论成功或失败，都要保存任务信息到数据库
        self._db.insert_item(task_info=task)
```

## 文件上传流程 (upload.py)

### 上传线程工作流程

```python
def run(self):
    for result_info in self.__get_next_result_item():
        try:
            if self.__stop:
                return
            if result_info is None:
                continue

            # 上传文件
            notice = self.__upload_to_server(result_info)

            # 更新服务器状态
            self.__notice_server(result_info, notice)

        except Exception as e:
            logger.exception(e)
```

### 文件上传处理

```python
def __upload_to_server(self, result_info):
    """上传结果文件到服务器"""
    result_dir_path = result_info["resultDirPath"]
    task_id = result_info["taskId"]

    # 检查结果文件是否存在
    has_result = util.check_result_file(result_dir_path)
    if not has_result:
        self.__remove_result(result_dir_path, result_info["id"])
        return {"status": "failed", "msg": "本地结果文件不存在"}

    # 压缩结果文件夹
    shutil.make_archive(base_name=result_dir_path, format="zip",
                       root_dir=os.path.dirname(result_dir_path),
                       base_dir=os.path.basename(result_dir_path))
    zip_file_path = f"{result_dir_path}.zip"

    # 生成MD5文件
    md5 = util.md5_file(zip_file_path)
    md5_file_path = f"{result_dir_path}.md5"
    with open(md5_file_path, "w+", encoding="UTF-8") as f:
        f.write(md5)

    # 上传到FTP服务器
    try:
        ftp_client = ftp.FtpClient(self.__site_conf)
        ftp_client.upload_file(zip_file_path)
        ftp_client.upload_file(md5_file_path)
        ftp_client.close()

        return {"status": "success", "msg": "上传成功"}
    except Exception as e:
        logger.exception(f"FTP上传失败: {e}")
        return {"status": "failed", "msg": str(e)}
```

### 清理策略

```python
def __notice_server(self, result_info, notice):
    """通知服务器更新任务状态"""
    try:
        # 调用API更新任务状态
        api_result = self._api_invoker.update_task_info(notice)

        # 上传成功则删除本地文件
        if notice["status"] == "success" and api_result.get("status") == "success":
            self.__remove_result(result_dir_path, result_info["id"])
            return

        # 清理策略：超过30天且上传10次以上，或磁盘使用率>90%
        day_num = (datetime.now() - datetime.strptime(
            result_info["create_time"], "%Y-%m-%d %H:%M:%S"
        )).days + 1

        system_info = util.system_info()
        if ((result_info["upload_num"] > 10 and day_num > 30) or
            system_info["disk"]["percent"] > 90):
            self.__remove_result(result_dir_path, result_info["id"])

    except Exception as e:
        logger.exception(e)
    finally:
        # 增加上传尝试次数
        self._db.inc_upload_num(result_info["id"])
```

## 握手通信流程 (handshake.py)

### 握手线程功能

握手线程作为守护线程运行，定期向服务器发送系统状态信息：

```python
class Handshake(threading.Thread):
    def __init__(self, site_id, api_invoker, interval=60):
        super().__init__(daemon=True)
        self.__site_id = site_id
        self.__api_invoker = api_invoker
        self.__interval = interval

        # 启动时立即发送一次握手信息
        sys_info = util.system_info()
        print(f"\n本机信息：\n{json.dumps(sys_info, ensure_ascii=False, sort_keys=True, indent='\t')}\n")
        self.__api_invoker.send_handshake(sys_info)
        self.__stop = False

    def run(self):
        while True:
            if self.__stop:
                return
            try:
                time.sleep(self.__interval)
                # 定期发送系统信息
                self.__api_invoker.send_handshake(util.system_info())
            except Exception as e:
                logger.info(f"发送心跳失败: {e}")
```

### 系统信息收集

```python
def system_info():
    """获取系统CPU、内存、磁盘信息"""
    return {
        "cpu": cpu_info(),
        "memory": memory_info(),
        "disk": disk_info()
    }

def cpu_info():
    """获取CPU信息"""
    try:
        cpu_freq = psutil.cpu_freq()
        return {
            "logical_count": psutil.cpu_count(),
            "physical_count": psutil.cpu_count(logical=False),
            "freq_current": cpu_freq.current,
            "freq_min": cpu_freq.min,
            "freq_max": cpu_freq.max
        }
    except Exception as e:
        logger.exception(e)
        return {"logical_count": -1, "physical_count": -1, "freq_current": -1}

def memory_info():
    """获取内存信息"""
    try:
        mem = psutil.virtual_memory()
        return {
            "total": mem.total,
            "available": mem.available,
            "used": mem.used,
            "free": mem.free,
            "percent": mem.percent
        }
    except Exception as e:
        logger.exception(e)
        return {"total": -1, "available": -1, "used": -1, "free": -1, "percent": -1}

def disk_info():
    """获取磁盘信息"""
    try:
        disk_tmp = psutil.disk_usage(program_root_dir())
        return {
            "total": disk_tmp.total,
            "used": disk_tmp.used,
            "free": disk_tmp.free,
            "percent": disk_tmp.percent
        }
    except Exception as e:
        logger.exception(e)
        return {"total": -1, "used": -1, "free": -1, "percent": -1}
```

## 数据库管理 (db.py)

### SQLite数据库结构

```sql
CREATE TABLE IF NOT EXISTS result_file (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  task_info TEXT NOT NULL,
  create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
  update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
  upload_num INTEGER DEFAULT 0
);

-- 创建触发器自动更新 update_time
CREATE TRIGGER IF NOT EXISTS UpdateLastTime AFTER UPDATE OF task_info, upload_num ON result_file
BEGIN
  UPDATE result_file SET update_time=CURRENT_TIMESTAMP WHERE new.id=old.id;
END;

-- 索引
CREATE INDEX IF NOT EXISTS idx_create_time ON result_file(create_time);
CREATE INDEX IF NOT EXISTS idx_update_time ON result_file(update_time);
```

### 数据库操作

```python
class DbFile:
    def __init__(self, timeout=30):
        self.__timeout = timeout
        self.__lock = threading.Lock()
        self.__conn = self.__init_conn()
        self.__init_table()

    def insert_item(self, task_info: dict):
        """插入任务结果信息"""
        sql = "INSERT INTO result_file (task_info) VALUES (?)"

        self.__lock.acquire(blocking=True, timeout=-1)
        cursor = None
        try:
            cursor = self.__conn.cursor()
            cursor.execute(sql, (json.dumps(task_info, ensure_ascii=False),))
            self.__conn.commit()
        except Exception as e:
            self.__conn.rollback()
            raise ValueError(f"插入数据失败: {e}")
        finally:
            self.__cursor_close(cursor)
            self.__lock.release()

    def get_next_item(self, start_id=0) -> dict:
        """获取下一个待上传的结果"""
        sql = "SELECT * FROM result_file WHERE id > ? ORDER BY id ASC LIMIT 1"

        self.__lock.acquire(blocking=True, timeout=-1)
        cursor = None
        try:
            cursor = self.__conn.cursor()
            cursor.execute(sql, (start_id,))
            row = cursor.fetchone()
            return dict(zip([x[0] for x in cursor.description], row)) if row else None
        except Exception as e:
            raise ValueError(f"查询数据失败: {e}")
        finally:
            self.__cursor_close(cursor)
            self.__lock.release()

    def inc_upload_num(self, result_id: int):
        """增加上传尝试次数"""
        sql = "UPDATE result_file SET upload_num = upload_num + 1 WHERE id = ?"

        self.__lock.acquire(blocking=True, timeout=-1)
        cursor = None
        try:
            cursor = self.__conn.cursor()
            cursor.execute(sql, (result_id,))
            self.__conn.commit()
        except Exception as e:
            self.__conn.rollback()
            raise ValueError(f"更新上传次数失败: {e}")
        finally:
            self.__cursor_close(cursor)
            self.__lock.release()
```

## API通信模块 (api.py)

### API调用类

```python
class ApiInvoker:
    def __init__(self, api_url: str, site_id: int, api_token: str, encoding="UTF-8", timeout=30):
        self.__api_url = api_url
        self.__site_id = site_id
        self.__api_token = api_token
        self.__encoding = encoding
        self.__timeout = timeout
        self.__req = requests.session()
        self.__req.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }
```

### 核心API方法

```python
def site_info(self) -> dict:
    """获取站点信息"""
    return self.__get(f"{self.__api_url}/getSiteBaseinfo",
                      params={"siteId": self.__site_id, "apiToken": self.__api_token})

def next_task_info(self, active_script_ids=None) -> dict:
    """获取下一个任务"""
    params = {
        "siteId": self.__site_id,
        "apiToken": self.__api_token
    }

    # 如果提供了活跃脚本ID列表，添加到参数中
    if active_script_ids and isinstance(active_script_ids, list):
        params["activeScriptIds"] = ",".join(str(script_id) for script_id in active_script_ids)

    return self.__get(f"{self.__api_url}/getNextTaskInfo", params=params)

def send_handshake(self, sys_info: dict) -> dict:
    """发送握手信息"""
    return self.__get(f"{self.__api_url}/sendHandshake",
                      params={"siteId": self.__site_id, "apiToken": self.__api_token,
                              "sysInfo": json.dumps(sys_info, ensure_ascii=False)})

def send_task_message(self, task_id: str, msg: str) -> dict:
    """发送任务日志消息"""
    try:
        return self.__get(f"{self.__api_url}/sendTaskMessage",
                          params={"siteId": self.__site_id, "taskId": task_id,
                                  "msg": msg, "apiToken": self.__api_token})
    except Exception as e:
        logger.exception(f"发送消息失败: {e}")

def get_task_script_file(self, script_id_str: str, to_file_path: str):
    """获取任务脚本文件"""
    response = self.__req.get(f"{self.__api_url}/getTaskScriptFile",
                              params={"siteId": self.__site_id, "scriptIdStr": script_id_str,
                                      "apiToken": self.__api_token},
                              stream=True, timeout=self.__timeout)

    if response.status_code != 200:
        raise ValueError(f"下载脚本失败，状态码: {response.status_code}")

    with open(to_file_path, "wb") as f:
        for chunk in response.iter_content(chunk_size=8192):
            f.write(chunk)
```

## 工具函数模块 (util.py)

### 配置管理

```python
def get_site_config() -> dict:
    """解析站点配置文件"""
    file = os.path.join(program_root_dir(), "conf/site.json")
    if not os.path.exists(file):
        raise ValueError(f"站点配置文件不存在: {file}")

    with open(file, "r", encoding="UTF-8") as f:
        site_config = json.loads(f.read())

    # 验证必要配置
    if not dict_has_value(site_config, "siteId"):
        raise ValueError("站点配置中未配置 siteId")
    if not dict_has_value(site_config, "apiPath"):
        raise ValueError("站点配置中未配置 apiPath")

    # 设置默认值
    site_config.setdefault("apiTimeout", 30)
    site_config.setdefault("apiEncoding", "UTF-8")
    site_config.setdefault("handshakeIntervalTime", 60)
    site_config.setdefault("ftpTimeout", 60)
    site_config.setdefault("ftpPasv", True)

    return site_config

def update_site_config(param: dict) -> dict:
    """更新站点配置"""
    config = get_site_config()
    if param is None or len(param) == 0:
        return config

    config_file = os.path.join(program_root_dir(), "conf/site.json")
    with open(config_file, 'w', encoding='utf-8') as file:
        for k, v in param.items():
            if k not in config.keys() or v is None:
                continue
            if isinstance(v, str) and str(v).strip() == "":
                continue
            config[k] = v

        file.write(json.dumps(config, ensure_ascii=False, indent=2))
        return config
```

### 文件操作

```python
def check_result_file(result_dir_path: str) -> bool:
    """验证文件是否已执行完成"""
    if result_dir_path is None or not os.path.exists(result_dir_path):
        return False

    # 检查定义文件是否存在且不为空
    define_file_path = os.path.join(result_dir_path, "attach_note.txt")
    if not os.path.exists(define_file_path) or os.path.getsize(define_file_path) == 0:
        return False

    # 检查结果文件夹中是否有足够的文件
    files = os.listdir(result_dir_path)
    if len(files) < 2:
        return False

    return True

def remove_result_dir(result_dir_path: str):
    """清理结果文件和相关文件"""
    try:
        if result_dir_path and os.path.exists(result_dir_path):
            shutil.rmtree(result_dir_path, ignore_errors=True)

        # 清理压缩文件和MD5文件
        for ext in [".zip", ".md5"]:
            file_path = f"{result_dir_path}{ext}"
            if os.path.exists(file_path):
                os.remove(file_path)
    except Exception as e:
        logger.exception(e)

def md5_file(file_path: str) -> str:
    """计算文件的MD5值"""
    if not os.path.exists(file_path):
        raise FileNotFoundError(file_path)

    m = md5()
    with open(file_path, "rb") as fh:
        chunk = fh.read(8096)
        while chunk:
            m.update(chunk)
            chunk = fh.read(8096)
    return m.hexdigest()

def dict_has_value(d: dict, key: str) -> bool:
    """检查字典中是否有该key值且不为空"""
    if d is None or key is None or key not in d.keys():
        return False
    if d[key] is None:
        return False
    if isinstance(d[key], str) and str(d[key]).strip() == "":
        return False
    return True
```

## 系统特性和优化

### 1. 容错机制

#### 多脚本容错
- 系统支持多个脚本依次执行，直到成功为止
- 脚本执行失败时自动清理结果目录，尝试下一个脚本
- 所有脚本都失败时，任务标记为失败但仍保存到数据库

#### 网络容错
- API调用失败时自动重试
- 脚本下载失败时抛出异常，不影响其他脚本
- 上传失败时记录重试次数，支持多次重试

#### 资源容错
- 磁盘空间不足时自动清理过期文件
- 内存不足时优雅降级处理
- 线程异常时不影响其他线程运行

### 2. 性能优化

#### 多线程优化
- 期刊类型站点支持最多5个并发线程
- 同一脚本ID只允许一个线程处理，避免IP封禁
- 任务队列缓冲机制，减少API调用频率

#### 内存优化
- 脚本模块动态加载和卸载
- 结果文件及时清理
- 数据库连接池管理

#### 网络优化
- HTTP连接复用
- 文件分块下载和上传
- 压缩传输减少带宽占用

### 3. 监控和日志

#### 日志系统
- 支持配置文件和默认配置两种方式
- 控制台输出 + 文件轮转日志
- 不同级别的日志记录（DEBUG、INFO、WARNING、ERROR）

#### 系统监控
- 定期发送系统状态信息（CPU、内存、磁盘）
- 任务执行统计（总数、成功数、失败数）
- 队列状态监控（队列长度、使用率）

#### 错误处理
- 异常信息详细记录
- 任务错误信息发送到服务器
- 关键错误不影响系统继续运行

### 4. 配置管理

#### 站点配置 (conf/site.json)
```json
{
  "siteId": 1,
  "apiPath": "http://api.example.com",
  "apiToken": "your_api_token",
  "apiTimeout": 30,
  "apiEncoding": "UTF-8",
  "handshakeIntervalTime": 60,
  "ftpPath": "ftp.example.com",
  "ftpPort": 21,
  "ftpUsername": "username",
  "ftpPassword": "password",
  "ftpTimeout": 60,
  "ftpPasv": true
}
```

#### 日志配置 (conf/logger.conf)
```ini
[loggers]
keys=root

[handlers]
keys=consoleHandler,fileHandler

[formatters]
keys=simpleFormatter

[logger_root]
level=DEBUG
handlers=consoleHandler,fileHandler

[handler_consoleHandler]
class=StreamHandler
level=DEBUG
formatter=simpleFormatter
args=(sys.stdout,)

[handler_fileHandler]
class=handlers.RotatingFileHandler
level=WARNING
formatter=simpleFormatter
args=('./log/ads.log', 'a', 30*1024*1024, 10, 'utf8')

[formatter_simpleFormatter]
format=%(asctime)s - %(name)s - %(levelname)s - %(module)s : %(lineno)d - %(message)s
datefmt=%Y-%m-%d %H:%M:%S
```

## 部署和运行

### 1. 环境要求
- Python 3.6 及以上版本
- 依赖包：requests, psutil, retry

### 2. 启动命令
```bash
# 基本启动
python start.py

# 指定参数启动
python start.py --siteId 1 --apiPath "http://api.example.com" --apiTimeout 60

# 后台运行
nohup python start.py > output.log 2>&1 &
```

### 3. 目录结构
```
pds-script/ads-node/
├── conf/                 # 配置文件目录
├── log/                  # 日志文件目录
├── db/                   # 数据库文件目录
├── script/               # 动态脚本目录
├── data/                 # 结果文件目录
└── *.py                  # 程序文件
```

### 4. 监控指标
- 任务处理速度
- 成功率统计
- 系统资源使用率
- 队列状态
- 错误日志

## 总结

PDS-V2 系统是一个功能完善、架构合理的文献下载系统，具有以下特点：

1. **高可靠性**: 多层容错机制，确保系统稳定运行
2. **高性能**: 多线程并发处理，提高下载效率
3. **高可扩展性**: 模块化设计，易于扩展和维护
4. **智能调度**: 队列保护机制，防止任务丢失
5. **完善监控**: 详细的日志记录和系统监控

系统设计合理，代码结构清晰，适合长期稳定运行的生产环境。通过合理的配置和监控，可以实现高效的文献下载服务。
