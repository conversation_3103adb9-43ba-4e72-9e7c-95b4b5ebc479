package org.biosino.lf.pds.web.controller.metadata;

import lombok.RequiredArgsConstructor;
import org.biosino.lf.pds.article.domain.Publisher;
import org.biosino.lf.pds.article.dto.PublisherMergeDTO;
import org.biosino.lf.pds.article.dto.PublisherQueryDTO;
import org.biosino.lf.pds.article.service.IPublisherService;
import org.biosino.lf.pds.common.core.controller.BaseController;
import org.biosino.lf.pds.common.core.domain.AjaxResult;
import org.biosino.lf.pds.common.core.page.TableDataInfo;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 出版社管理控制器
 */
@RestController
@RequestMapping("/publisher")
@RequiredArgsConstructor
public class PublisherController extends BaseController {

    private final IPublisherService publisherService;

    /**
     * 查询出版社列表
     */
    @GetMapping("/list")
    public TableDataInfo list(PublisherQueryDTO queryDTO) {
        startPage();
        List<Publisher> list = publisherService.selectPublisherList(queryDTO);
        return getDataTable(list);
    }

    /**
     * 根据出版社编号获取详细信息
     */
    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable Long id) {
        Publisher publisher = publisherService.selectPublisherById(id);
        return success(publisher);
    }

    /**
     * 修改出版社
     */

    @PutMapping
    @PreAuthorize("@ss.hasPermi('publishers:edit')")
    public AjaxResult edit(@RequestBody Publisher publisher) {
        publisherService.updatePublisher(publisher);
        return AjaxResult.success();
    }


    /**
     * 合并出版社
     */
    @PostMapping("/merge")
    @PreAuthorize("@ss.hasPermi('publishers:merge')")
    public AjaxResult merge(@RequestBody @Validated PublisherMergeDTO mergeDTO) {
        publisherService.mergePublishers(mergeDTO);
        return AjaxResult.success();
    }

    /**
     * 修改出版社状态
     */

    @PostMapping("/changeStatus")
    @PreAuthorize("@ss.hasPermi('publishers:status')")
    public AjaxResult changeStatus(@RequestParam Long id, @RequestParam Integer status) {
        publisherService.updatePublisherStatus(id, status);
        return AjaxResult.success();
    }
}
