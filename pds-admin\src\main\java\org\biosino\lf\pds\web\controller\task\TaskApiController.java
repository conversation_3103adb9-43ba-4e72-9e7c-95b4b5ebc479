package org.biosino.lf.pds.web.controller.task;

import com.alibaba.fastjson2.JSONObject;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.pds.article.custbean.dto.api.HandshakeDTO;
import org.biosino.lf.pds.article.custbean.dto.api.NoticeApiDTO;
import org.biosino.lf.pds.article.custbean.vo.api.ApiResultVO;
import org.biosino.lf.pds.article.custbean.vo.api.SiteInfoVO;
import org.biosino.lf.pds.article.domain.TbDdsSite;
import org.biosino.lf.pds.common.core.controller.BaseController;
import org.biosino.lf.pds.common.core.domain.AjaxResult;
import org.biosino.lf.pds.task.service.ITaskApiService;
import org.biosino.lf.pds.task.service.ITbDdsSiteService;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 任务对外api接口（小蚂蚁请求接口控制层）
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/pds-api/pub-lds")
public class TaskApiController extends BaseController {
    private final ITaskApiService taskApiService;
    private final ITbDdsSiteService tbDdsSiteService;

    /**
     * 验证API令牌
     *
     * @param siteId   站点ID
     * @param apiToken API令牌
     * @return 站点对象，如果验证失败则返回null
     */
    private TbDdsSite validateApiToken(String siteId, String apiToken) {
        try {
            if (siteId == null || apiToken == null) {
                return null;
            }

            TbDdsSite site = tbDdsSiteService.findByIdWithCache(Integer.parseInt(siteId));

            if (site == null) {
                return null;
            }

            final String configApiToken = site.getApiToken();
            if (configApiToken == null || !configApiToken.equals(apiToken)) {
                log.warn("API令牌验证失败，站点ID: {}", siteId);
                return null;
            }

            return site;
        } catch (Exception e) {
            log.error("验证API令牌时出错", e);
            return null;
        }
    }

    /**
     * 发送握手信息
     */
    @GetMapping("/sendHandshake")
    public ApiResultVO sendHandshake(final HandshakeDTO dto, final String apiToken, final HttpServletRequest request) {
        TbDdsSite site = validateApiToken(dto.getSiteId(), apiToken);
        if (site == null) {
            ApiResultVO resultVO = new ApiResultVO();
            resultVO.setStatus("error");
            resultVO.setMsg("API令牌验证失败");
            return resultVO;
        }

        return taskApiService.sendHandshake(dto, request);
    }

    /**
     * 拉取下一个任务
     */
    @GetMapping("/getNextTaskInfo")
    public JSONObject getNextTaskInfo(@RequestParam(required = true) final String siteId,
                                      @RequestParam(required = true) final String apiToken,
                                      @RequestParam(required = false) String startPmid,
                                      @RequestParam(required = false) String endPmid,
                                      @RequestParam(required = false) String activeScriptIds) {
        TbDdsSite site = validateApiToken(siteId, apiToken);
        if (site == null) {
            JSONObject result = new JSONObject();
            result.put("status", "error");
            result.put("msg", "API令牌验证失败");
            return result;
        }

        return taskApiService.getNextTaskInfo(siteId, startPmid, endPmid, activeScriptIds);
    }


    /**
     * 小蚂蚁下载脚本文件
     */
    @GetMapping("/getTaskScriptFile")
    public void getTaskScriptFile(String siteId, String scriptIdStr, String apiToken, HttpServletResponse response) {
        TbDdsSite site = validateApiToken(siteId, apiToken);
        if (site == null) {
            try {
                response.setStatus(403);
                response.getWriter().write("API令牌验证失败");
                return;
            } catch (Exception e) {
                log.error("响应错误信息失败", e);
                return;
            }
        }

        taskApiService.getTaskScriptFile(scriptIdStr, response);
    }


    @GetMapping("/getSiteBaseinfo")
    public SiteInfoVO getSiteBaseinfo(String siteId, String apiToken) {
        TbDdsSite site = validateApiToken(siteId, apiToken);
        if (site == null) {
            SiteInfoVO resultVO = new SiteInfoVO();
            resultVO.setStatus(-1);
            resultVO.setMsg("API令牌验证失败");
            return resultVO;
        }

        return taskApiService.getSiteBaseinfo(siteId);
    }

    /**
     * 记录日志
     */
    @GetMapping("/sendTaskMessage")
    public AjaxResult sendTaskMessage(String siteId, String taskId, String msg, String apiToken) {
        TbDdsSite site = validateApiToken(siteId, apiToken);
        if (site == null) {
            return AjaxResult.error("API令牌验证失败");
        }

        taskApiService.sendTaskMessage(taskId, msg);
        return AjaxResult.success();
    }

    /**
     * HTTP文件上传接口
     *
     * @param siteId     站点ID
     * @param taskId     任务ID
     * @param resultFile 结果文件（ZIP格式）
     * @param md5File    MD5文件
     * @param apiToken   API令牌，用于验证请求合法性
     * @param docId      文档ID（可选）
     * @return 上传结果
     */
    @PostMapping("/uploadTaskResult")
    public AjaxResult uploadTaskResult(@RequestParam("siteId") String siteId,
                                       @RequestParam("taskId") String taskId,
                                       @RequestParam("resultFile") MultipartFile resultFile,
                                       @RequestParam("md5File") MultipartFile md5File,
                                       @RequestParam("apiToken") String apiToken,
                                       @RequestParam(value = "docId") Long docId) {
        try {
            // 验证apiToken
            TbDdsSite site = validateApiToken(siteId, apiToken);
            if (site == null) {
                return AjaxResult.error("API令牌验证失败");
            }

            return taskApiService.uploadTaskResult(siteId, taskId, resultFile, md5File, docId);
        } catch (Exception e) {
            log.error("文件上传失败", e);
            return AjaxResult.error("文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 通知更新任务状态
     *
     * @param jsonStr 包含任务信息的JSON字符串
     * @return 处理结果
     */
    @PostMapping("/notice2UpdateTask")
    public ApiResultVO notice2UpdateTask(@RequestParam("jsonStr") String jsonStr) {
        final NoticeApiDTO taskInfo = JSONObject.parseObject(jsonStr, NoticeApiDTO.class);

        // 从任务信息中获取站点ID和API令牌
        final String siteId = taskInfo.getSiteId();
        final String apiToken = taskInfo.getApiToken();

        // 验证API令牌
        final TbDdsSite site = validateApiToken(siteId, apiToken);
        if (site == null) {
            return ApiResultVO.error("API令牌验证失败");
        }

        return taskApiService.notice2UpdateTask(site.getId(), taskInfo);
    }
}
