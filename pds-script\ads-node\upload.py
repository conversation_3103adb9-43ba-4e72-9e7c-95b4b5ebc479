# coding=utf8
__author__ = 'yhju'

import json
import logging
import os
import shutil
import threading
import time
from datetime import datetime

import requests
from requests_toolbelt import MultipartEncoder

import api
import db
import util

logger = logging.getLogger()


class StreamingFileReader:
    """
    流式文件读取器，用于大文件上传时减少内存占用
    """

    def __init__(self, file_path, chunk_size=8192):
        """
        初始化流式文件读取器

        Args:
            file_path: 文件路径
            chunk_size: 每次读取的块大小，默认8KB
        """
        self.file_path = file_path
        self.chunk_size = chunk_size
        self._file = None
        self._size = None

    def __enter__(self):
        """上下文管理器入口"""
        self._file = open(self.file_path, 'rb')
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口，确保文件被关闭"""
        if self._file:
            self._file.close()

    def read(self, size=-1):
        """
        读取文件内容

        Args:
            size: 读取字节数，-1表示读取一个chunk

        Returns:
            读取的字节数据
        """
        if not self._file:
            raise ValueError("文件未打开")

        if size == -1:
            return self._file.read(self.chunk_size)
        return self._file.read(size)

    def __len__(self):
        """返回文件大小"""
        if self._size is None:
            if os.path.exists(self.file_path):
                self._size = os.path.getsize(self.file_path)
            else:
                self._size = 0
        return self._size

    def __iter__(self):
        """迭代器支持，用于分块读取"""
        while True:
            chunk = self.read(self.chunk_size)
            if not chunk:
                break
            yield chunk


class Uploader(threading.Thread):
    """
    上传传递结果到服务器，并更新任务状态
    """

    def __init__(self, site_conf: dict, db_file: db.DbFile, api_invoker: api.ApiInvoker):
        super(Uploader, self).__init__(daemon=True)
        self.__site_id = site_conf["siteId"]
        self.__db = db_file
        self.__api_invoker = api_invoker
        self.__api_path = site_conf["apiPath"]
        self.__api_timeout = site_conf["apiTimeout"]
        self.__api_token = site_conf["apiToken"]
        self.__stop = False

    def run(self) -> None:
        for result_info in self.__get_next_result_item():
            try:
                if self.__stop:
                    logging.warning("文献上传线程已停止")
                    return
                if result_info is None:
                    continue
                # 上传文件
                notice = self.__upload_to_server(result_info)
                # 更新服务器状态
                self.__notice_server(result_info, notice)
                time.sleep(1)
            except BaseException as e:
                logger.exception(e)

    def __notice_server(self, result_info: dict, notice: dict):
        """
        通知服务器修改状态
        :return:
        """

        result_id = result_info["id"]
        task_id = result_info["task_info"]["taskId"]
        pmid = result_info["task_info"].get("pmid", "")
        doc_id = result_info["task_info"]["docId"]
        result_dir_path = result_info["task_info"]["resultDirPath"]

        try:

            # 先修改服务器状态
            logger.info("Notice Info: {}".format(notice))
            api_result = self.__api_invoker.update_task_info(notice)
            api_result = self.__load_json_from_str(api_result)

            api_invoke_flag = True if util.dict_has_value(api_result, "status") and "success" == str(
                api_result["status"]).strip().lower() else False
            if not api_invoke_flag:
                if pmid:
                    logger.warning(
                        "调用通知任务结果状态的接口返回服务器错误，TaskId: {} docId: {} PMID: {} Msg： {}".format(task_id,
                                                                                                                doc_id,
                                                                                                                pmid,
                                                                                                                api_result))
                else:
                    logger.warning(
                        "调用通知任务结果状态的接口返回服务器错误，TaskId: {} docId: {} Msg： {}".format(task_id, doc_id,
                                                                                                       api_result))
            else:
                if pmid:
                    logger.info(
                        "已通知服务器下载结果状态 {} TaskId： {} docId: {} PMID: {}".format(notice["status"], task_id,
                                                                                           doc_id, pmid))
                else:
                    logger.info(
                        "已通知服务器下载结果状态 {} TaskId： {} docId: {}".format(notice["status"], task_id, doc_id))

            # 如果上传至服务器成功，直接删除本地文件
            if "success" == str(notice["status"]).lower().strip() and api_invoke_flag:
                self.__remove_result(result_dir_path, result_id)
                return

            # 如果上传服务器/或更新结果失败： 超过 30 天且累计上传过10次还没传上去 或 本地磁盘已经占用90%以上 直接删除掉
            day_num = (datetime.now() - datetime.strptime(result_info["create_time"], "%Y-%m-%d %H:%M:%S")).days + 1
            system_info = util.system_info()
            if (result_info["upload_num"] > 10 and day_num > 30) or system_info["disk"]["percent"] > 90:
                self.__remove_result(result_dir_path, result_id)

        except BaseException as e:
            logger.exception(e)
            if task_id is not None:
                self.__api_invoker.send_task_message(task_id,
                                                     "站点 {} 通知服务器上传结果失败\nException: {}".format(
                                                         self.__site_id, e))
        finally:
            try:
                # 累计次数
                self.__db.inc_upload_num(result_id)
            except BaseException as e:
                logger.exception(e)

    def __upload_to_server(self, result_info: dict):
        """
        使用HTTP上传文件到服务器
        """
        task_id = result_info["task_info"]["taskId"]
        pmid = result_info["task_info"].get("pmid", "")
        doc_id = result_info["task_info"]["docId"]
        result_dir_path = result_info["task_info"]["resultDirPath"]

        notice = {"siteId": self.__site_id, "taskId": task_id, "docId": doc_id, "uploadMd5Path": None,
                  "uploadPath": None,
                  "status": "failed"}

        # pmid是可选字段，只有存在时才添加
        if pmid:
            notice["pmid"] = pmid

        try:
            if pmid:
                logger.info("开始上传 TaskId: {} docId: {} PMID: {}".format(task_id, doc_id, pmid))
            else:
                logger.info("开始上传 TaskId: {} docId: {}".format(task_id, doc_id))

            # 检查结果文件是否存在
            has_result = util.check_result_file(result_dir_path)
            # 本地文件都没了，就直接报告下载失败
            if not has_result:
                self.__remove_result(result_dir_path, result_info["id"])
                return notice

            # 压缩结果文件夹
            shutil.make_archive(base_name=result_dir_path, format="zip", root_dir=os.path.dirname(result_dir_path),
                                base_dir=os.path.basename(result_dir_path))
            os.chdir(util.program_root_dir())
            zip_file_path = "{}.zip".format(result_dir_path)

            # 生成md5文件
            md5 = util.md5_file(zip_file_path)
            md5_file_path = "{}.md5".format(result_dir_path)
            with open(md5_file_path, "w+", encoding="UTF-8") as f:
                f.write(md5)

            # 使用HTTP上传文件
            upload_url = f"{self.__api_path}/uploadTaskResult"

            # 使用MultipartEncoder进行文件上传
            multipart_data = {
                'siteId': str(self.__site_id),
                'taskId': str(task_id),
                'pmid': str(pmid),
                'docId': str(doc_id),
                'apiToken': str(self.__api_token),
                'resultFile': (os.path.basename(zip_file_path), open(zip_file_path, 'rb'), 'application/zip'),
                'md5File': (os.path.basename(md5_file_path), open(md5_file_path, 'rb'), 'text/plain')
            }

            encoder = MultipartEncoder(fields=multipart_data)

            try:
                response = requests.post(
                    upload_url,
                    data=encoder,
                    headers={'Content-Type': encoder.content_type},
                    timeout=self.__api_timeout
                )

                if response.status_code == 200:
                    response_data = response.json()
                    if response_data.get('code') == 200:
                        if pmid:
                            logger.info(
                                "下载结果已成功上传至服务器，TaskId: {} docId: {} PMID: {}".format(task_id, doc_id,
                                                                                                  pmid))
                        else:
                            logger.info("下载结果已成功上传至服务器，TaskId: {} docId: {}".format(task_id, doc_id))
                        notice["status"] = "success"
                        data_result = response_data.get('data')
                        notice["uploadMd5Path"] = data_result.get('uploadMd5Path')
                        notice["uploadPath"] = data_result.get('uploadPath')
                    else:
                        if pmid:
                            logger.error(
                                "下载结果上传失败，TaskId: {} docId: {} PMID: {} 服务器返回：{}".format(task_id, doc_id,
                                                                                                      pmid,
                                                                                                      response_data.get(
                                                                                                          'msg')))
                        else:
                            logger.error("下载结果上传失败，TaskId: {} docId: {} 服务器返回：{}".format(task_id, doc_id,
                                                                                                      response_data.get(
                                                                                                          'msg')))
                else:
                    if pmid:
                        logger.error(
                            "上传失败，HTTP状态码: {} TaskId: {} docId: {} PMID: {}".format(response.status_code,
                                                                                           task_id, doc_id, pmid))
                    else:
                        logger.error(
                            "上传失败，HTTP状态码: {} TaskId: {} docId: {}".format(response.status_code, task_id,
                                                                                  doc_id))
            except Exception as e:
                if pmid:
                    logger.exception("HTTP上传异常: {} TaskId: {} docId: {} PMID: {}".format(e, task_id, doc_id, pmid))
                else:
                    logger.exception("HTTP上传异常: {} TaskId: {} docId: {}".format(e, task_id, doc_id))
            finally:
                # 关闭文件句柄
                try:
                    multipart_data['resultFile'][1].close()
                    multipart_data['md5File'][1].close()
                except:
                    pass

        except BaseException as e:
            self.__api_invoker.send_task_message(task_id,
                                                 "站点 {} 上传结果到服务器失败, TaskID: {} doc_id: {} PMID: {} \nException: {}".format(
                                                     self.__site_id, task_id, doc_id, pmid, e))
            logger.exception(e)
        return notice

    def __get_next_result_item(self) -> dict:
        """
        获取下一个要上传的文件
        :return:
        """
        item_id = 0
        while True:
            try:

                result_info = self.__db.get_next_item(item_id)

                if result_info is None:
                    item_id = 0  # 没有数据了下一次从 0 开始
                    __sleep_time = 60
                    logger.info("没有结果要上传至服务器了， 上传线程 sleep {} s".format(__sleep_time))
                    self.__sleep(__sleep_time)
                    continue

                # 查到值先把下一条起始 ID 重置
                item_id = result_info["id"]

                if not util.dict_has_value(result_info, "task_info"):
                    logger.warning("查询数据中记录的task信息为空，删除本记录 ID: {}".format(item_id))
                    self.__db.delete_item(item_id=item_id)
                    continue

                task_dict_info = self.__load_json_from_str(result_info["task_info"])
                if not task_dict_info or not util.dict_has_value(task_dict_info, "resultDirPath"):
                    logger.warning("查询数据中记录的任务结果信息为空，删除本记录 ID: {}".format(item_id))
                    self.__db.delete_item(item_id=item_id)
                    continue

                if not util.dict_has_value(task_dict_info, "taskId") or not util.dict_has_value(task_dict_info, "pmid"):
                    logger.warning("查询数据中记录的任务信息为空，删除本记录 ID: {}".format(item_id))
                    self.__remove_result(task_dict_info["resultDirPath"], item_id)
                    continue

                result_info["task_info"] = task_dict_info
                yield result_info

            except BaseException as e:
                logger.exception(e)
                self.__sleep(5)

    def __remove_result(self, result_dir: str, result_id: int):
        try:
            util.remove_result_dir(result_dir)
            self.__db.delete_item(item_id=result_id)
        except BaseException as e:
            logger.exception(e)

    @staticmethod
    def __load_json_from_str(j) -> dict:
        if not j:
            return None
        if isinstance(j, dict):
            return j
        if not isinstance(j, str):
            return None
        try:
            return json.loads(j, encoding="UTF-8")
        except BaseException as e:
            logger.exception(e)
        return None

    @staticmethod
    def __sleep(i=10):
        try:
            time.sleep(i)
        except BaseException as e:
            logger.exception(e)

    def stop(self):
        self.__stop = True
