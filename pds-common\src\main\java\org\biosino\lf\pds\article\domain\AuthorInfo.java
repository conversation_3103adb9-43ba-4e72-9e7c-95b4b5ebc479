package org.biosino.lf.pds.article.domain;

import lombok.Data;

import java.util.List;

/**
 * 作者信息JSON对象
 */
@Data
public class AuthorInfo {
    /**
     * 名字
     */
    private String forename;

    /**
     * 姓氏
     */
    private String lastname;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 类型（person / organization）
     */
    private String type;

    /**
     * 作者顺序
     */
    private Integer authorOrder;

    /**
     * 是否通讯作者（yes/no）
     */
    private String authorCorrespond;

    /**
     * 机构列表
     */
    private List<String> organizations;
}
