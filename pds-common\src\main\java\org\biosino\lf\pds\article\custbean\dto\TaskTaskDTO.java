package org.biosino.lf.pds.article.custbean.dto;

import lombok.Data;
import org.biosino.lf.pds.common.core.domain.BaseEntity;

/**
 * 任务跟踪DTO
 *
 * <AUTHOR>
 * @date 2025/6/23
 */
@Data
public class TaskTaskDTO extends BaseEntity {
    /**
     * 任务id
     */
    private String id;

    /**
     * 任务名称/描述
     */
    private String description;

    /**
     * 任务发布开始时间
     */
    private String beginTime;

    /**
     * 任务发布结束时间
     */
    private String endTime;

    /**
     * 发布人
     */
    private Long creator;

}
