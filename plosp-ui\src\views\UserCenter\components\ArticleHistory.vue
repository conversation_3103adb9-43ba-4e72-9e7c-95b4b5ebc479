<template>
  <div class="article-history-page">
    <div class="page-content">
      <!-- 文献历史列表区域 -->
      <div class="history-section">
        <el-card class="history-card">
          <template #header>
            <div class="card-header">
              <h3 class="section-title">
                <el-icon><Document /></el-icon>
                文献浏览历史
              </h3>
              <div class="table-info">
                共 {{ total }} 条记录
              </div>
            </div>
          </template>
          <!-- 工具栏 -->
          <div class="d-flex justify-space-between">
            <div class="search-form-wrapper">
              <el-form :model="searchForm" class="search-form" :inline="true">
                <el-form-item label="关键字" class="form-item">
                  <el-input
                      v-model="searchForm.keyword"
                      placeholder="请输入文献编号/文献标题"
                      clearable
                      class="search-input"
                  />
                </el-form-item>

                <el-form-item label="浏览时间" class="form-item date-range">
                  <el-date-picker
                      v-model="searchForm.browseTimeRange"
                      type="daterange"
                      range-separator="至"
                      start-placeholder="起始时间"
                      end-placeholder="截止时间"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                      class="search-input date-picker"
                  />
                </el-form-item>

                <el-form-item class="form-item search-actions">
                  <el-button type="primary" @click="handleSearch" class="search-btn">
                    <el-icon class="mr-1"><Search /></el-icon>
                    搜索
                  </el-button>
                  <el-button @click="handleReset" class="reset-btn">
                    <el-icon class="mr-1"><Refresh /></el-icon>
                    重置
                  </el-button>
                </el-form-item>
              </el-form>
            </div>

            <div class="toolbar-right">
              <div class="select-all-section">
                <el-checkbox v-model="selectAll" @change="handleSelectAll">
                  全选
                </el-checkbox>
              </div>
              <div class="batch-actions">
                <el-button type="danger" plain @click="handleClearHistory">
                  <el-icon class="mr-1"><Delete /></el-icon>
                  清空历史
                </el-button>
                <el-button type="danger" plain @click="handleDeleteSelected">
                  <el-icon class="mr-1"><Remove /></el-icon>
                  删除
                </el-button>
              </div>
            </div>
          </div>


          <!-- 文献列表 -->
          <div class="literature-list">
            <div
                v-for="article in articles"
                :key="article.id"
                class="literature-item"
            >
              <div class="literature-checkbox">
                <el-checkbox
                    :model-value="selectedArticles.includes(article.id)"
                    @change="toggleArticleSelection(article.id)"
                />
              </div>

              <div class="literature-content">
                <!-- 标题行 -->
                <div class="title-row">
                  <span class="source-tag" :class="article.source.toLowerCase()">{{ article.source }}</span>
                  <h4 class="literature-title">{{ article.title }}</h4>
                </div>

                <!-- 作者 -->
                <div class="literature-authors">
                  {{ article.authors }}
                </div>

                <!-- 期刊信息和标识符 -->
                <div class="literature-meta-line">
                  <div class="literature-meta-line mb-0">
                    <div class="journal-info">
                      <span class="journal-name">{{ article.journal }}</span>
                      <span class="publication-year">{{ article.year }}</span>
                      <span class="volume-info">{{ article.volume }}({{ article.issue }}):{{ article.pages }}</span>
                    </div>
                    <div class="literature-ids">
                      <span v-if="article.pmid" class="id-tag pmid-tag">
                        PMID: {{ article.pmid }}
                      </span>
                      <span v-if="article.doi" class="id-tag doi-tag">
                        DOI: {{ article.doi }}
                      </span>
                    </div>
                  </div>
                  <div class="browse-info">
                    <span class="browse-time">{{ article.browseTime }}</span>
                  </div>
                </div>

                <!-- 描述 -->
                <div class="literature-description">
                  {{ article.description }}
                </div>
              </div>
            </div>
          </div>

          <!-- 分页 -->
          <div class="pagination-wrapper">
            <el-pagination
                v-model:current-page="pagination.currentPage"
                v-model:page-size="pagination.pageSize"
                :page-sizes="[10, 20, 50, 100]"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                class="pagination"
            />
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import {
  Search,
  Refresh,
  Document,
  Clock,
  Delete,
  Remove
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 响应式数据
const loading = ref(false)
const total = ref(0)
const selectAll = ref(false)
const selectedArticles = ref([])
const sort = ref('最近浏览')

// 搜索表单
const searchForm = reactive({
  keyword: '',
  browseTimeRange: null
})

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 20
})

// 文献历史数据
const articles = ref([
  {
    id: 1,
    source: 'PubMed',
    title: '新型冠状病毒感染中医药防治的临床研究进展及其机制分析',
    authors: 'Zhang L, Wang Y, Li J, Wang K, Chen M, Sun E',
    description: '本研究通过系统性回顾和荟萃分析，探讨了中医药在新型冠状病毒感染防治中的临床应用效果及其潜在机制，为中西医结合治疗提供了重要的理论依据和实践指导。',
    journal: 'Nature',
    year: '2024',
    volume: '615',
    issue: '7952',
    pages: '456-468',
    doi: '10.1038/s41586-024-07123-4',
    pmid: '38234567',
    browseTime: '2024-01-15',
    browseCount: 3
  },
  {
    id: 2,
    source: 'PMC',
    title: 'PD-1/PD-L1免疫检查点抑制剂在非小细胞肺癌中的应用及其耐药机制',
    authors: 'Chen H, Liu Y, Yang R, Zhao W, Wang J, Dong Z',
    description: '深入分析了PD-1/PD-L1免疫检查点抑制剂在非小细胞肺癌治疗中的作用机制，重点探讨了耐药性产生的分子基础和潜在的克服策略。',
    journal: 'Nature',
    year: '2024',
    volume: '615',
    issue: '7952',
    pages: '123-135',
    doi: '10.1038/s41586-024-07456-7',
    pmid: '38345678',
    browseTime: '2024-01-14',
    browseCount: 5
  },
  {
    id: 3,
    source: 'BioRxiv',
    title: '基于深度学习的医学影像诊断技术在早期癌症检测中的应用研究',
    authors: 'Li Q, Wu J, Xu L, Huang P, Zhang J',
    description: '利用先进的深度学习算法开发了一套高精度的医学影像分析系统，能够有效识别早期癌症病变，为临床诊断提供了强有力的技术支持。',
    journal: 'Science',
    year: '2024',
    volume: '383',
    issue: '6630',
    pages: '789-801',
    doi: '10.1126/science.abcd5678',
    pmid: '38456789',
    browseTime: '2024-01-13',
    browseCount: 2
  },
  {
    id: 4,
    source: 'PubMed',
    title: '机器学习在药物发现中的应用：当前挑战与未来展望',
    authors: 'Wang X, Liu M, Chen S, Zhang H, Li P',
    description: '综述了机器学习技术在药物发现各个阶段的应用现状，分析了当前面临的主要挑战，并展望了未来的发展方向和潜在突破点。',
    journal: 'Cell',
    year: '2024',
    volume: '187',
    issue: '3',
    pages: '567-580',
    doi: '10.1016/j.cell.2024.01.012',
    pmid: '38567890',
    browseTime: '2024-01-12',
    browseCount: 7
  },
  {
    id: 5,
    source: 'PMC',
    title: '气候变化对全球农业生产力的影响：基于卫星数据的综合评估',
    authors: 'Brown A, Smith J, Johnson K, Davis R, Wilson T',
    description: '利用长期卫星观测数据，系统评估了气候变化对全球主要农作物产量的影响，为制定适应性农业政策提供了科学依据。',
    journal: 'Nature Climate Change',
    year: '2024',
    volume: '14',
    issue: '2',
    pages: '123-138',
    doi: '10.1038/s41558-024-01234-5',
    pmid: '38678901',
    browseTime: '2024-01-11',
    browseCount: 1
  },
  {
    id: 6,
    source: 'BioRxiv',
    title: '量子计算在密码学中的应用：当前挑战与未来机遇',
    authors: 'Taylor M, Anderson P, Thompson L, White S, Clark D',
    description: '探讨了量子计算技术在现代密码学中的应用前景，分析了量子算法对传统加密方法的威胁，并提出了后量子密码学的发展策略。',
    journal: 'Nature Physics',
    year: '2024',
    volume: '20',
    issue: '1',
    pages: '45-58',
    doi: '10.1038/s41567-024-02345-6',
    pmid: '38789012',
    browseTime: '2024-01-10',
    browseCount: 4
  }
])

// 方法
const handleSearch = () => {
  ElMessage.success('搜索功能待实现')
}

const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    if (key === 'browseTimeRange') {
      searchForm[key] = null
    } else {
      searchForm[key] = ''
    }
  })
  ElMessage.success('重置成功')
}

const handleSelectAll = (checked) => {
  if (checked) {
    selectedArticles.value = filteredArticles.value.map(article => article.id)
  } else {
    selectedArticles.value = []
  }
}

const toggleArticleSelection = (articleId) => {
  const index = selectedArticles.value.indexOf(articleId)
  if (index > -1) {
    selectedArticles.value.splice(index, 1)
  } else {
    selectedArticles.value.push(articleId)
  }

  // 更新全选状态
  selectAll.value = selectedArticles.value.length === filteredArticles.value.length
}

const handleClearHistory = () => {
  ElMessageBox.confirm(
      '确定要清空所有浏览历史吗？此操作不可恢复。',
      '确认清空',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
  ).then(() => {
    ElMessage.success('清空历史功能待实现')
  }).catch(() => {
    ElMessage.info('已取消清空')
  })
}

const handleDeleteSelected = () => {
  if (selectedArticles.value.length === 0) {
    ElMessage.warning('请先选择要删除的记录')
    return
  }

  ElMessageBox.confirm(
      `确定要删除选中的 ${selectedArticles.value.length} 条浏览记录吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
  ).then(() => {
    ElMessage.success('删除功能待实现')
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

// 初始化
onMounted(() => {
  total.value = articles.value.length
})
</script>

<style lang="scss" scoped>
@import "@/assets/styles/variables";

.article-history-page {
  padding: 0;
}

.page-content {
  display: flex;
  flex-direction: column;
  gap: $spacing-lg;
}

// 通用卡片样式
.search-card,
.history-card {
  background: white;
  border-radius: $border-radius-lg;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04), 0 1px 4px rgba(0, 0, 0, 0.02);
  border: 1px solid rgba(226, 232, 240, 0.5);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.06), 0 2px 8px rgba(0, 0, 0, 0.04);
    transform: translateY(-1px);
  }

  :deep(.el-card__header) {
    padding: $spacing-md $spacing-lg;
    border-bottom: 1px solid rgba(226, 232, 240, 0.6);
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.8), rgba(241, 245, 249, 0.6));
  }

  :deep(.el-card__body) {
    padding: $spacing-lg;
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .section-title {
    display: flex;
    align-items: center;
    gap: $spacing-xs;
    font-size: 18px;
    font-weight: $font-weight-bold;
    color: $primary-color;
    margin: 0;

    .el-icon {
      font-size: 28px;
      background: linear-gradient(135deg, $primary-color, #2563eb);
      color: white;
      padding: 6px;
      border-radius: 50%;
      box-shadow: 0 2px 8px rgba(4, 56, 115, 0.2);
    }
  }

  .table-info {
    font-size: $font-size-small;
    color: #6b7280;
    font-weight: $font-weight-medium;
  }
}

// 搜索表单样式
.search-form-wrapper {
  .search-form {
    .form-item {
      margin-bottom: $spacing-md;
      margin-right: $spacing-lg;

      :deep(.el-form-item__label) {
        font-weight: $font-weight-medium;
        color: $gray;
        font-size: $font-size-small;
        width: 80px;
        text-align: right;
        padding-right: $spacing-sm;
      }

      :deep(.el-form-item__content) {
        flex: 1;
        min-width: 200px;
      }

      &.date-range {
        :deep(.el-form-item__content) {
          min-width: 300px;
        }
      }

      &.search-actions {
        :deep(.el-form-item__label) {
          width: 0;
        }

        :deep(.el-form-item__content) {
          margin-left: 0;
        }
      }
    }

    .search-input {
      width: 100%;
    }

    .date-picker {
      width: 100%;
    }

    .search-btn {
      background: $primary-color;
      border: none;
      color: white;
      font-weight: $font-weight-bold;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-1px);
      }
    }

    .reset-btn {
      background: white;
      border: 1px solid rgba(226, 232, 240, 0.8);
      color: $gray;
      font-weight: $font-weight-medium;
      transition: all 0.3s ease;

      &:hover {
        border-color: $primary-color;
        color: $primary-color;
        transform: translateY(-1px);
      }
    }
  }
}

// 工具栏样式
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-md;
  padding-bottom: $spacing-sm;
  border-bottom: 1px solid rgba(226, 232, 240, 0.6);
  flex-wrap: wrap;
  gap: $spacing-sm;

  @media (max-width: $breakpoint-md) {
    flex-direction: column;
    align-items: stretch;
  }
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: $spacing-md;

  .el-radio-button.is-active {
    :deep(.el-radio-button__inner) {
      background: #2E4E73;
      color: #ffffff;
    }
  }
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: $spacing-md;
  flex-wrap: wrap;
  margin-bottom: 20px;
  justify-content: flex-end;

  @media (max-width: $breakpoint-md) {
    justify-content: space-between;
  }
}

.select-all-section {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
}

.batch-actions {
  display: flex;
  align-items: center;
  gap: $spacing-xs;

  .el-button {
    background: #FEF2F2;
    color: #DC2626;
    border: 1px solid rgba(220, 38, 38, 0.3);

    &:hover {
      background: #DC2626;
      color: #ffffff;
      border: 1px solid #DC2626;
    }
  }
}

// 文献列表样式
.literature-list {
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;
}

.literature-item {
  display: flex;
  gap: $spacing-sm;
  padding: $spacing-sm $spacing-md;
  border: 1px solid rgba(226, 232, 240, 0.6);
  border-radius: $border-radius-md;
  background: white;
  transition: all 0.3s ease;

  &:hover {
    border-color: rgba(148, 163, 184, 0.4);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
  }

  .literature-checkbox {
    flex-shrink: 0;
    display: flex;
    align-items: flex-start;
    padding-top: 2px;
  }

  .literature-content {
    flex: 1;
    min-width: 0;
  }
}

.title-row {
  display: flex;
  align-items: flex-start;
  gap: $spacing-xs;
  margin-bottom: $spacing-xs;
}

.source-tag {
  flex-shrink: 0;
  padding: 2px 14px;
  border-radius: 10px;
  font-size: 14px;
  font-weight: $font-weight-bold;
  margin-top: 2px;

  &.pubmed {
    background: #E3F2FD;
    color: #1976D2;
  }

  &.pmc {
    background: #F3E5F5;
    color: #7B1FA2;
  }

  &.biorxiv {
    background: #E8F5E8;
    color: #388E3C;
  }
}

.literature-title {
  font-size: $font-size-medium;
  font-weight: $font-weight-bold;
  color: $primary-color;
  margin: 0;
  line-height: 1.5;
  cursor: pointer;
  transition: all 0.3s ease;
  flex: 1;

  &:hover {
    color: #2563eb;
  }
}

.literature-authors {
  font-size: $font-size-small;
  color: #575757;
  margin-bottom: $spacing-xs;
  line-height: 1.4;
  font-weight: $font-weight-medium;
}

.literature-meta-line {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: $spacing-md;
  font-size: 14px;
  color: #374151;
  margin-bottom: $spacing-xxs;

  .journal-info {
    display: flex;
    align-items: center;
    gap: $spacing-xs;
    font-size: $font-size-small;

    .publication-year {
      color: #6b7280;
    }

    .volume-info {
      color: #6b7280;
    }
  }

  .literature-ids {
    display: flex;
    align-items: center;
    gap: $spacing-xs;
    flex-shrink: 0;

    .id-tag {
      display: inline-flex;
      align-items: center;
      padding: 3px 8px;
      border-radius: 12px;
      transition: all 0.3s ease;
      cursor: pointer;
      white-space: nowrap;

      &.pmid-tag,
      &.doi-tag {
        background: #F2F7FB;
        color: #374151;

        &:hover {
          background: #dae8fa;
        }
      }
    }
  }

  .browse-info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 2px;
    font-size: $font-size-small;
    color: #6b7280;
    flex-shrink: 0;

    .browse-time {
      font-weight: $font-weight-medium;
    }

    .browse-count {
      color: $primary-color;
      font-weight: $font-weight-bold;
    }
  }
}

.literature-description {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

// 分页样式
.pagination-wrapper {
  margin-top: 20px;
  .el-pagination {
    justify-content: center;
  }
}

// 响应式设计
@media (max-width: $breakpoint-lg) {
  .search-form {
    .form-item {
      margin-right: $spacing-md;

      :deep(.el-form-item__label) {
        width: 70px;
      }

      :deep(.el-form-item__content) {
        min-width: 180px;
      }

      &.date-range {
        :deep(.el-form-item__content) {
          min-width: 250px;
        }
      }
    }
  }
}

@media (max-width: $breakpoint-md) {
  .page-content {
    gap: $spacing-md;
  }

  .search-card,
  .history-card {
    :deep(.el-card__body) {
      padding: $spacing-md;
    }
  }

  .search-form {
    :deep(.el-form--inline) {
      .el-form-item {
        display: block;
        margin-right: 0;
        margin-bottom: $spacing-sm;

        .el-form-item__label {
          width: 100%;
          text-align: left;
          padding-right: 0;
          margin-bottom: $spacing-xs;
        }

        .el-form-item__content {
          width: 100%;
          margin-left: 0;
        }
      }
    }

    .search-btn,
    .reset-btn {
      width: 100%;
      margin-bottom: $spacing-xs;
    }
  }

  .literature-item {
    padding: $spacing-xs $spacing-sm;
    flex-direction: column;
    gap: $spacing-xs;

    .literature-checkbox {
      align-self: flex-start;
    }
  }

  .literature-content {
    .title-row {
      flex-direction: column;
      gap: $spacing-xxs;
    }

    .literature-title {
      font-size: $font-size-small;
    }

    .literature-authors {
      font-size: 14px;
      margin-bottom: $spacing-xxs;
    }

    .literature-meta-line {
      flex-direction: column;
      align-items: flex-start;
      gap: $spacing-xs;

      .journal-info {
        gap: 2px;
      }

      .literature-ids {
        gap: $spacing-xxs;
        flex-wrap: wrap;
        width: 100%;

        .id-tag {
          padding: 1px 6px;
        }
      }

      .browse-info {
        align-items: flex-start;
        font-size: 12px;
      }
    }
  }

  .toolbar {
    .toolbar-left,
    .toolbar-right {
      width: 100%;
      justify-content: space-between;
    }

    .batch-actions {
      flex-wrap: wrap;
      justify-content: flex-start;
    }
  }
}

// 加载状态样式
:deep(.el-loading-mask) {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(4px);
}

:deep(.el-loading-spinner) {
  .el-loading-text {
    color: $primary-color;
    font-weight: $font-weight-medium;
  }

  .circular {
    stroke: $primary-color;
  }
}
</style>