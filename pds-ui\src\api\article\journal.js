import request from '@/utils/request';

// 查询期刊列表
export function listJournal(query) {
  return request({
    url: '/journal/list',
    method: 'get',
    params: query,
  });
}

// 获取期刊详情
export function getJournal(id) {
  return request({
    url: `/journal/${id}`,
    method: 'get',
  });
}

// 获取出版社名称列表（用于自动完成）
export function getPublisherNames(query) {
  return request({
    url: '/journal/publishers',
    method: 'get',
    params: { name: query },
  });
}

// 更新期刊信息
export function updateJournal(data) {
  return request({
    url: '/journal/updateJournal',
    method: 'post',
    data: data,
  });
}

// 修改期刊状态
export function changeJournalStatus(id, status) {
  return request({
    url: '/journal/changeStatus',
    method: 'post',
    params: {
      id: id,
      status: status,
    },
  });
}

// 合并期刊
export function mergeJournals(data) {
  return request({
    url: '/journal/merge',
    method: 'post',
    data: data,
  });
}
