<template>
  <div class="app-container">
    <!-- 查询条件表单 -->
    <el-form
      v-show="showSearch"
      ref="queryForm"
      :model="queryParams"
      :inline="true"
      label-width="120px"
      class="search-form"
    >
      <div class="form-row">
        <el-form-item label="期刊名称(批量)" prop="titleMultiple">
          <el-input
            v-model="queryParams.titleMultiple"
            type="textarea"
            placeholder="请输入多个期刊名称"
            :rows="1"
            wrap="off"
            clearable
            class="search-input"
            @keydown="handleKeyDown"
          />
        </el-form-item>
        <el-form-item label="期刊名称" prop="title">
          <el-input
            v-model="queryParams.title"
            placeholder="请输入期刊名称"
            clearable
            class="search-input"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="期刊简称" prop="isoabbreviation">
          <el-input
            v-model="queryParams.isoabbreviation"
            placeholder="请输入期刊简称"
            clearable
            class="search-input"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="出版社名称" prop="publisher">
          <el-autocomplete
            v-model="queryParams.publisher"
            :fetch-suggestions="getPublisherSuggestions"
            placeholder="请输入出版社名称"
            clearable
            class="search-input"
            @keyup.enter="handleQuery"
            @select="handlePublisherSelect"
            @clear="handlePublisherClear"
          />
        </el-form-item>
        <el-form-item label="Unique NLM ID" prop="uniqueNlmId">
          <el-input
            v-model="queryParams.uniqueNlmId"
            placeholder="请输入Unique NLM ID"
            clearable
            class="search-input"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
      </div>
      <div class="form-row">
        <el-form-item label="ISSN Print" prop="issnPrint">
          <el-input
            v-model="queryParams.issnPrint"
            placeholder="请输入ISSN Print"
            clearable
            class="search-input"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="ISSN Electronic" prop="issnElectronic">
          <el-input
            v-model="queryParams.issnElectronic"
            placeholder="请输入ISSN Electronic"
            clearable
            class="search-input"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="来源" prop="source">
          <el-select
            v-model="queryParams.source"
            placeholder="请选择来源"
            clearable
            class="search-input"
          >
            <el-option label="PubMed" value="PubMed" />
            <el-option label="PMC" value="PMC" />
            <el-option label="JCR" value="JCR" />
            <el-option label="中科院" value="中科院" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select
            v-model="queryParams.status"
            placeholder="请选择状态"
            clearable
            class="search-input"
          >
            <el-option
              v-for="dict in sys_normal_disable"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <div>
          <el-button type="primary" icon="Search" @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </div>
      </div>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mt10">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['journal:merge']"
          type="warning"
          plain
          icon="Sort"
          :disabled="!canMerge"
          @click="handleMerge"
        >
          批量合并
        </el-button>
      </el-col>
      <right-toolbar
        v-model:show-search="showSearch"
        :columns="columns"
        @query-table="getJournalList"
      />
    </el-row>

    <!-- 期刊表格列表 -->
    <div v-loading="loading" class="journal-list">
      <el-empty v-if="journalList.length === 0" description="暂无数据" />
      <el-table
        v-else
        :data="journalList"
        border
        style="width: 100%"
        :row-key="row => row.id"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
      >
        <el-table-column type="selection" width="45" />
        <el-table-column
          v-if="columns[0].visible"
          prop="title"
          sortable
          label="期刊名称"
          min-width="250"
        >
          <template #default="scope">
            <el-popover
              placement="top"
              :width="300"
              trigger="hover"
              :content="scope.row.title"
            >
              <template #reference>
                <span class="text-ellipsis">{{ scope.row.title }}</span>
              </template>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column
          v-if="columns[1].visible"
          prop="isoabbreviation"
          sortable
          label="期刊简称"
          min-width="120"
        >
          <template #default="scope">
            <el-popover
              placement="top"
              :width="250"
              trigger="hover"
              :content="scope.row.isoabbreviation"
            >
              <template #reference>
                <span class="text-ellipsis">{{
                  scope.row.isoabbreviation
                }}</span>
              </template>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column
          v-if="columns[2].visible"
          prop="publisherName"
          label="出版社名称"
          min-width="130"
        >
          <template #default="scope">
            <el-popover
              placement="top"
              :width="280"
              trigger="hover"
              :content="scope.row.publisherName"
            >
              <template #reference>
                <span class="text-ellipsis">{{ scope.row.publisherName }}</span>
              </template>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column
          v-if="columns[3].visible"
          prop="issnPrint"
          label="ISSN Print"
          sortable
          width="120"
        >
          <template #default="scope">
            <span>{{
              Array.isArray(scope.row.issnPrint) &&
              scope.row.issnPrint.length > 0
                ? scope.row.issnPrint[0]
                : scope.row.issnPrint
            }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="columns[4].visible"
          prop="issnElectronic"
          label="ISSN Electronic"
          sortable
          width="150"
        >
          <template #default="scope">
            <span>{{
              Array.isArray(scope.row.issnElectronic) &&
              scope.row.issnElectronic.length > 0
                ? scope.row.issnElectronic[0]
                : scope.row.issnElectronic
            }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="columns[5].visible"
          label="历史ISSN"
          sortable
          width="110"
        >
          <template #default="scope">
            <template
              v-if="scope.row.issnHistory && scope.row.issnHistory.length > 0"
            >
              <span>{{ scope.row.issnHistory[0] }}</span>
              <el-tooltip
                v-if="scope.row.issnHistory.length > 1"
                placement="top"
              >
                <template #content>
                  <div
                    v-for="(issn, index) in scope.row.issnHistory.slice(1)"
                    :key="index"
                  >
                    {{ issn }}
                  </div>
                </template>
                <el-icon class="more-icon"><Fold /></el-icon>
              </el-tooltip>
            </template>
            <span v-else class="text-gray-400"></span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="columns[6].visible"
          prop="uniqueNlmId"
          sortable
          label="Unique NLM ID"
          width="150"
        />
        <el-table-column
          v-if="columns[7].visible"
          label="历史Unique NLM ID"
          width="160"
        >
          <template #default="scope">
            <template
              v-if="
                scope.row.uniqueHistory && scope.row.uniqueHistory.length > 0
              "
            >
              <span>{{ scope.row.uniqueHistory[0] }}</span>
              <el-tooltip
                v-if="scope.row.uniqueHistory.length > 1"
                placement="top"
              >
                <template #content>
                  <div
                    v-for="(nlmId, index) in scope.row.uniqueHistory.slice(1)"
                    :key="index"
                  >
                    {{ nlmId }}
                  </div>
                </template>
                <el-icon class="more-icon"><Fold /></el-icon>
              </el-tooltip>
            </template>
            <span v-else class="text-gray-400"></span>
          </template>
        </el-table-column>
        <el-table-column v-if="columns[8].visible" label="更新时间" width="160">
          <template #default="scope">
            {{ parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
          </template>
        </el-table-column>
        <el-table-column
          v-if="columns[9].visible"
          prop="medlineTa"
          sortable
          label="MedlineTA"
          width="115"
        >
          <template #default="scope">
            <el-popover
              placement="top"
              :width="200"
              trigger="hover"
              :content="scope.row.medlineTa"
            >
              <template #reference>
                <span class="text-ellipsis">{{ scope.row.medlineTa }}</span>
              </template>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column v-if="columns[10].visible" label="来源" width="90">
          <template #default="scope">
            <template v-if="scope.row.source && scope.row.source.length > 0">
              <el-tag
                v-for="src in scope.row.source"
                :key="src"
                size="small"
                style="margin-right: 4px"
                >{{ src }}</el-tag
              >
            </template>
            <span v-else></span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="columns[11].visible"
          prop="status"
          label="状态"
          width="70"
        >
          <template #default="scope">
            <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="scope">
            <el-button
              v-hasPermi="['journal:edit']"
              type="primary"
              link
              icon="Edit"
              @click="handleEdit(scope.row)"
              >编辑</el-button
            >
            <el-button
              v-hasPermi="['journal:status']"
              :type="scope.row.status === 1 ? 'success' : 'danger'"
              link
              :icon="scope.row.status === 1 ? 'CircleCheck' : 'CircleClose'"
              @click="toggleStatus(scope.row)"
            >
              {{ scope.row.status === 1 ? '启用' : '停用' }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        @pagination="getJournalList"
      />
    </div>

    <!-- 编辑期刊弹窗 -->
    <el-dialog
      v-model="editDialog.visible"
      title="编辑期刊"
      width="700px"
      append-to-body
      :close-on-click-modal="!editDialog.loading"
    >
      <el-form
        ref="editFormRef"
        v-loading="editDialog.loading"
        :model="editDialog.form"
        label-width="150px"
        :rules="rules"
        element-loading-text="正在加载期刊详情..."
      >
        <el-form-item label="期刊名称" prop="title">
          <el-input
            v-model="editDialog.form.title"
            placeholder="请输入期刊名称"
          />
        </el-form-item>
        <el-form-item label="期刊简称" prop="isoabbreviation">
          <el-input
            v-model="editDialog.form.isoabbreviation"
            placeholder="请输入期刊简称"
          />
        </el-form-item>
        <el-form-item label="出版社名称" prop="publisherName">
          <el-autocomplete
            v-model="editDialog.form.publisherName"
            :fetch-suggestions="getPublisherSuggestions"
            placeholder="请输入出版社名称"
            clearable
            style="width: 100%"
            @select="handleEditPublisherSelect"
            @clear="handleEditPublisherClear"
          />
        </el-form-item>

        <el-form-item label="ISSN Print" prop="issnPrint">
          <el-input
            v-model="editDialog.form.issnPrint"
            placeholder="请输入ISSN Print"
          />
        </el-form-item>
        <el-form-item label="ISSN Electronic" prop="issnElectronic">
          <el-input
            v-model="editDialog.form.issnElectronic"
            placeholder="请输入ISSN Electronic"
          />
        </el-form-item>

        <el-form-item label="Unique NLM ID" prop="uniqueNlmId">
          <el-input
            v-model="editDialog.form.uniqueNlmId"
            placeholder="请输入Unique NLM ID"
          />
        </el-form-item>
        <el-form-item label="历史ISSN" prop="issnHistory">
          <el-select
            v-model="editDialog.form.issnHistory"
            multiple
            filterable
            allow-create
            default-first-option
            placeholder="请输入或选择历史ISSN"
          >
            <el-option
              v-for="item in editDialog.form.issnHistory"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="历史Unique NLM ID" prop="uniqueHistory">
          <el-select
            v-model="editDialog.form.uniqueHistory"
            multiple
            filterable
            allow-create
            default-first-option
            placeholder="请输入或选择历史Unique NLM ID"
          >
            <el-option
              v-for="item in editDialog.form.uniqueHistory"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="MedlineTA" prop="medlineTa">
          <el-input
            v-model="editDialog.form.medlineTa"
            placeholder="请输入MedlineTA"
          />
        </el-form-item>

        <el-form-item label="来源" prop="source">
          <el-select
            v-model="editDialog.form.source"
            multiple
            placeholder="请选择来源"
          >
            <el-option label="PubMed" value="PubMed" />
            <el-option label="PMC" value="PMC" />
            <el-option label="JCR" value="JCR" />
            <el-option label="ZKY" value="ZKY" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitEdit">保存</el-button>
          <el-button @click="cancelEdit">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 合并详情弹窗 -->
    <el-dialog
      v-model="mergeDetailDialog.visible"
      title="合并详情"
      width="700px"
      append-to-body
      :close-on-click-modal="!mergeDetailDialog.loading"
    >
      <el-form
        v-loading="mergeDetailDialog.loading"
        :model="mergeDetailDialog.form"
        label-width="150px"
        element-loading-text="正在合并期刊，请稍候..."
      >
        <el-form-item label="期刊名称">
          <el-autocomplete
            v-model="mergeDetailDialog.form.title"
            :fetch-suggestions="
              (queryString, cb) =>
                getSelectedJournalSuggestions('title', queryString, cb)
            "
            placeholder="请输入或选择期刊名称"
            style="width: 100%"
            :disabled="mergeDetailDialog.loading"
          >
            <template #default="{ item }">
              <div>{{ item.value }}</div>
            </template>
          </el-autocomplete>
        </el-form-item>
        <el-form-item label="期刊简称">
          <el-autocomplete
            v-model="mergeDetailDialog.form.isoabbreviation"
            :fetch-suggestions="
              (queryString, cb) =>
                getSelectedJournalSuggestions(
                  'isoabbreviation',
                  queryString,
                  cb,
                )
            "
            placeholder="请输入或选择期刊简称"
            style="width: 100%"
            :disabled="mergeDetailDialog.loading"
          >
            <template #default="{ item }">
              <div>{{ item.value }}</div>
            </template>
          </el-autocomplete>
        </el-form-item>
        <el-form-item label="出版社名称">
          <el-autocomplete
            v-model="mergeDetailDialog.form.publisherName"
            :fetch-suggestions="
              (queryString, cb) =>
                getSelectedJournalSuggestions('publisherName', queryString, cb)
            "
            placeholder="请输入或选择出版社名称"
            style="width: 100%"
            :disabled="mergeDetailDialog.loading"
          >
            <template #default="{ item }">
              <div>{{ item.value }}</div>
            </template>
          </el-autocomplete>
        </el-form-item>
        <el-form-item label="ISSN Print">
          <el-autocomplete
            v-model="mergeDetailDialog.form.issnPrint"
            :fetch-suggestions="
              (queryString, cb) =>
                getSelectedJournalSuggestions(
                  'issnPrint',
                  queryString,
                  cb,
                  true,
                )
            "
            placeholder="请输入或选择ISSN Print"
            style="width: 100%"
            :disabled="mergeDetailDialog.loading"
          >
            <template #default="{ item }">
              <div>{{ item.value }}</div>
            </template>
          </el-autocomplete>
        </el-form-item>
        <el-form-item label="ISSN Electronic">
          <el-autocomplete
            v-model="mergeDetailDialog.form.issnElectronic"
            :fetch-suggestions="
              (queryString, cb) =>
                getSelectedJournalSuggestions(
                  'issnElectronic',
                  queryString,
                  cb,
                  true,
                )
            "
            placeholder="请输入或选择ISSN Electronic"
            style="width: 100%"
            :disabled="mergeDetailDialog.loading"
          >
            <template #default="{ item }">
              <div>{{ item.value }}</div>
            </template>
          </el-autocomplete>
        </el-form-item>

        <el-form-item label="Unique NLM ID">
          <el-autocomplete
            v-model="mergeDetailDialog.form.uniqueNlmId"
            :fetch-suggestions="
              (queryString, cb) =>
                getSelectedJournalSuggestions(
                  'uniqueNlmId',
                  queryString,
                  cb,
                  true,
                )
            "
            placeholder="请输入或选择Unique NLM ID"
            style="width: 100%"
            :disabled="mergeDetailDialog.loading"
          >
            <template #default="{ item }">
              <div>{{ item.value }}</div>
            </template>
          </el-autocomplete>
        </el-form-item>
        <el-form-item label="历史ISSN">
          <el-select
            v-model="mergeDetailDialog.form.issnHistory"
            multiple
            filterable
            allow-create
            default-first-option
            placeholder="请输入或选择历史ISSN"
            style="width: 100%"
            :disabled="mergeDetailDialog.loading"
          >
            <el-option
              v-for="item in getHistoricalIssnOptions()"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="历史Unique NLM ID">
          <el-select
            v-model="mergeDetailDialog.form.uniqueHistory"
            multiple
            filterable
            allow-create
            default-first-option
            placeholder="请输入或选择历史Unique NLM ID"
            style="width: 100%"
            :disabled="mergeDetailDialog.loading"
          >
            <el-option
              v-for="item in getHistoricalNlmIdOptions()"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="MedlineTA">
          <el-autocomplete
            v-model="mergeDetailDialog.form.medlineTa"
            :fetch-suggestions="
              (queryString, cb) =>
                getSelectedJournalSuggestions('medlineTa', queryString, cb)
            "
            placeholder="请输入或选择MedlineTA"
            style="width: 100%"
            :disabled="mergeDetailDialog.loading"
          >
            <template #default="{ item }">
              <div>{{ item.value }}</div>
            </template>
          </el-autocomplete>
        </el-form-item>

        <el-form-item label="来源">
          <el-select
            v-model="mergeDetailDialog.form.source"
            multiple
            placeholder="请选择来源"
            style="width: 100%"
            :disabled="mergeDetailDialog.loading"
          >
            <el-option label="PubMed" value="PubMed" />
            <el-option label="PMC" value="PMC" />
            <el-option label="JCR" value="JCR" />
            <el-option label="ZKY" value="ZKY" />
          </el-select>
        </el-form-item>

        <el-form-item label="脚本">
          <el-select
            v-model="mergeDetailDialog.form.scriptId"
            placeholder="请选择脚本"
            style="width: 100%"
            clearable
            :disabled="mergeDetailDialog.loading"
          >
            <el-option
              v-for="script in getSelectedScripts()"
              :key="script.id"
              :label="script.name"
              :value="script.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button
            type="primary"
            :loading="mergeDetailDialog.loading"
            :disabled="mergeDetailDialog.loading"
            @click="submitMergeDetail"
          >
            {{ mergeDetailDialog.loading ? '合并中...' : '保存' }}
          </el-button>
          <el-button
            :disabled="mergeDetailDialog.loading"
            @click="mergeDetailDialog.visible = false"
            >取消</el-button
          >
        </div>
      </template>
    </el-dialog>

    <!-- 期刊详情弹窗 -->
    <el-dialog
      v-model="detailDialog.visible"
      title="期刊详情"
      width="500px"
      append-to-body
    >
      <div class="journal-detail">
        <div class="detail-row">
          <span class="label">期刊名称：</span
          >{{ detailDialog.data.journalName }}
        </div>
        <div class="detail-row">
          <span class="label">期刊简称：</span
          >{{ detailDialog.data.abbreviation }}
        </div>
        <div class="detail-row">
          <span class="label">Unique NLM ID：</span
          >{{ detailDialog.data.nlmId }}
        </div>
        <div class="detail-row">
          <span class="label">ISSN Print：</span>
          <span v-if="Array.isArray(detailDialog.data.issnPrint)">{{
            detailDialog.data.issnPrint.join(', ')
          }}</span>
          <span v-else>{{ detailDialog.data.issnPrint }}</span>
        </div>
        <div class="detail-row">
          <span class="label">ISSN Electronic：</span>
          <span v-if="Array.isArray(detailDialog.data.issnElectronic)">{{
            detailDialog.data.issnElectronic.join(', ')
          }}</span>
          <span v-else>{{ detailDialog.data.issnElectronic }}</span>
        </div>
        <div class="detail-row">
          <span class="label">出版社：</span
          >{{ detailDialog.data.publisherName }}
        </div>
        <div class="detail-row">
          <span class="label">历史ISSN：</span>
          <span v-if="Array.isArray(detailDialog.data.historicalIssn)">{{
            detailDialog.data.historicalIssn.join(', ')
          }}</span>
          <span v-else>{{ detailDialog.data.historicalIssn }}</span>
        </div>
        <div class="detail-row">
          <span class="label">历史Unique NLM ID：</span>
          <span v-if="Array.isArray(detailDialog.data.historicalNlmId)">{{
            detailDialog.data.historicalNlmId.join(', ')
          }}</span>
          <span v-else>{{ detailDialog.data.historicalNlmId }}</span>
        </div>
        <div class="detail-row">
          <span class="label">MedlineTA：</span
          >{{ detailDialog.data.medlineTa }}
        </div>
        <div class="detail-row">
          <span class="label">来源：</span
          >{{ detailDialog.data.source?.join(', ') }}
        </div>
        <div class="detail-row">
          <span class="label">状态：</span>{{ detailDialog.data.status }}
        </div>
      </div>
    </el-dialog>

    <!-- ISSN验证错误弹窗 -->
    <el-dialog
      v-model="validationErrorDialog.visible"
      title="字段冲突详情"
      width="1000px"
      append-to-body
      :close-on-click-modal="false"
    >
      <div class="validation-error-content">
        <el-alert
          title="检测到字段冲突"
          type="error"
          :closable="false"
          show-icon
          class="mb-16"
        >
        </el-alert>

        <el-table
          :data="validationErrorDialog.conflictData"
          border
          style="width: 100%"
          class="conflict-table"
        >
          <el-table-column
            prop="field"
            label="冲突字段"
            width="150"
            align="center"
          >
            <template #default="scope">
              <el-tag :type="getFieldTagType(scope.row.field)">
                {{ getFieldDisplayName(scope.row.field) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            prop="value"
            label="冲突值"
            width="150"
            align="center"
          >
            <template #default="scope">
              <el-text type="danger" class="conflict-value">
                {{ scope.row.value }}
              </el-text>
            </template>
          </el-table-column>
          <el-table-column
            prop="conflictJournalId"
            label="冲突期刊ID"
            width="120"
            align="center"
          >
            <template #default="scope">
              <el-link
                type="primary"
                @click="handleEdit(scope.row.conflictJournalId)"
              >
                {{ scope.row.conflictJournalId }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column
            prop="conflictJournalTitle"
            label="冲突期刊标题"
            min-width="200"
          >
            <template #default="scope">
              <el-popover
                placement="top"
                :width="300"
                trigger="hover"
                :content="scope.row.conflictJournalTitle"
              >
                <template #reference>
                  <span class="text-ellipsis">{{
                    scope.row.conflictJournalTitle
                  }}</span>
                </template>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" align="center">
            <template #default="scope">
              <el-button
                type="primary"
                size="small"
                @click="handleEdit(scope.row.conflictJournalId)"
              >
                查看详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="validation-tips">
          <h4>解决建议：</h4>
          <ul>
            <li>检查输入的ISSN值是否正确</li>
            <li>ISSN值在记录间必须报错唯一性</li>
            <li>人工确认需要修改的冲突期刊ISSN信息</li>
            <li>如果是合并操作，请先处理冲突的期刊记录</li>
            <li>联系管理员协助处理复杂的冲突情况</li>
          </ul>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button
            type="success"
            icon="Download"
            @click="exportJournalConflictData"
          >
            导出Excel
          </el-button>
          <el-button
            type="primary"
            @click="validationErrorDialog.visible = false"
          >
            我知道了
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Journal">
  import { ref, getCurrentInstance, computed } from 'vue';
  import { Fold } from '@element-plus/icons-vue';
  import {
    listJournal,
    getJournal,
    updateJournal,
    changeJournalStatus,
    mergeJournals,
  } from '@/api/article/journal';
  import * as XLSX from 'xlsx';
  import { listPublisher } from '@/api/article/publisher';
  import { parseTime } from '@/utils/ruoyi';

  const { proxy } = getCurrentInstance();

  // 字典数据
  const { sys_normal_disable } = proxy.useDict('sys_normal_disable');

  // 查询参数
  const queryParams = ref({
    pageNum: 1,
    pageSize: 20,
    publisher: '',
    publisherId: '',
    titleMultiple: '',
    title: '',
    isoabbreviation: '',
    uniqueNlmId: '',
    issnPrint: '',
    issnElectronic: '',
    source: '',
    status: '',
    orderByColumn: '', // 排序字段
    isAsc: '', // 排序方向：asc 或 desc
  });

  const showSearch = ref(true);
  const loading = ref(false);
  const total = ref(0);
  const journalList = ref([]);
  const dateRange = ref([]);

  // 列显隐信息和列宽配置
  const columns = ref([
    { key: 0, label: `期刊名称`, visible: true, width: 140 },
    { key: 1, label: `期刊简称`, visible: true, width: 120 },
    { key: 2, label: `出版社名称`, visible: true, width: 130 },
    { key: 3, label: `ISSN Print`, visible: true, width: 100 },
    { key: 4, label: `ISSN Electronic`, visible: true, width: 120 },
    { key: 5, label: `历史ISSN`, visible: false, width: 110 },
    { key: 6, label: `Unique NLM ID`, visible: false, width: 120 },
    { key: 7, label: `历史Unique NLM ID`, visible: false, width: 160 },
    { key: 8, label: `更新时间`, visible: true, width: 160 },
    { key: 9, label: `MedlineTA`, visible: false, width: 115 },
    { key: 10, label: `来源`, visible: true, width: 100 },
    { key: 11, label: `状态`, visible: true, width: 80 },
  ]);

  // 编辑弹窗
  const editDialog = ref({
    visible: false,
    loading: false,
    form: {},
  });
  const rules = {
    title: [{ required: true, message: '请输入期刊名称', trigger: 'blur' }],
  };

  // 详情弹窗
  const detailDialog = ref({
    visible: false,
    data: {},
  });

  // ISSN验证错误弹窗
  const validationErrorDialog = ref({
    visible: false,
    errorMessage: '',
    conflictData: [],
  });

  // 合并期刊相关
  const mergeDetailDialog = ref({
    visible: false,
    loading: false, // 添加加载状态
    form: {
      targetId: null, // 目标期刊ID
      sourceIds: [], // 源期刊ID列表
      title: '', // 合并后的期刊名称
      isoabbreviation: '', // 合并后的期刊简称
      publisherName: '', // 合并后的出版社名称
      publisherId: null, // 合并后的出版社ID
      issnPrint: '', // 合并后的ISSN Print
      issnElectronic: '', // 合并后的ISSN Electronic
      uniqueNlmId: '', // 合并后的Unique NLM ID
      issnHistory: [], // 合并后的历史ISSN
      uniqueHistory: [], // 合并后的历史Unique NLM ID
      medlineTa: '', // 合并后的MedlineTA
      source: [], // 合并后的来源
      scriptId: null, // 合并后的脚本ID
    },
  });

  // 记录所有已选中的期刊（合并用）
  const selectedIds = ref([]);

  function handleSelectionChange(selection) {
    selectedIds.value = selection.map(row => row.id);
  }

  // 计算是否可以进行合并（至少选择1个期刊）
  const canMerge = computed(() => selectedIds.value.length >= 1);

  /** 合并按钮操作 */
  function handleMerge() {
    if (selectedIds.value.length < 2) {
      proxy.$modal.msgWarning('请至少选择两个期刊进行合并操作');
      return;
    }

    mergeDetailDialog.value.visible = true;

    // 重置表单
    mergeDetailDialog.value.form = {
      targetId: selectedIds.value[0],
      sourceIds: selectedIds.value.slice(1),
      title: '',
      isoabbreviation: '',
      publisherName: '',
      publisherId: null,
      issnPrint: '',
      issnElectronic: '',
      uniqueNlmId: '',
      issnHistory: [],
      uniqueHistory: [],
      medlineTa: '',
      source: [],
      scriptId: null,
    };

    // 默认使用第一个选中期刊的值作为初始值
    const selectedJournals = journalList.value.filter(j =>
      selectedIds.value.includes(j.id),
    );

    if (selectedJournals.length > 0) {
      const firstJournal = selectedJournals[0];
      mergeDetailDialog.value.form.title = firstJournal.title || '';
      mergeDetailDialog.value.form.isoabbreviation =
        firstJournal.isoabbreviation || '';
      mergeDetailDialog.value.form.publisherName =
        firstJournal.publisherName || '';
      mergeDetailDialog.value.form.issnPrint = firstJournal.issnPrint || '';
      mergeDetailDialog.value.form.issnElectronic =
        firstJournal.issnElectronic || '';
      mergeDetailDialog.value.form.uniqueNlmId = firstJournal.uniqueNlmId || '';
      mergeDetailDialog.value.form.medlineTa = firstJournal.medlineTa || '';
    }
  }

  // 确保值为数组
  function ensureArray(value) {
    if (!value) return [];
    return Array.isArray(value) ? value : [value];
  }

  // 获取选中期刊的脚本选项
  function getSelectedScripts() {
    const selectedJournals = journalList.value.filter(j =>
      selectedIds.value.includes(j.id),
    );

    // 收集所有选中期刊的脚本信息，去重
    const scriptMap = new Map();
    selectedJournals.forEach(journal => {
      if (journal.scriptId && journal.scriptName) {
        scriptMap.set(journal.scriptId, {
          id: journal.scriptId,
          name: journal.scriptName,
        });
      }
    });
    return Array.from(scriptMap.values());
  }

  // 获取历史ISSN选项
  function getHistoricalIssnOptions() {
    const selectedJournals = journalList.value.filter(j =>
      selectedIds.value.includes(j.id),
    );

    // 收集所有选中期刊的历史ISSN值
    const allValues = [];
    selectedJournals.forEach(journal => {
      const values = ensureArray(journal.issnHistory); // 修正字段名
      values.forEach(value => {
        if (value && !allValues.includes(value)) {
          allValues.push(value);
        }
      });
    });
    return allValues;
  }

  // 获取历史NLM ID选项
  function getHistoricalNlmIdOptions() {
    const selectedJournals = journalList.value.filter(j =>
      selectedIds.value.includes(j.id),
    );

    // 收集所有选中期刊的历史NLM ID值
    const allValues = [];
    selectedJournals.forEach(journal => {
      const values = ensureArray(journal.uniqueHistory); // 修正字段名
      values.forEach(value => {
        if (value && !allValues.includes(value)) {
          allValues.push(value);
        }
      });
    });
    return allValues;
  }
  // 重置到第一页，并按更新时间倒序排列，确保合并后的期刊显示在第一行
  function resetToFirstPageWithTimeSort() {
    queryParams.value.pageNum = 1;
    queryParams.value.orderByColumn = 'update_time';
    queryParams.value.isAsc = 'desc';
  }

  // 批量合并获取选中期刊字段建议函数
  function getSelectedJournalSuggestions(
    fieldName,
    queryString,
    cb,
    caseSensitive = false,
  ) {
    const selectedJournals = journalList.value.filter(j =>
      selectedIds.value.includes(j.id),
    );
    const values = [
      ...new Set(
        selectedJournals
          .map(j => j[fieldName])
          .filter(value => {
            if (!value) return false;
            if (!queryString) return true;

            return caseSensitive
              ? value.includes(queryString)
              : value.toLowerCase().includes(queryString.toLowerCase());
          }),
      ),
    ].map(value => ({ value }));
    cb(values);
  }

  function submitMergeDetail() {
    // 验证必填字段
    if (!mergeDetailDialog.value.form.title) {
      proxy.$modal.msgError('请输入期刊名称');
      return;
    }

    if (
      !mergeDetailDialog.value.form.targetId ||
      mergeDetailDialog.value.form.sourceIds.length === 0
    ) {
      proxy.$modal.msgError('合并数据异常，请重新选择期刊');
      return;
    }

    // 设置加载状态
    mergeDetailDialog.value.loading = true;

    // 调用后端合并接口
    mergeJournals(mergeDetailDialog.value.form)
      .then(() => {
        proxy.$modal.msgSuccess('期刊合并成功');
        mergeDetailDialog.value.visible = false;
        // 清空选中状态
        selectedIds.value = [];

        resetToFirstPageWithTimeSort();

        // 重新加载列表
        getJournalList();
      })
      .catch(error => {
        // 检查是否是ISSN验证错误
        if (isValidationError(error)) {
          showValidationErrorDialog(error);
        } else {
          proxy.$modal.msgError('期刊合并失败');
        }
      })
      .finally(() => {
        // 无论成功还是失败都要取消加载状态
        mergeDetailDialog.value.loading = false;
      });
  }

  // 状态转换函数 - 与字典保持一致
  function statusToNumber(status) {
    // 直接返回字典值，不做转换
    return status;
  }

  function statusToText(status) {
    // 直接返回状态值，让字典组件处理显示
    return status;
  }

  // 查询期刊列表
  function getJournalList() {
    loading.value = true;

    // 处理多个期刊名称查询
    let processedTitleMultiple = queryParams.value.titleMultiple;
    if (processedTitleMultiple) {
      // 按行分割，去除每行的首尾空白，过滤掉空行
      const lines = processedTitleMultiple.split('\n').map(item => item.trim());
      const filteredLines = lines.filter(item => item !== '');
      processedTitleMultiple = filteredLines;
    }

    // 构建查询参数
    const params = {
      pageNum: queryParams.value.pageNum,
      pageSize: queryParams.value.pageSize,
      publisherId: queryParams.value.publisherId
        ? Number(queryParams.value.publisherId)
        : null, // 转换为数字
      publisherName: queryParams.value.publisher, // 添加出版社名称查询
      titleMultiple: processedTitleMultiple, // 多个期刊名称查询（处理后的数组）
      title: queryParams.value.title,
      isoabbreviation: queryParams.value.isoabbreviation,
      uniqueNlmId: queryParams.value.uniqueNlmId,
      issnPrint: queryParams.value.issnPrint,
      issnElectronic: queryParams.value.issnElectronic,
      source: queryParams.value.source,
      orderByColumn: queryParams.value.orderByColumn,
      isAsc: queryParams.value.isAsc,
      status: statusToNumber(queryParams.value.status), // 转换状态值
    };

    // 添加时间范围参数
    if (dateRange.value && dateRange.value.length === 2) {
      params.beginTime = dateRange.value[0];
      params.endTime = dateRange.value[1];
    }

    // 调试信息
    console.log('发送给后端的参数:', params);

    // 调用API
    listJournal(params)
      .then(response => {
        // 转换后端返回的状态值为前端显示的文字
        const processedRows = response.rows.map(row => ({
          ...row,
          status: statusToText(row.status),
        }));
        journalList.value = processedRows;
        total.value = response.total;
        loading.value = false;
      })
      .catch(error => {
        console.error('获取期刊列表失败:', error);
        loading.value = false;
        proxy.$modal.msgError('获取期刊列表失败');
      });
  }

  /** 排序触发事件 */
  function handleSortChange(column) {
    queryParams.value.orderByColumn = column.prop;
    queryParams.value.isAsc = column.order;
    getJournalList();
  }

  function handleQuery() {
    queryParams.value.pageNum = 1;
    getJournalList();
  }

  function resetQuery() {
    Object.keys(queryParams.value).forEach(k => {
      if (k === 'pageNum' || k === 'pageSize') return;
      queryParams.value[k] = '';
    });
    handleQuery();
  }

  // 出版社自动完成建议
  function getPublisherSuggestions(queryString, callback) {
    if (!queryString) {
      callback([]);
      return;
    }

    // 调用出版社API获取出版社列表
    listPublisher({ name: queryString })
      .then(response => {
        // 将后端返回的数据转换为el-autocomplete需要的格式
        const suggestions = response.rows.map(publisher => ({
          value: publisher.name, // 显示的值
          id: publisher.id, // 存储的ID
        }));
        callback(suggestions);
      })
      .catch(error => {
        console.error('获取出版社列表失败:', error);
        callback([]);
      });
  }

  // 处理出版社选择
  function handlePublisherSelect(item) {
    queryParams.value.publisherId = item.id; // 存储选中的出版社ID
    handleQuery(); // 自动触发查询
  }

  // 处理出版社清空
  function handlePublisherClear() {
    queryParams.value.publisherId = ''; // 清空出版社ID
    handleQuery(); // 自动触发查询
  }

  // 处理键盘事件
  function handleKeyDown(event) {
    // 如果是 Enter 键且没有按 Shift，则触发搜索
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault(); // 阻止默认的换行行为
      handleQuery();
    }
  }

  // 处理编辑表单中的出版社选择
  function handleEditPublisherSelect(item) {
    editDialog.value.form.publisherId = item.id; // 存储选中的出版社ID
  }
  // 处理编辑表单中的出版社清空
  function handleEditPublisherClear() {
    editDialog.value.form.publisherId = ''; // 清空出版社ID
  }

  // 编辑期刊
  function handleEdit(row) {
    // 通过API获取期刊详情
    const journalId = row.id || row;

    // 显示加载状态
    editDialog.value.visible = true;
    editDialog.value.loading = true;

    getJournal(journalId)
      .then(response => {
        const journal = response.data;
        // 确保数组字段是数组，并映射字段名
        const formData = {
          id: journal.id,
          title: journal.title,
          isoabbreviation: journal.isoabbreviation,
          publisherId: journal.publisherId,
          publisherName: journal.publisherName,
          issnPrint: journal.issnPrint,
          issnElectronic: journal.issnElectronic,
          uniqueNlmId: journal.uniqueNlmId,
          issnHistory: ensureArray(journal.issnHistory),
          uniqueHistory: ensureArray(journal.uniqueHistory),
          medlineTa: journal.medlineTa || '',
          source: ensureArray(journal.source),
        };
        editDialog.value.form = formData;
      })
      .catch(error => {
        console.error('获取期刊详情失败:', error);
        proxy.$modal.msgError('获取期刊详情失败');
        editDialog.value.visible = false;
      })
      .finally(() => {
        editDialog.value.loading = false;
      });
  }

  function submitEdit() {
    proxy.$refs.editFormRef.validate(valid => {
      if (!valid) return;

      // 直接提交表单数据，不包含状态字段
      const submitData = { ...editDialog.value.form };

      // 调用后端更新接口
      updateJournal(submitData)
        .then(() => {
          proxy.$modal.msgSuccess('保存成功');
          editDialog.value.visible = false;
          // 重新加载列表
          getJournalList();
        })
        .catch(error => {
          editDialog.value.visible = false;
          console.error('更新期刊失败:', error);

          // 检查是否是ISSN验证错误
          if (isValidationError(error)) {
            showValidationErrorDialog(error);
          } else {
            proxy.$modal.msgError('保存失败');
          }
        });
    });
  }

  function cancelEdit() {
    editDialog.value.visible = false;
    proxy.$refs.editFormRef?.resetFields();
  }

  function toggleStatus(item) {
    // 确定新状态和提示信息
    const currentStatus = item.status;
    const newStatusText = currentStatus === 1 ? '停用' : '启用';
    const newStatusValue = currentStatus === 1 ? 0 : 1;

    // 显示确认弹框
    proxy.$modal
      .confirm(`确定要${newStatusText}期刊"${item.title}"吗？`)
      .then(() => {
        // 用户确认后，调用专门的状态修改接口
        changeJournalStatus(item.id, newStatusValue)
          .then(() => {
            proxy.$modal.msgSuccess(`期刊已${newStatusText}`);
            // 重新加载列表
            getJournalList();
          })
          .catch(error => {
            console.error('更新状态失败:', error);
            proxy.$modal.msgError('状态更新失败');
          });
      });
  }

  // 检查是否是ISSN验证错误
  function isValidationError(error) {
    // 检查错误响应中是否包含验证错误的特征
    const errorMessage = error?.response?.data?.msg || error?.message || '';
    return (
      errorMessage.includes('字段') &&
      errorMessage.includes('已存在于期刊记录中') &&
      (errorMessage.includes('ISSN') || errorMessage.includes('Unique'))
    );
  }

  // 显示验证错误弹窗
  function showValidationErrorDialog(error) {
    const errorMessage =
      error?.response?.data?.msg || error?.message || '验证失败';

    // 解析错误消息，提取冲突信息
    const conflictData = parseValidationError(errorMessage);

    validationErrorDialog.value = {
      visible: true,
      errorMessage: errorMessage,
      conflictData: conflictData,
    };
  }

  // 解析验证错误消息，提取冲突信息
  function parseValidationError(errorMessage) {
    const conflicts = [];

    // 使用正则表达式解析错误消息
    // 格式：字段 'ISSN Print' 的值 '1234-5678' 已存在于期刊记录中 (ID: 999, 标题: Existing Journal)
    const regex =
      /字段\s+'([^']+)'\s+的值\s+'([^']+)'\s+已存在于期刊记录中\s+\(ID:\s+(\d+),\s+标题:\s+([^)]+)\)/g;

    let match;
    while ((match = regex.exec(errorMessage)) !== null) {
      conflicts.push({
        field: getFieldKey(match[1]), // 将显示名称转换为字段键
        fieldDisplay: match[1],
        value: match[2],
        conflictJournalId: match[3],
        conflictJournalTitle: match[4],
      });
    }

    // 如果正则解析失败，创建一个通用的冲突记录
    if (conflicts.length === 0) {
      conflicts.push({
        field: 'unknown',
        fieldDisplay: '未知字段',
        value: '未知值',
        conflictJournalId: '未知',
        conflictJournalTitle: '解析错误消息失败',
      });
    }

    return conflicts;
  }

  // 将字段显示名称转换为字段键
  function getFieldKey(displayName) {
    const fieldMap = {
      'ISSN Print': 'issnPrint',
      'ISSN Electronic': 'issnElectronic',
      'Unique NLM ID': 'uniqueNlmId',
      'ISSN History': 'issnHistory',
      'Unique History': 'uniqueHistory',
    };
    return fieldMap[displayName] || 'unknown';
  }

  // 获取字段显示名称
  function getFieldDisplayName(fieldKey) {
    const fieldMap = {
      issnPrint: 'ISSN Print',
      issnElectronic: 'ISSN Electronic',
      uniqueNlmId: 'Unique NLM ID',
      issnHistory: 'ISSN History',
      uniqueHistory: 'Unique History',
    };
    return fieldMap[fieldKey] || fieldKey;
  }

  // 获取字段标签类型
  function getFieldTagType(fieldKey) {
    const typeMap = {
      issnPrint: 'primary',
      issnElectronic: 'success',
      uniqueNlmId: 'warning',
      issnHistory: 'info',
      uniqueHistory: 'danger',
    };
    return typeMap[fieldKey] || 'default';
  }

  // 查看冲突期刊详情
  function viewConflictJournal(journalId) {
    // 直接通过API获取期刊详情并显示编辑弹窗
    handleEdit(journalId);
  }

  // 导出期刊冲突数据到Excel
  function exportJournalConflictData() {
    try {
      if (
        !validationErrorDialog.value.conflictData ||
        validationErrorDialog.value.conflictData.length === 0
      ) {
        proxy.$modal.msgWarning('没有冲突数据可导出');
        return;
      }

      // 准备导出数据
      const exportData = validationErrorDialog.value.conflictData.map(
        (conflict, index) => ({
          序号: index + 1,
          冲突字段:
            conflict.fieldDisplay || getFieldDisplayName(conflict.field),
          冲突值: conflict.value,
          冲突期刊ID: conflict.conflictJournalId,
          冲突期刊标题: conflict.conflictJournalTitle,
          错误描述: validationErrorDialog.value.errorMessage,
        }),
      );

      // 创建工作簿
      const wb = XLSX.utils.book_new();

      // 创建工作表
      const ws = XLSX.utils.json_to_sheet(exportData);

      // 设置列宽
      const colWidths = [
        { wch: 8 }, // 序号
        { wch: 15 }, // 冲突字段
        { wch: 20 }, // 冲突值
        { wch: 15 }, // 冲突期刊ID
        { wch: 40 }, // 冲突期刊标题
        { wch: 50 }, // 错误描述
      ];
      ws['!cols'] = colWidths;

      // 添加工作表到工作簿
      XLSX.utils.book_append_sheet(wb, ws, '期刊ISSN冲突数据');

      // 生成文件名
      const timestamp = new Date()
        .toISOString()
        .slice(0, 19)
        .replace(/[:-]/g, '')
        .replace('T', '_');
      const filename = `期刊ISSN冲突数据_${timestamp}.xlsx`;

      // 导出文件
      XLSX.writeFile(wb, filename);

      proxy.$modal.msgSuccess('冲突数据导出成功');
    } catch (error) {
      console.error('导出冲突数据失败:', error);
      proxy.$modal.msgError('导出失败，请重试');
    }
  }

  // 初始化：获取期刊列表
  getJournalList();
</script>

<style lang="scss" scoped>
  .app-container {
    padding: 10px;
    padding-bottom: 40px;
  }
  .mb8 {
    margin-bottom: 8px;
  }
  .mt10 {
    margin-top: 10px;
  }
  .search-form {
    display: flex;
    flex-direction: column;
    gap: 16px;
    .form-row {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      gap: 8px;
      .el-form-item {
        margin-bottom: 0;
        margin-right: 16px;
      }
      .search-input {
        width: 220px;
      }
      .action-btns {
        display: flex;
        gap: 8px;
        margin-left: auto;
      }
    }
  }
  .journal-list {
    margin-top: 10px;
  }
  .journal-items {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }
  .journal-item {
    display: flex;
    align-items: flex-start;
    background: #fff;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    padding: 16px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
    transition: box-shadow 0.2s;
    &:hover {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    }
    .card-checkbox {
      margin-right: 16px;
      margin-top: 4px;
    }
    .item-main {
      flex: 1;
      .item-title-row {
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 16px;
        font-weight: bold;
        color: #303133;
        margin-bottom: 6px;
        .partition,
        .impact-factor {
          font-size: 14px;
          font-weight: normal;
          color: #909399;
        }
        .source-tags {
          margin-left: 8px;
          display: flex;
          gap: 4px;
        }
      }
      .item-info-row {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
        font-size: 14px;
        color: #606266;
      }
    }
    .item-ops {
      width: 100px;
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: 8px;
      .el-button {
        padding: 4px 0;
        margin-left: 0;
      }
    }
  }
  // 父节点背景色
  @for $i from 0 through 5 {
    .el-table__row.merge-row-bg-#{$i} > td {
      background: nth(
        (#f6ffed, #e6f7ff, #fffbe6, #fff0f6, #f9f0ff, #f0f5ff),
        $i + 1
      ) !important;
    }
  }

  // 表格内容居中对齐
  :deep(.el-table) {
    .el-table__header th .cell,
    .el-table__body td .cell {
      text-align: center !important;
    }
  }

  // 文本省略样式
  .text-ellipsis {
    display: inline-block;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;
  }

  .journal-detail {
    .detail-row {
      margin-bottom: 8px;
      font-size: 15px;
      .label {
        color: #909399;
        margin-right: 8px;
      }
    }
  }

  .more-icon {
    top: 2px;
    margin-left: 4px;
    font-size: 14px;
    color: #7c9eff;
    cursor: pointer;
  }

  /* 增强表格列边框和可拖拽效果 */
  .journal-list :deep(.el-table) {
    border-collapse: separate;
    border-spacing: 0;
  }

  /* 添加列分隔线 - 表头 */
  .journal-list :deep(.el-table__header-wrapper .el-table__header thead tr th) {
    border-right: 1px solid #dcdfe6 !important;
    position: relative;
  }

  .journal-list
    :deep(.el-table__header-wrapper .el-table__header thead tr th:last-child) {
    border-right: none !important;
  }

  /* 添加列分隔线 - 表体 */
  .journal-list :deep(.el-table__body-wrapper .el-table__body tbody tr td) {
    border-right: 1px solid #ebeef5 !important;
  }

  .journal-list
    :deep(.el-table__body-wrapper .el-table__body tbody tr td:last-child) {
    border-right: none !important;
  }

  /* 固定列的分隔线 */
  .journal-list :deep(.el-table__fixed-right) {
    border-left: 1px solid #dcdfe6 !important;
  }

  /* 增强列拖拽手柄的视觉效果 */
  .journal-list :deep(.el-table__header-wrapper .el-table__header th:hover) {
    background-color: #f5f7fa;
  }

  /* 拖拽手柄样式 */
  .journal-list :deep(.el-table .el-table__border-line) {
    background-color: #409eff !important;
    width: 2px !important;
  }

  .journal-list :deep(.el-table .el-table__border-line:hover) {
    background-color: #66b1ff !important;
  }

  /* ISSN验证错误弹窗样式 */
  .validation-error-content {
    .mb-16 {
      margin-bottom: 16px;
    }

    .conflict-table {
      margin-bottom: 20px;

      .conflict-value {
        font-weight: bold;
        font-family: 'Courier New', monospace;
      }
    }

    .validation-tips {
      background-color: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 4px;
      padding: 16px;
      margin-top: 16px;

      h4 {
        margin: 0 0 12px 0;
        color: #495057;
        font-size: 14px;
        font-weight: 600;
      }

      ul {
        margin: 0;
        padding-left: 20px;

        li {
          margin-bottom: 8px;
          color: #6c757d;
          font-size: 13px;
          line-height: 1.5;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }
</style>
