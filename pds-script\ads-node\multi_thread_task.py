# coding=utf8
__author__ = 'sw'

import logging
import queue
import threading
from collections import defaultdict
from concurrent.futures import Thread<PERSON>oolExecutor

import api
import db
from task_executor import TaskExecutor

logger = logging.getLogger()


class MultiThreadTaskProcessor(threading.Thread, TaskExecutor):
    """
    多线程任务处理器
    支持期刊类型站点(currSiteType='2'或'3')多线程并发下载
    最多同时处理5个不同脚本ID的任务，避免同一脚本ID多线程导致IP封禁
    确保同一脚本ID两次执行之间存在睡眠时间
    """

    def __init__(self, site_id: int, db_file: db.DbFile, api_invoker: api.ApiInvoker):
        threading.Thread.__init__(self, daemon=True)
        TaskExecutor.__init__(self, site_id, db_file, api_invoker)
        self.__stop = False

        # 多线程相关
        self.__max_workers = 5  # 最多5个并发线程
        self.__executor = ThreadPoolExecutor(max_workers=self.__max_workers, thread_name_prefix="TaskWorker")
        self.__active_script_ids = set()  # 当前正在处理的脚本ID集合
        self.__active_script_ids_lock = threading.Lock()  # 保护active_script_ids的锁
        self.__script_sleep_times = {}  # 记录每个脚本ID的睡眠结束时间
        self.__script_sleep_lock = threading.Lock()  # 保护script_sleep_times的锁
        self.__task_queue = queue.Queue(maxsize=8)  # 任务队列

        # 统计信息
        self.__stats = {
            'total_tasks': 0,
            'success_tasks': 0,
            'failed_tasks': 0,
            'concurrent_scripts': 0
        }
        self.__stats_lock = threading.Lock()

    def run(self) -> None:
        """主线程：负责获取任务并分发到线程池"""
        logger.info(f"多线程任务处理器启动，站点: {self._site_id}")

        while True:
            if self.__stop:
                logger.info(f"任务调度线程已停止, 站点: {self._site_id}")
                self.__executor.shutdown(wait=True)
                return

            try:
                # 检查任务队列是否已满，如果满了就不再获取新任务
                if self.__task_queue.qsize() >= self.__task_queue.maxsize * 0.8:  # 队列使用率超过80%
                    logger.warning(
                        f"任务队列使用率过高 ({self.__task_queue.qsize()}/{self.__task_queue.maxsize})，暂停获取新任务")
                    self.safe_sleep(15)  # 等待15秒后再检查
                    continue

                # 获取当前活跃的脚本ID列表
                with self.__active_script_ids_lock:
                    active_script_ids = list(self.__active_script_ids)

                # 获取下一个任务，传递活跃脚本ID列表
                logger.warning(f"获取下一个任务，当前活跃脚本ID: {active_script_ids}")
                task_info = self._api_invoker.next_task_info(active_script_ids)

                # 解析任务状态
                task_status = task_info.get("status", "")

                # 没有任务了就休眠一会儿
                if "none" == task_status:
                    sleep_time = 30
                    logger.info(f"服务端没有任务，休眠{sleep_time}秒后重试")
                    self.safe_sleep(sleep_time)
                    continue

                # 获取任务失败了
                if str(task_status).strip().lower() != "success":
                    logger.info(f"获取任务失败 站点：{self._site_id} Task: {task_info}")
                    continue

                # 验证任务信息
                is_valid, error_msg = self.validate_task_info(task_info)
                if not is_valid:
                    logger.warning(f"获取任务失败 {error_msg}, Task: {task_info}")
                    continue

                # 全部使用多线程处理
                self.__submit_task_to_thread_pool(task_info)

            except BaseException as e:
                if 'task_info' in locals():
                    self.handle_task_error(task_info, e, "多线程")

    def __submit_task_to_thread_pool(self, task_info):
        """将任务提交到线程池处理"""
        script_id = task_info.get("scriptIdStr", "unknown")
        task_id = task_info.get("taskId", "unknown")
        journal_id = task_info.get("journalId", "unknown")

        # 检查脚本ID是否还在睡眠期
        if self.__is_script_sleeping(script_id):
            sleep_remaining = self.__get_script_sleep_remaining(script_id)
            logger.info(f"脚本ID {script_id} 还在睡眠期，剩余 {sleep_remaining:.1f}s，任务等待")
            self.__put_task_to_queue(task_info)
            return

        with self.__active_script_ids_lock:
            # 检查该脚本ID是否已经在处理中
            if script_id in self.__active_script_ids:
                logger.info(f"脚本ID {script_id} 已有任务在处理中，当前任务等待")
                # 将任务放入队列等待，使用阻塞方式确保任务不丢失
                self.__put_task_to_queue(task_info)
                return

            # 检查是否达到最大并发数
            if len(self.__active_script_ids) >= self.__max_workers:
                logger.info(f"已达到最大并发数 {self.__max_workers}，任务等待")
                # 将任务放入队列等待，使用阻塞方式确保任务不丢失
                self.__put_task_to_queue(task_info)
                return

            # 标记脚本ID为处理中
            self.__active_script_ids.add(script_id)

        # 提交任务到线程池
        future = self.__executor.submit(self.__execute_task_multi_thread, task_info)

        # 添加完成回调
        future.add_done_callback(lambda f: self.__task_completed(script_id, f))

        with self.__stats_lock:
            self.__stats['total_tasks'] += 1
            self.__stats['concurrent_scripts'] = len(self.__active_script_ids)

        logger.info(
            f"任务 {task_id} (期刊:{journal_id}, 脚本ID:{script_id}) 已提交到线程池，当前并发数: {len(self.__active_script_ids)}")

    def __put_task_to_queue(self, task_info):
        """将任务放入队列，使用阻塞方式确保任务不丢失"""
        task_id = task_info.get("taskId", "unknown")

        while True:
            try:
                # 使用较短的超时时间，避免长时间阻塞
                self.__task_queue.put(task_info, timeout=5)
                logger.info(f"任务 {task_id} 已加入等待队列，队列长度: {self.__task_queue.qsize()}")
                break
            except queue.Full:
                if self.__stop:
                    logger.warning(f"系统正在停止，放弃任务 {task_id}")
                    break
                logger.warning(f"任务队列已满，等待5秒后重试，任务: {task_id}")
                self.safe_sleep(5)

    def __is_script_sleeping(self, script_id):
        """检查脚本ID是否还在睡眠期"""
        import time
        with self.__script_sleep_lock:
            if script_id in self.__script_sleep_times:
                return time.time() < self.__script_sleep_times[script_id]
            return False

    def __get_script_sleep_remaining(self, script_id):
        """获取脚本ID剩余睡眠时间"""
        import time
        with self.__script_sleep_lock:
            if script_id in self.__script_sleep_times:
                remaining = self.__script_sleep_times[script_id] - time.time()
                return max(0, remaining)
            return 0

    def __set_script_sleep_time(self, script_id, sleep_seconds):
        """设置脚本ID的睡眠时间"""
        import time
        with self.__script_sleep_lock:
            self.__script_sleep_times[script_id] = time.time() + sleep_seconds
            logger.debug(f"设置脚本ID {script_id} 睡眠 {sleep_seconds}s")

    def __cleanup_script_id(self, script_id):
        """清理脚本ID，确保从active_script_ids中移除"""
        with self.__active_script_ids_lock:
            if script_id in self.__active_script_ids:
                self.__active_script_ids.discard(script_id)
                logger.debug(f"清理脚本ID: {script_id}，当前活跃脚本数: {len(self.__active_script_ids)}")

    def __task_completed(self, script_id, future):
        """任务完成回调"""
        # 清理脚本ID（双重保险，防止遗漏）
        self.__cleanup_script_id(script_id)

        # 检查是否有等待的任务
        self.__process_queued_tasks()

        # 更新统计
        with self.__stats_lock:
            self.__stats['concurrent_scripts'] = len(self.__active_script_ids)
            try:
                result = future.result()
                if result:
                    self.__stats['success_tasks'] += 1
                else:
                    self.__stats['failed_tasks'] += 1
            except Exception as e:
                self.__stats['failed_tasks'] += 1
                logger.exception(f"任务执行异常: {e}")

        logger.info(f"脚本ID {script_id} 任务完成，当前活跃脚本数: {len(self.__active_script_ids)}")

    def __process_queued_tasks(self):
        """处理队列中等待的任务"""
        processed_count = 0
        max_process_count = 10  # 限制每次最多处理10个任务，避免长时间占用

        while not self.__task_queue.empty() and processed_count < max_process_count:
            try:
                task_info = self.__task_queue.get_nowait()
                script_id = task_info.get("scriptIdStr", "unknown")
                task_id = task_info.get("taskId", "unknown")
                journal_id = task_info.get("journalId", "unknown")

                # 检查脚本ID是否还在睡眠期
                if self.__is_script_sleeping(script_id):
                    # 还在睡眠期，放回队列头部
                    self.__put_task_to_queue_front(task_info)
                    break

                with self.__active_script_ids_lock:
                    if (script_id not in self.__active_script_ids and
                            len(self.__active_script_ids) < self.__max_workers):
                        # 可以处理这个任务
                        self.__active_script_ids.add(script_id)
                        future = self.__executor.submit(self.__execute_task_multi_thread, task_info)
                        future.add_done_callback(lambda f: self.__task_completed(script_id, f))

                        with self.__stats_lock:
                            self.__stats['total_tasks'] += 1
                            self.__stats['concurrent_scripts'] = len(self.__active_script_ids)

                        logger.info(f"从队列处理任务 {task_id} (期刊:{journal_id}, 脚本ID:{script_id})")
                        processed_count += 1
                    else:
                        # 还不能处理，放回队列头部（优先处理）
                        self.__put_task_to_queue_front(task_info)
                        break
            except queue.Empty:
                break

    def __put_task_to_queue_front(self, task_info):
        """将任务放回队列头部，优先处理"""
        task_id = task_info.get("taskId", "unknown")

        # 创建一个临时队列来实现队列头部插入
        temp_queue = queue.Queue(maxsize=self.__task_queue.maxsize)
        temp_queue.put(task_info)  # 先放入要优先处理的任务

        # 将原队列中的任务转移到临时队列
        while not self.__task_queue.empty():
            try:
                item = self.__task_queue.get_nowait()
                temp_queue.put(item)
            except (queue.Empty, queue.Full):
                break

        # 将临时队列中的任务转移回原队列
        while not temp_queue.empty():
            try:
                item = temp_queue.get_nowait()
                self.__task_queue.put(item, timeout=1)
            except (queue.Empty, queue.Full):
                break

        logger.debug(f"任务 {task_id} 已放回队列头部")

    def __execute_task_multi_thread(self, task):
        """多线程环境下执行单个任务"""
        script_id = task.get("scriptIdStr", "unknown")
        self.log_task_start(task, f"[线程-{threading.current_thread().name}] ")

        try:
            # 执行任务的核心逻辑
            result = self.execute_task_core(task)

            # 计算睡眠时间并设置脚本睡眠期
            sleep_time = self.calculate_sleep_time(task, is_multi_thread=True)
            self.__set_script_sleep_time(script_id, sleep_time)
            logger.info(f"[线程-{threading.current_thread().name}] 任务执行完成，脚本ID {script_id} 将睡眠 {sleep_time}s")

            self.log_task_end(task, result, f"[线程-{threading.current_thread().name}] ")
            return result

        except Exception as e:
            self.handle_task_error(task, e, f"[线程-{threading.current_thread().name}] 多线程")
            # 即使失败也要设置睡眠时间，避免频繁重试
            sleep_time = self.calculate_sleep_time(task, is_multi_thread=True)
            self.__set_script_sleep_time(script_id, sleep_time)
            logger.info(f"[线程-{threading.current_thread().name}] 任务执行失败，脚本ID {script_id} 将睡眠 {sleep_time}s")
            return False
        finally:
            # 确保在任何情况下都清理active_script_ids
            self.__cleanup_script_id(script_id)

    # 删除单线程执行方法和__task_end方法，因为现在全部使用多线程处理
    # 睡眠时间通过脚本ID级别的睡眠管理来实现

    def get_stats(self):
        """获取统计信息"""
        with self.__stats_lock:
            stats = self.__stats.copy()

        # 添加队列状态信息
        stats['queue_size'] = self.__task_queue.qsize()
        stats['queue_max_size'] = self.__task_queue.maxsize
        stats['queue_usage_rate'] = round(self.__task_queue.qsize() / self.__task_queue.maxsize * 100, 2)

        with self.__active_script_ids_lock:
            stats['active_script_ids'] = list(self.__active_script_ids)

        return stats

    def get_queue_status(self):
        """获取队列详细状态"""
        return {
            'current_size': self.__task_queue.qsize(),
            'max_size': self.__task_queue.maxsize,
            'usage_rate': round(self.__task_queue.qsize() / self.__task_queue.maxsize * 100, 2),
            'is_full': self.__task_queue.full(),
            'is_empty': self.__task_queue.empty()
        }

    def stop(self):
        """停止任务处理器"""
        self.__stop = True

        # 清空任务队列，避免新任务继续处理
        while not self.__task_queue.empty():
            try:
                task_info = self.__task_queue.get_nowait()
                task_id = task_info.get("taskId", "unknown")
                logger.info(f"系统停止，丢弃队列中的任务: {task_id}")
            except queue.Empty:
                break

        # 清空所有活跃脚本ID，确保下次启动时状态干净
        with self.__active_script_ids_lock:
            if self.__active_script_ids:
                logger.info(f"系统停止，清理所有活跃脚本ID: {list(self.__active_script_ids)}")
                self.__active_script_ids.clear()

        # 清空所有脚本睡眠时间记录
        with self.__script_sleep_lock:
            if self.__script_sleep_times:
                logger.info(f"系统停止，清理所有脚本睡眠时间记录: {list(self.__script_sleep_times.keys())}")
                self.__script_sleep_times.clear()

        self.__executor.shutdown(wait=False)
