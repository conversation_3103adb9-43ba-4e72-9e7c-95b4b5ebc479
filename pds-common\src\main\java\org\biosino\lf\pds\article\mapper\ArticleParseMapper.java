package org.biosino.lf.pds.article.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.biosino.lf.pds.article.domain.ArticleParse;
import org.biosino.lf.pds.article.dto.ArticleParseDTO;

import java.util.List;

/**
 * 文献解析元数据表 数据层
 *
 * <AUTHOR>
 */
@Mapper
public interface ArticleParseMapper extends BaseMapper<ArticleParse> {

    List<ArticleParse> selectParseList(ArticleParseDTO articleParseDTO);

}