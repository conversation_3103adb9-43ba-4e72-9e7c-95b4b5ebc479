package org.biosino.lf.pds.article.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.biosino.lf.pds.article.domain.Article;
import org.biosino.lf.pds.article.dto.ArticleQueryDTO;

import java.util.List;

/**
 * 文章信息表 Mapper 接口
 */
@Mapper
public interface ArticleMapper extends CommonMapper<Article> {

    /**
     * 查询文献列表
     *
     * @param queryDTO 查询参数
     * @return 文献列表
     */
    List<Article> selectArticleList(ArticleQueryDTO queryDTO);

    /**
     * 获取文献详情
     *
     * @param id 文献ID
     * @return 文献详情
     */
    Article selectArticleById(Long id);

    List<Article> searchArticleNotInTaskPaper(@Param("dateBefore") String dateBefore, @Param("wosQuartile") String wosQuartile, @Param("limitNum") int limitNum);

    /**
     * 批量更新文献的期刊ID（用于期刊合并）
     *
     * @param targetJournalId  目标期刊ID
     * @param sourceJournalIds 源期刊ID列表
     */
    void updateJournalIdBatch(Long targetJournalId, List<Long> sourceJournalIds);
}
