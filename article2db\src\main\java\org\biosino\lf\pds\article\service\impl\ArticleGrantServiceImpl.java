package org.biosino.lf.pds.article.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.biosino.lf.pds.article.domain.ArticleGrant;
import org.biosino.lf.pds.article.mapper.ArticleGrantMapper;
import org.biosino.lf.pds.article.service.IArticleGrantService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 文章资助关联表 服务实现类
 */
@Service
public class ArticleGrantServiceImpl extends ServiceImpl<ArticleGrantMapper, ArticleGrant> implements IArticleGrantService {
    @Override
    public List<ArticleGrant> findByDocId(Long pmid) {
        return this.list(
                Wrappers.<ArticleGrant>lambdaQuery()
                        .eq(ArticleGrant::getDocId, pmid)
        );
    }

    @Override
    public boolean removeByDocId(Long docId) {
        return this.remove(
                Wrappers.<ArticleGrant>lambdaQuery()
                        .eq(ArticleGrant::getDocId, docId)
        );
    }
}
