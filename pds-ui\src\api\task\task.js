import request from '@/utils/request';

/**
 * 发布文献传递任务
 * @param {Object} data 任务数据
 * @returns {Promise} 返回任务发布结果
 */
export function publishTask(data) {
  return request({
    url: '/task/publishTask',
    method: 'post',
    data: data,
  });
}

// 查询任务列表
export function listTask(query) {
  return request({
    url: '/task/searchTaskTrackList',
    method: 'get',
    params: query,
  });
}

// 获取所有PDS用户列表
export function getAllPdsUsers() {
  return request({
    url: '/task/allPdsUsers',
    method: 'get',
  });
}

// 获取所有PDS站点列表
export function getAllPdsSites() {
  return request({
    url: '/task/allPdsSites',
    method: 'get',
  });
}

// 获取所有任务状态
export function allPaperStatus() {
  return request({
    url: '/task/allPaperStatus',
    method: 'get',
  });
}

export function taskArticleViewPage(query) {
  return request({
    url: '/task/taskArticleViewPage',
    method: 'get',
    params: query,
  });
}

// 获取文献详情
export function getArticle(id) {
  return request({
    url: `/article/${id}`,
    method: 'get',
  });
}

// 删除文件
export function deleteAttachment(fileId) {
  return request({
    url: `/article/file/deleteAttachment/${fileId}`,
    method: 'delete',
  });
}

// 获取任务日志
export function getTaskLogs(taskId) {
  return request({
    url: `/task/logs/${taskId}`,
    method: 'get',
  });
}

// 暂停、恢复任务
export function updateTaskStatus(taskId, status) {
  return request({
    url: `/task/updateTaskStatus?taskId=${taskId}&status=${status}`,
    method: 'get',
  });
}

// 下载任务结果文件
export function downloadTaskArticleAttachment(taskId) {
  return request({
    url: `/task/downloadTaskArticleAttachment?taskId=${taskId}`,
    method: 'get',
  });
}

// 下载任务结果文件
export function deleteTask(taskId) {
  return request({
    url: `/task/deleteTask?taskId=${taskId}`,
    method: 'get',
  });
}
