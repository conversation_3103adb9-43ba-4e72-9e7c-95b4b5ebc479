package org.biosino.lf.pds.article.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.biosino.lf.pds.article.domain.TbDdsTaskLink;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
public interface TbDdsTaskLinkMapper extends CommonMapper<TbDdsTaskLink> {
    void deleteByTaskId(@Param("taskId") String taskId);
}
