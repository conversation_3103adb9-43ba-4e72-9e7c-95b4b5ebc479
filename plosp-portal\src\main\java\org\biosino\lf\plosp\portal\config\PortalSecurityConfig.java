package org.biosino.lf.plosp.portal.config;


import org.biosino.lf.pds.framework.security.handle.AuthenticationEntryPointImpl;
import org.biosino.lf.pds.framework.security.handle.LogoutSuccessHandlerImpl;
import org.biosino.lf.plosp.portal.security.filter.PortalJwtAuthenticationTokenFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.authentication.logout.LogoutFilter;
import org.springframework.web.filter.CorsFilter;

/**
 * 门户安全配置
 * 专门为门户应用程序配置安全设置
 */
@Configuration
@EnableWebSecurity
@EnableMethodSecurity(securedEnabled = true)
public class PortalSecurityConfig {

    /**
     * 认证失败处理类
     */
    @Autowired
    private AuthenticationEntryPointImpl unauthorizedHandler;

    /**
     * 退出处理类
     */
    @Autowired
    private LogoutSuccessHandlerImpl logoutSuccessHandler;

    /**
     * 门户token认证过滤器
     */
    @Autowired
    private PortalJwtAuthenticationTokenFilter authenticationTokenFilter;

    /**
     * 跨域过滤器 - 使用框架提供的
     */
    @Autowired
    private CorsFilter corsFilter;

    /**
     * Portal Security Filter Chain
     * <p>
     * anyRequest          |   匹配所有请求路径
     * access              |   SpringEl表达式结果为true时可以访问
     * anonymous           |   匿名可以访问
     * denyAll             |   用户不能访问
     * fullyAuthenticated  |   用户完全认证可以访问（非remember-me下自动登录）
     * hasAnyAuthority     |   如果有参数，参数表示权限，则其中任何一个权限可以访问
     * hasAnyRole          |   如果有参数，参数表示角色，则其中任何一个角色可以访问
     * hasAuthority        |   如果有参数，参数表示权限，则其权限可以访问
     * hasIpAddress        |   如果有参数，参数表示IP地址，如果用户IP和参数匹配，则可以访问
     * hasRole             |   如果有参数，参数表示角色，则其角色可以访问
     * permitAll           |   用户可以任意访问
     * rememberMe          |   允许通过remember-me登录的用户访问
     * authenticated       |   用户登录后可访问
     */
    @Bean("portalSecurityFilterChain")
    @Order(1)
    public SecurityFilterChain portalFilterChain(HttpSecurity httpSecurity) throws Exception {
        return httpSecurity
                // 只匹配门户相关的请求路径
                .securityMatcher("/api/**", "/swagger-ui/**", "/v3/api-docs/**", "/swagger-resources/**", "/webjars/**", "/druid/**")
                // CSRF禁用，因为不使用session
                .csrf(csrf -> csrf.disable())
                // CORS配置 - 使用框架提供的配置
                .cors(cors -> cors.disable())
                // 认证失败处理类
                .exceptionHandling(exception -> exception.authenticationEntryPoint(unauthorizedHandler))
                // 基于token，所以不需要session
                .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
                // 门户特定的授权配置
                .authorizeHttpRequests((requests) -> {
                    // Portal authentication endpoints - 允许匿名访问
                    requests.requestMatchers("/api/auth/login", "/api/auth/register", "/api/auth/send-verification-code",
                                    "/api/auth/verify-email", "/api/auth/forgot-password", "/api/auth/reset-password").permitAll()
                            // Public API endpoints - 允许匿名访问
                            .requestMatchers("/api/public/**").permitAll()
                            // API documentation - 允许匿名访问
                            .requestMatchers("/swagger-ui/**", "/v3/api-docs/**", "/swagger-resources/**", "/webjars/**").permitAll()
                            // Database monitoring - 允许匿名访问
                            .requestMatchers("/druid/**").permitAll()
                            // 其他API请求需要认证
                            .anyRequest().authenticated();
                })
                // 添加Logout filter
                .logout(logout -> logout.logoutUrl("/api/auth/logout").logoutSuccessHandler(logoutSuccessHandler))
                // 添加JWT filter
                .addFilterBefore(authenticationTokenFilter, UsernamePasswordAuthenticationFilter.class)
                // 添加CORS filter - 使用框架提供的
                .addFilterBefore(corsFilter, PortalJwtAuthenticationTokenFilter.class)
                .addFilterBefore(corsFilter, LogoutFilter.class)
                .build();
    }

}
