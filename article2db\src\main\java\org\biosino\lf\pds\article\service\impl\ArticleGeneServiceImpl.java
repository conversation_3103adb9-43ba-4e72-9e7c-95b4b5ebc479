package org.biosino.lf.pds.article.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.biosino.lf.pds.article.domain.ArticleChemical;
import org.biosino.lf.pds.article.domain.ArticleGene;
import org.biosino.lf.pds.article.mapper.ArticleGeneMapper;
import org.biosino.lf.pds.article.service.IArticleGeneService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 文章基因关联表 服务实现类
 */
@Service
public class ArticleGeneServiceImpl extends ServiceImpl<ArticleGeneMapper, ArticleGene> implements IArticleGeneService {
    @Override
    public List<ArticleGene> findByDocId(Long pmid) {
        return this.list(
                Wrappers.<ArticleGene>lambdaQuery()
                        .eq(ArticleGene::getDocId, pmid)
        );
    }

    @Override
    public boolean removeByDocId(Long docId) {
        return this.remove(
                Wrappers.<ArticleGene>lambdaQuery()
                        .eq(ArticleGene::getDocId, docId)
        );
    }
}
