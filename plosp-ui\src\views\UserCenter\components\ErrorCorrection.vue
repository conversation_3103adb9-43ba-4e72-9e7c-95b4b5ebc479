<template>
  <div class="error-correction-page">
    <div class="page-content">
      <!-- 表格区域 -->
      <div class="table-section">
        <el-card class="table-card">
          <template #header>
            <div class="card-header">
              <h3 class="section-title">
                <el-icon><EditPen /></el-icon>
                纠错记录
              </h3>
              <div class="table-info">
                共 {{ total }} 条记录
              </div>
            </div>
          </template>
          <div class="search-section">
            <el-form :model="searchForm" class="search-form" :inline="true">
              <el-form-item label="文献编号" class="form-item">
                <el-input
                    v-model="searchForm.articleId"
                    placeholder="请输入文献编号"
                    clearable
                    class="search-input"
                />
              </el-form-item>

              <el-form-item label="纠错类型" class="form-item">
                <el-select
                    v-model="searchForm.correctionType"
                    placeholder="请选择纠错类型"
                    clearable
                    class="search-input"
                >
                  <el-option label="全部" value="" />
                  <el-option label="标题" value="title" />
                  <el-option label="作者" value="author" />
                  <el-option label="机构" value="institution" />
                  <el-option label="期刊信息" value="journal" />
                  <el-option label="DOI" value="doi" />
                  <el-option label="PMID" value="pmid" />
                </el-select>
              </el-form-item>

              <el-form-item label="状态" class="form-item">
                <el-select
                    v-model="searchForm.status"
                    placeholder="请选择状态"
                    clearable
                    class="search-input"
                >
                  <el-option label="全部" value="" />
                  <el-option label="待处理" value="pending" />
                  <el-option label="已处理" value="processed" />
                  <el-option label="被驳回" value="rejected" />
                </el-select>
              </el-form-item>

              <el-form-item label="纠错时间" class="form-item date-range">
                <el-date-picker
                    v-model="searchForm.correctionTimeRange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="起始时间"
                    end-placeholder="截止时间"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    class="search-input date-picker"
                />
              </el-form-item>

              <el-form-item class="form-item search-actions">
                <el-button type="primary" @click="handleSearch" class="search-btn">
                  <el-icon class="mr-1"><Search /></el-icon>
                  搜索
                </el-button>
                <el-button @click="handleReset" class="reset-btn">
                  <el-icon class="mr-1"><Refresh /></el-icon>
                  重置
                </el-button>
              </el-form-item>
            </el-form>
          </div>

          <el-table
              :data="tableData"
              v-loading="loading"
              class="correction-table"
              stripe
              border
              empty-text="暂无数据"
          >
            <el-table-column
                prop="articleId"
                label="文献编号"
                width="140"
                show-overflow-tooltip
            >
            </el-table-column>

            <el-table-column
                prop="title"
                label="文献标题"
                min-width="300"
                show-overflow-tooltip
            >
            </el-table-column>

            <el-table-column
                prop="correctionType"
                label="纠错类型"
                width="120"
                align="center"
            >
            </el-table-column>

            <el-table-column
                prop="correctionTime"
                label="纠错时间"
                width="160"
                sortable
            >
            </el-table-column>
            <el-table-column
                prop="rejectReason"
                label="驳回原因"
                min-width="250"
                show-overflow-tooltip
            >
              <template #default="{ row }">
                <span v-if="row.status === 'rejected'">
                  {{ row.rejectReason }}
                </span>
                <span v-else class="no-reason">-</span>
              </template>
            </el-table-column>
            <el-table-column
                prop="status"
                label="状态"
                width="120"
                align="center"
            >
              <template #default="{ row }">
                <el-tag
                    :type="getStatusTagType(row.status)"
                    class="status-tag"
                >
                  {{ getStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>


          </el-table>

          <!-- 分页 -->
          <div class="pagination-wrapper">
            <el-pagination
                v-model:current-page="pagination.currentPage"
                v-model:page-size="pagination.pageSize"
                :page-sizes="[10, 20, 50, 100]"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                class="pagination"
            />
          </div>
        </el-card>
      </div>
    </div>


  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import {
  Search,
  Refresh,
  EditPen
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// 响应式数据
const loading = ref(false)
const total = ref(0)

// 搜索表单
const searchForm = reactive({
  articleId: '',
  correctionType: '',
  status: '',
  correctionTimeRange: null
})

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 20
})

// 表格数据
const tableData = ref([
  {
    id: 1,
    articleId: '30715263',
    title: '新型冠状病毒感染中医药防治的临床研究进展及其机制分析',
    correctionType: 'title',
    correctionTime: '2025-01-15 14:30',
    status: 'pending'
  },
  {
    id: 2,
    articleId: '30715264',
    title: 'PD-1/PD-L1免疫检查点抑制剂在非小细胞肺癌中的应用及其耐药机制',
    correctionType: 'author',
    correctionTime: '2025-01-14 16:45',
    status: 'processed'
  },
  {
    id: 3,
    articleId: '30715265',
    title: '基于深度学习的医学影像诊断技术在早期癌症检测中的应用研究',
    correctionType: 'doi',
    correctionTime: '2025-01-13 10:20',
    status: 'rejected',
    rejectTime: '2025-01-14 09:15',
    rejectReason: '提交的DOI信息经核实后确认无误，原文献DOI信息正确。建议重新核对您提交的纠错内容。'
  },
  {
    id: 4,
    articleId: '30715266',
    title: '机器学习在药物发现中的应用：当前挑战与未来展望',
    correctionType: 'journal',
    correctionTime: '2025-01-12 09:15',
    status: 'processed'
  },
  {
    id: 5,
    articleId: '30715267',
    title: '气候变化对全球农业生产力的影响：基于卫星数据的综合评估',
    correctionType: 'pmid',
    correctionTime: '2025-01-11 15:30',
    status: 'rejected',
    rejectTime: '2025-01-12 11:20',
    rejectReason: '经查证，该文献的PMID信息准确无误。您提交的纠错信息与数据库记录不符，请重新确认后再次提交。'
  },
  {
    id: 6,
    articleId: '30715268',
    title: '量子计算在密码学中的应用：当前挑战与未来机遇',
    correctionType: 'institution',
    correctionTime: '2025-01-10 11:45',
    status: 'pending'
  },
  {
    id: 7,
    articleId: '30715269',
    title: '纳米材料在生物医学中的应用及其安全性评估',
    correctionType: 'author',
    correctionTime: '2025-01-09 13:20',
    status: 'processed'
  },
  {
    id: 8,
    articleId: '30715270',
    title: '人工智能在精准医疗中的应用现状与发展趋势',
    correctionType: 'title',
    correctionTime: '2025-01-08 16:10',
    status: 'rejected',
    rejectTime: '2025-01-09 10:30',
    rejectReason: '文献标题信息经多方数据源验证后确认正确。您提交的标题修改建议与官方发布信息不一致。'
  },
  {
    id: 9,
    articleId: '30715271',
    title: '基因编辑技术CRISPR-Cas9在疾病治疗中的最新进展',
    correctionType: 'journal',
    correctionTime: '2025-01-07 14:25',
    status: 'pending'
  },
  {
    id: 10,
    articleId: '30715272',
    title: '可再生能源技术的发展现状及其对环境的影响评估',
    correctionType: 'doi',
    correctionTime: '2025-01-06 10:15',
    status: 'processed'
  }
])

// 方法
const handleSearch = () => {
  ElMessage.success('搜索功能待实现')
}

const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    if (key === 'correctionTimeRange') {
      searchForm[key] = null
    } else {
      searchForm[key] = ''
    }
  })
  ElMessage.success('重置成功')
}
// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    pending: '待处理',
    processed: '已处理',
    rejected: '被驳回'
  }
  return statusMap[status] || status
}

// 获取状态标签类型
const getStatusTagType = (status) => {
  const statusMap = {
    pending: 'warning',
    processed: 'success',
    rejected: 'danger'
  }
  return statusMap[status] || ''
}



// 初始化
onMounted(() => {
  total.value = tableData.value.length
})
</script>

<style lang="scss" scoped>
@import "@/assets/styles/variables";

.error-correction-page {
  padding: 0;
}

.page-content {
  display: flex;
  flex-direction: column;
  gap: $spacing-lg;
}

// 通用卡片样式
.search-card,
.table-card {
  background: white;
  border-radius: $border-radius-lg;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04), 0 1px 4px rgba(0, 0, 0, 0.02);
  border: 1px solid rgba(226, 232, 240, 0.5);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.06), 0 2px 8px rgba(0, 0, 0, 0.04);
    transform: translateY(-1px);
  }

  :deep(.el-card__header) {
    padding: $spacing-md $spacing-lg;
    border-bottom: 1px solid rgba(226, 232, 240, 0.6);
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.8), rgba(241, 245, 249, 0.6));
  }

  :deep(.el-card__body) {
    padding: $spacing-lg;
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .section-title {
    display: flex;
    align-items: center;
    gap: $spacing-xs;
    font-size: 18px;
    font-weight: $font-weight-bold;
    color: $primary-color;
    margin: 0;

    .el-icon {
      font-size: 28px;
      background: linear-gradient(135deg, $primary-color, #2563eb);
      color: white;
      padding: 6px;
      border-radius: 50%;
      box-shadow: 0 2px 8px rgba(4, 56, 115, 0.2);
    }
  }

  .table-info {
    font-size: $font-size-small;
    color: #6b7280;
    font-weight: $font-weight-medium;
  }
}

// 搜索表单样式
.search-form {
  .form-item {
    margin-bottom: $spacing-md;
    margin-right: $spacing-lg;

    :deep(.el-form-item__label) {
      font-weight: $font-weight-medium;
      color: $gray;
      font-size: $font-size-small;
      width: 80px;
      text-align: right;
      padding-right: $spacing-sm;
    }

    :deep(.el-form-item__content) {
      flex: 1;
      min-width: 200px;
    }

    &.date-range {
      :deep(.el-form-item__content) {
        min-width: 300px;
      }
    }

    &.search-actions {
      :deep(.el-form-item__label) {
        width: 0;
      }

      :deep(.el-form-item__content) {
        margin-left: 0;
      }
    }
  }

  .search-input {
    width: 100%;
  }

  .date-picker {
    width: 100%;
  }

  .search-btn {
    background:  $primary-color;
    border: none;
    color: white;
    font-weight: $font-weight-bold;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-1px);
    }
  }

  .reset-btn {
    background: white;
    border: 1px solid rgba(226, 232, 240, 0.8);
    color: $gray;
    font-weight: $font-weight-medium;
    transition: all 0.3s ease;

    &:hover {
      border-color: $primary-color;
      color: $primary-color;
      transform: translateY(-1px);
    }
  }
}

// 表格样式
.correction-table {
  :deep(.el-table__header) {
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.8), rgba(241, 245, 249, 0.6));

    th {
      background: transparent;
      color: $gray;
      font-weight:500;
      font-size: $font-size-small;
      border-bottom: 2px solid rgba(226, 232, 240, 0.8);
    }
  }
}
:deep(.el-table .cell.el-tooltip){
  font-size: 16px;
}
:deep(.el-tag__content){
  font-size: 14px;
}

// 标签样式
.type-tag {
  font-weight: $font-weight-medium;
}

.status-tag {
  font-weight: $font-weight-medium;
}

.no-reason {
  color: #9ca3af;
  font-style: italic;
}
//分页
.pagination-wrapper{
  margin-top: 20px;
  .el-pagination{
    justify-content: center;

  }
}

// 响应式设计
@media (max-width: $breakpoint-lg) {
  .search-form {
    .form-item {
      margin-right: $spacing-md;

      :deep(.el-form-item__label) {
        width: 70px;
      }

      :deep(.el-form-item__content) {
        min-width: 180px;
      }

      &.date-range {
        :deep(.el-form-item__content) {
          min-width: 250px;
        }
      }
    }
  }

  .correction-table {
    font-size: $font-size-small;

    :deep(.el-table__body) {
      tr td {
        padding: $spacing-xs $spacing-sm;
      }
    }
  }
}

@media (max-width: $breakpoint-md) {
  .page-content {
    gap: $spacing-md;
  }

  .search-card,
  .table-card {
    :deep(.el-card__body) {
      padding: $spacing-md;
    }
  }

  .search-form {
    :deep(.el-form--inline) {
      .el-form-item {
        display: block;
        margin-right: 0;
        margin-bottom: $spacing-sm;

        .el-form-item__label {
          width: 100%;
          text-align: left;
          padding-right: 0;
          margin-bottom: $spacing-xs;
        }

        .el-form-item__content {
          width: 100%;
          margin-left: 0;
        }
      }
    }

    .search-btn,
    .reset-btn {
      width: 100%;
      margin-bottom: $spacing-xs;
    }
  }

  .correction-table {
    :deep(.el-table) {
      font-size: 12px;
    }
  }
}

// 加载状态样式
:deep(.el-loading-mask) {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(4px);
}

:deep(.el-loading-spinner) {
  .el-loading-text {
    color: $primary-color;
    font-weight: $font-weight-medium;
  }

  .circular {
    stroke: $primary-color;
  }
}
</style>