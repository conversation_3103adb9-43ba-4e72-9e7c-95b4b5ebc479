@import "./variables";

/* 全局样式 */
* {
  //margin: 0;
  //padding: 0;
  box-sizing: border-box;
}

body {
  font-family: $font-family-main;
  font-size: $font-size-medium;
  color: $primary-color;
  line-height: 1.4;
  background-color: $white;
  width: 100%;
  margin: 0;
}

h1, h2, h3, h4, h5, h6 {
  font-family: $font-family-title;
  font-weight: $font-weight-bold;
  margin-bottom: $spacing-md;
}

h1 {
  font-size: $font-size-huge;
}

h2 {
  font-size: $font-size-xxxlarge;
}

h3 {
  font-size: $font-size-xxlarge;
}

h4 {
  font-size: $font-size-xlarge;
}

a {
  color: $secondary-color;
  text-decoration: none;
  transition: color 0.3s ease;

  //&:hover {
  //  color: color.adjust($secondary-color, $lightness: -10%);
  //}
}

.container {
  width: 100%;
  max-width: 1536px;
  margin: 0 auto;
  padding: 0 $spacing-md;
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-md $spacing-xl;
  border-radius: $border-radius-md;
  font-weight: $font-weight-medium;
  cursor: pointer;
  transition: all 0.3s ease;

  &-primary {
    background-color: $primary-color;
    color: $white;
    border: none;

    //&:hover {
    //  background-color: color.adjust($primary-color, $lightness: -10%);
    //}
  }

  &-secondary {
    background-color: $white;
    color: $primary-color;
    border: 1px solid $primary-color;

    &:hover {
      background-color: rgba($primary-color, 0.05);
    }
  }
}

/* 响应式工具类 */
.d-none {
  display: none !important;
}

.d-block {
  display: block !important;
}

.d-flex {
  display: flex !important;
}

@media (min-width: $breakpoint-sm) {
  .d-sm-none {
    display: none !important;
  }

  .d-sm-block {
    display: block !important;
  }

  .d-sm-flex {
    display: flex !important;
  }
}

@media (min-width: $breakpoint-md) {
  .d-md-none {
    display: none !important;
  }

  .d-md-block {
    display: block !important;
  }

  .d-md-flex {
    display: flex !important;
  }
}

@media (min-width: $breakpoint-lg) {
  .d-lg-none {
    display: none !important;
  }

  .d-lg-block {
    display: block !important;
  }

  .d-lg-flex {
    display: flex !important;
  }
}

@media (min-width: $breakpoint-xl) {
  .d-xl-none {
    display: none !important;
  }

  .d-xl-block {
    display: block !important;
  }

  .d-xl-flex {
    display: flex !important;
  }
}

/* 间距工具类 */
.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: $spacing-xs !important; }
.mt-2 { margin-top: $spacing-sm !important; }
.mt-3 { margin-top: $spacing-md !important; }
.mt-4 { margin-top: $spacing-lg !important; }
.mt-5 { margin-top: $spacing-xl !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: $spacing-xs !important; }
.mb-2 { margin-bottom: $spacing-sm !important; }
.mb-3 { margin-bottom: $spacing-md !important; }
.mb-4 { margin-bottom: $spacing-lg !important; }
.mb-5 { margin-bottom: $spacing-xl !important; }

.ml-0 { margin-left: 0 !important; }
.ml-1 { margin-left: $spacing-xs !important; }
.ml-2 { margin-left: $spacing-sm !important; }
.ml-3 { margin-left: $spacing-md !important; }
.ml-4 { margin-left: $spacing-lg !important; }
.ml-5 { margin-left: $spacing-xl !important; }

.mr-0 { margin-right: 0 !important; }
.mr-1 { margin-right: $spacing-xs !important; }
.mr-2 { margin-right: $spacing-sm !important; }
.mr-3 { margin-right: $spacing-md !important; }
.mr-4 { margin-right: $spacing-lg !important; }
.mr-5 { margin-right: $spacing-xl !important; }

.pt-0 { padding-top: 0 !important; }
.pt-1 { padding-top: $spacing-xs !important; }
.pt-2 { padding-top: $spacing-sm !important; }
.pt-3 { padding-top: $spacing-md !important; }
.pt-4 { padding-top: $spacing-lg !important; }
.pt-5 { padding-top: $spacing-xl !important; }

.pb-0 { padding-bottom: 0 !important; }
.pb-1 { padding-bottom: $spacing-xs !important; }
.pb-2 { padding-bottom: $spacing-sm !important; }
.pb-3 { padding-bottom: $spacing-md !important; }
.pb-4 { padding-bottom: $spacing-lg !important; }
.pb-5 { padding-bottom: $spacing-xl !important; }

.pl-0 { padding-left: 0 !important; }
.pl-1 { padding-left: $spacing-xs !important; }
.pl-2 { padding-left: $spacing-sm !important; }
.pl-3 { padding-left: $spacing-md !important; }
.pl-4 { padding-left: $spacing-lg !important; }
.pl-5 { padding-left: $spacing-xl !important; }

.pr-0 { padding-right: 0 !important; }
.pr-1 { padding-right: $spacing-xs !important; }
.pr-2 { padding-right: $spacing-sm !important; }
.pr-3 { padding-right: $spacing-md !important; }
.pr-4 { padding-right: $spacing-lg !important; }
.pr-5 { padding-right: $spacing-xl !important; }


.flex-1{
  flex: 1;
}
.cursor-pointer{
  cursor: pointer;
}
.w-50{
  width: 50%!important;
}
.justify-space-between{
  justify-content: space-between;
}