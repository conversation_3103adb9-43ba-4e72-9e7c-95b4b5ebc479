package org.biosino.lf.pds.common.utils.mail;

import cn.hutool.core.io.IoUtil;
import freemarker.core.Environment;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.pds.common.core.mail.MailTemplateEnum;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.UnsupportedEncodingException;
import java.util.Map;

@Slf4j
public class MailTemplateUtil {

    private static final String CHARSET = "UTF-8";

    /*public static String getTemplate(Configuration subjectCfg, Map<String, Object> map, MailTemplateEnum mailTemplate) throws ServiceException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        OutputStreamWriter oStreamWriter = null;
        try {
            oStreamWriter = new OutputStreamWriter(baos, CHARSET);
            Template t = subjectCfg.getTemplate("./" + mailTemplate.getTemplateName(), CHARSET);
            t.setOutputEncoding(CHARSET);
            t.process(map, oStreamWriter);
        } catch (IOException | TemplateException e1) {
            e1.printStackTrace();
            throw new ServiceException("未找到模板");
        } finally {
            IoUtil.close(oStreamWriter);
            IoUtil.close(baos);
        }
        try {
            return baos.toString(CHARSET);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return baos.toString();
    }*/

    public static String getTemplateContent(Configuration freemarkerConfig, final Map<String, Object> data, final MailTemplateEnum templateEnum) throws TemplateException, IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        OutputStreamWriter output = null;
        try {
            output = new OutputStreamWriter(baos, CHARSET);
            Template t = freemarkerConfig.getTemplate(templateEnum.getTemplateName(), CHARSET);
            // System.out.println(t.getOutputFormat().getName());
            Environment env = t.createProcessingEnvironment(data, output);
            env.setOutputEncoding(CHARSET);
            env.process();
        } catch (Exception e) {
            log.error("未找到邮件模板:{}", e.getMessage());
            throw e;
        } finally {
            IoUtil.close(output);
            IoUtil.close(baos);
        }

        try {
            return baos.toString(CHARSET);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return baos.toString();
    }
}
