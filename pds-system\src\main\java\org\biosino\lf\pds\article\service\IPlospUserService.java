package org.biosino.lf.pds.article.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.biosino.lf.pds.article.domain.PlospUser;
import org.biosino.lf.pds.article.dto.PlospUserQueryDTO;

import java.util.List;

/**
 * 前台用户服务接口
 */
public interface IPlospUserService extends IService<PlospUser> {

    /**
     * 查询用户列表
     */
    List<PlospUser> selectUserList(PlospUserQueryDTO queryDTO);

    /**
     * 根据邮箱查询用户
     */
    PlospUser selectUserByEmail(String email);


    /**
     * 新增用户
     */
    boolean addUser(PlospUser user);

    /**
     * 修改用户信息
     */
    boolean updateUser(PlospUser user);

    /**
     * 删除用户
     *
     * @param ids 用户ID数组
     * @return 结果
     */
    boolean deleteUserByIds(Long[] ids);

    /**
     * 修改用户状态
     */
    boolean changeStatus(Long id, Integer status);

    /**
     * 检查邮箱是否唯一
     */
    boolean checkEmailUnique(Long id, String email);
    
    /**
     * 发送注册验证码
     *
     * @param email 邮箱
     */
    void sendRegisterCode(String email);
    
    /**
     * 用户注册
     *
     * @param user 用户信息
     * @param code 验证码
     */
    void registerUser(PlospUser user, String code);
    
    /**
     * 重置用户密码
     *
     * @param userId 用户ID
     * @param newPassword 新密码
     */
    boolean resetUserPwd(Long userId, String newPassword);
}
