# coding=utf8
__author__ = 'yhju'

import json
import logging

import requests
from retry import retry

logger = logging.getLogger()


class ApiInvoker(object):
    """
    任务调度接口，与pds服务器通信获取任务及进行任务调度
    """

    def __init__(self, api_url: str, site_id: int, api_token: str, encoding="UTF-8", timeout=30):
        if api_url is None or api_url.strip() == "":
            raise ValueError("请指定 apiUrl")
        self.__api_url = api_url
        self.__site_id = site_id
        self.__api_token = api_token
        self.__encoding = encoding
        self.__timeout = timeout
        self.__req = requests.session()
        # 被安全控件拦截了，所以加个头
        self.__req.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) "
                          "Chrome/81.0.4044.129 Safari/537.36 "
        }

    def site_info(self) -> dict:
        """
        获取站点信息
        :return:
        """
        if not self.__site_id or self.__site_id < 1:
            raise ValueError("参数错误，请指定站点ID")

        return self.__get("{}/getSiteBaseinfo".format(self.__api_url), 
                          params={"siteId": self.__site_id, "apiToken": self.__api_token})

    def send_handshake(self, sys_info: dict) -> dict:
        """
        发送握手信息
        :param sys_info:
        :return:
        """
        if not self.__site_id or self.__site_id < 1:
            raise ValueError("参数错误，请指定站点ID")

        if sys_info is None:
            sys_info = {}

        return self.__get("{}/sendHandshake".format(self.__api_url),
                          params={"siteId": self.__site_id, "apiToken": self.__api_token, 
                                  "sysInfo": json.dumps(sys_info, ensure_ascii=False)})

    def next_task_info(self, active_script_ids=None) -> dict:
        """
        获取下一个任务的doc_id(pmid、pmcid、doi)等信息，返回结果中的状态 status 意义： success，error，none(没有任务了)
        :param active_script_ids: 当前活跃的脚本ID列表，用于后台优先获取不同脚本ID的任务
        :return:
        """
        if not self.__site_id or self.__site_id < 1:
            raise ValueError("参数错误，请指定站点ID")

        url = "{}/getNextTaskInfo".format(self.__api_url)
        params = {
            "siteId": self.__site_id,
            "apiToken": self.__api_token
        }

        # 如果提供了活跃脚本ID列表，添加到参数中
        if active_script_ids and isinstance(active_script_ids, list):
            # 将脚本ID数组转换为逗号分隔的字符串
            params["activeScriptIds"] = ",".join(str(script_id) for script_id in active_script_ids)
            logger.debug(f"发送活跃脚本ID到后台: {params['activeScriptIds']}")

        return self.__get(url, params=params)

    def send_task_message(self, task_id: str, msg: str) -> dict:
        """
        发送任务日志消息，该方法为日志方法，不抛出异常而中断主线程
        :param msg:
        :param task_id:
        :return:
        """
        if task_id is None or task_id.strip() == "" or msg is None or msg.strip() == "":
            return None
        # 捕获所有异常，即使出错也不影响调用者继续执行
        try:
            url = "{}/sendTaskMessage".format(self.__api_url)
            return self.__get(url, params={"siteId": self.__site_id, "taskId": task_id, 
                                           "msg": msg, "apiToken": self.__api_token})
        except BaseException as e:
            logger.exception("发送消息失败 {}".format(e))

    @retry(tries=1, delay=3)
    def get_task_script_file(self, script_id_str: str, to_file_path: str):
        """
        获取任务脚本文件
        :param to_file_path:
        :param script_id_str:
        :return:
        """
        if script_id_str is None or script_id_str.strip() == "" or to_file_path is None or to_file_path.strip() == "":
            return

        url = "{}/getTaskScriptFile".format(self.__api_url)
        response = self.__req.get(url, params={"siteId": self.__site_id, "scriptIdStr": script_id_str, 
                                               "apiToken": self.__api_token}, 
                                  stream=True, timeout=self.__timeout)

        if response.status_code != 200:
            raise ValueError(
                "[GET]调用 API 失败 Response Code: {} URL: {}, Param: {}".format(response.status_code, url, script_id_str))

        with open(to_file_path, "wb") as f:
            for chunk in response.iter_content(chunk_size=1024):
                if chunk:
                    f.write(chunk)

    def update_task_info(self, task_info: dict) -> dict:
        """
        更新任务信息
        必需字段: "siteId", "taskId", "docId", "status"
        可选字段: "pmid", "uploadPath"
        :param task_info:
        :return:
        """
        if task_info is None or len(task_info) == 0:
            return None

        url = "{}/notice2UpdateTask".format(self.__api_url)

        # 必要字段验证
        required_fields = ["siteId", "taskId", "docId", "status"]
        for field in required_fields:
            if field in task_info.keys():
                continue
            raise ValueError("[POST]调用 API 失败，缺少参数 {}， URL: {}, Data: {}".format(field, url, task_info))

        # 添加apiToken到任务信息
        task_info["apiToken"] = self.__api_token
        
        return self.__post(url=url, data={"jsonStr": json.dumps(task_info, ensure_ascii=False)})

    @retry(tries=1, delay=3)
    def __get(self, url: str, params: dict) -> dict:
        response = self.__req.get(url, params=params, timeout=self.__timeout)

        if response.status_code != 200:
            raise ValueError(
                "[GET]调用 API 失败 Response Code: {} URL: {}, Param: {}".format(response.status_code, url, params))

        result = None
        try:
            response.encoding = self.__encoding
            result = response.json()
        except BaseException as e:
            raise ValueError(
                "[GET]调用 API 失败 Response Code: {} URL: {}, Data: {}, Exception: {}".format(response.status_code, url,
                                                                                           params, e))
        finally:
            response.close()
        return result

    @retry(tries=1, delay=3)
    def __post(self, url: str, data: dict) -> dict:
        response = self.__req.post(url, data=data, timeout=self.__timeout)

        if response.status_code != 200:
            raise ValueError(
                "[POST]调用 API 失败 Response Code: {} URL: {}, Data: {}".format(response.status_code, url, data))

        result = None
        try:
            response.encoding = self.__encoding
            result = response.json()
        except BaseException as e:
            raise ValueError(
                "[POST]调用 API 失败 Response Code: {} URL: {}, Data: {}, Exception: {}".format(response.status_code, url,
                                                                                            data,
                                                                                            e))
        finally:
            response.close()
        return result
