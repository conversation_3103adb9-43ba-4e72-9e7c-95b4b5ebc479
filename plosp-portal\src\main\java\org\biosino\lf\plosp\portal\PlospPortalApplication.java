package org.biosino.lf.plosp.portal;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;

/**
 * PLOSP门户应用程序
 * 个人科学论文库门户后端
 */
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@ComponentScan(basePackages = {"org.biosino.lf.pds.**", "org.biosino.lf.plosp.**"})
public class PlospPortalApplication {

    public static void main(String[] args) {
        SpringApplication.run(PlospPortalApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  PLOSP Portal启动成功   ლ(´ڡ`ლ)ﾞ");
    }
}
