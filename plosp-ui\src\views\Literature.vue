<template>
  <div class="literature">
    <!-- 搜索区域 -->
    <section class="search-section">
      <div class="container">
        <div class="search-container">
          <div class="search-bar">
            <div class="search-field">
              <el-select
                  v-model="searchType"
                  class="search-type"
                  popper-class="search-type-dropdown"
              >
                <el-option
                    v-for="item in searchOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                />
              </el-select>
              <input
                type="text"
                class="search-input"
                placeholder="输入关键词、标题、作者、DOI、PMID"
                v-model="searchKeyword"
              />
            </div>
            <button class="search-button" @click="handleSearch">
              <el-icon><Search /></el-icon>
            </button>
          </div>

          <div class="search-options">
            <div class="search-option" :class="{ active: showAdvancedSearch }" @click="toggleAdvancedSearch">
              <img src="@/assets/images/filter-icon.svg" alt="高级检索" class="option-icon" />
              <span>高级检索</span>
            </div>
            <div class="search-option" :class="{ active: showProblemSearch }" @click="toggleProblemSearch">
              <img src="@/assets/images/semantic-search-icon.svg" alt="语义检索" class="option-icon" />
              <span>语义检索</span>
            </div>
          </div>
        </div>
      </div>
    </section>
    <div class="container ">
      <!-- 高级检索面板 -->
      <el-collapse-transition>
        <div v-if="showAdvancedSearch" class="advanced-search-container mt-0 mb-4">
          <div class="advanced-search-content mt-0">
            <div class="search-row" v-for="(condition, index) in searchConditions" :key="index">
              <el-select v-model="condition.logic" class="logic-select">
                <el-option label="AND" value="AND" />
                <el-option label="OR" value="OR" />
                <el-option label="NOT" value="NOT" />
              </el-select>

            <el-select v-model="condition.field" class="field-select" popper-class="field-select-dropdown">
              <el-option label="所有字段" value="所有字段" />
              <el-option label="标题" value="标题" />
              <el-option label="作者" value="作者" />
              <el-option label="关键词" value="关键词" />
              <el-option label="摘要" value="摘要" />
              <el-option label="DOI" value="DOI" />
              <el-option label="PMID" value="PMID" />
              <el-option label="期刊" value="期刊" />
            </el-select>

            <el-input v-model="condition.value" class="keyword-input" placeholder="输入检索词..." />

            <el-button
                type="text"
                class="remove-condition-btn"
                @click="removeSearchCondition(index)"
                v-if="searchConditions.length > 1"
            >
              <el-icon><Close /></el-icon>
            </el-button>
          </div>

          <div class="add-condition-row">
            <el-button type="primary" text class="add-condition-btn" @click="addSearchCondition">
              <el-icon><Plus /></el-icon>
              添加检索条件
            </el-button>
          </div>

          <div class="search-query-container">
            <div class="search-query-box">
              <div class="search-query-label">查询语句</div>
              <el-button type="text" class="copy-btn" @click="copySearchQuery">
                <el-icon><CopyDocument /></el-icon>
                复制
              </el-button>
            </div>
           <el-input v-model="searchQuery" type="textarea" rows="5"></el-input>
          </div>

          <div class="search-history-container">
            <div class="search-history-label">检索历史</div>
            <div class="search-history-list">
              <div class="search-history-item" v-for="(history, index) in searchHistory" :key="index">
                <div class="history-index">#{{ index + 1 }}</div>
                <div class="history-query">{{ history.query }}</div>
                <div class="history-result-count">{{ history.count }} 结果</div>
                <div class="history-actions">
                  <el-button type="text" class="edit-btn" @click="editSearchHistory(index)">编辑</el-button>
                  <el-button type="text" class="delete-btn" @click="deleteSearchHistory(index)">删除</el-button>
                </div>
              </div>
            </div>
          </div>

          <div class="search-actions">
            <el-button plain class="clear-btn" @click="showAdvancedSearch=false">关闭</el-button>
            <el-button plain class="clear-btn" @click="clearSearchConditions">清空</el-button>
            <el-button type="primary" class="search-btn" @click="handleAdvancedSearch">检索</el-button>
          </div>
        </div>
        </div>
      </el-collapse-transition>

      <!-- 语义检索面板 -->
      <el-collapse-transition>
        <div v-if="showProblemSearch" class="advanced-search-container mt-0 mb-4">
          <div class="search-query-container mt-0">
            <div class="search-query-label search-query-box">输入问题</div>
            <el-input v-model="problemSearch" type="textarea" rows="5"></el-input>
          </div>
          <div class="search-actions">
            <el-button plain class="clear-btn" @click="showProblemSearch=false">关闭</el-button>
            <el-button type="primary" class="search-btn">检索</el-button>
          </div>
        </div>
      </el-collapse-transition>
    </div>

    <!-- 主要内容区域 -->
    <section class="content-section">
      <div class="container content-container">
        <!-- 移动端筛选按钮 -->
        <div class="mobile-filter-toggle" v-if="isMobile" @click="showMobileFilters = !showMobileFilters">
          <span>筛选条件</span>
          <el-icon class="toggle-icon" :class="{ 'rotated': showMobileFilters }"><ArrowDown /></el-icon>
        </div>

        <!-- 左侧筛选条件 -->
        <div class="filter-sidebar" :class="{ 'mobile-hidden': isMobile && !showMobileFilters }">
          <div class="filter-header">
            <img src="@/assets/images/filter-icon.svg" alt="高级检索" class="filter-icon" />
            <h2 class="filter-title">筛选条件</h2>

          </div>

          <!-- 发布时间范围 -->
          <div class="filter-group">
            <h3 class="filter-group-title">发布时间范围</h3>
            <el-radio-group v-model="timeRange" class="filter-options">
              <div class="filter-option" v-for="item in timeRangeOptions" :key="item.value">
                <el-radio :label="item.value">{{ item.label }}</el-radio>
              </div>
              <div class="filter-option custom-range cursor-pointer" @click="openCustomRangeDialog">
                <span>自定义范围</span>
              </div>
            </el-radio-group>
          </div>

          <!-- 可用文献 -->
          <div class="filter-group">
            <h3 class="filter-group-title">可用文献</h3>
            <el-checkbox-group v-model="availableTypes" class="filter-options">
              <div class="filter-option" v-for="item in availableTypesOptions" :key="item.value">
                <el-checkbox :label="item.value">
                  <div class="filter-option-content">
                    <span>{{ item.label }}</span>
                    <div class="count-badge">{{ item.count }}</div>
                  </div>
                </el-checkbox>
              </div>
            </el-checkbox-group>
          </div>

          <!-- 文献类型 -->
          <div class="filter-group">
            <h3 class="filter-group-title">文献类型</h3>
            <el-checkbox-group v-model="literatureTypes" class="filter-options">
              <div class="filter-option" v-for="item in literatureTypesOptions" :key="item.value">
                <el-checkbox :label="item.value">
                  <div class="filter-option-content">
                    <span>{{ item.label }}</span>
                    <div class="count-badge">{{ item.count }}</div>
                  </div>
                </el-checkbox>
              </div>
            </el-checkbox-group>
            <div class="more-types" @click="literTypeDialog = true">更多类型</div>
          </div>

          <!-- 文章属性 -->
          <div class="filter-group">
            <h3 class="filter-group-title">文章属性</h3>
            <el-checkbox-group v-model="articleAttributes" class="filter-options">
              <div class="filter-option" v-for="item in articleAttributesOptions" :key="item.value">
                <el-checkbox :label="item.value">
                  <div class="filter-option-content">
                    <span>{{ item.label }}</span>
                    <div class="count-badge">{{ item.count }}</div>
                  </div>
                </el-checkbox>
              </div>
            </el-checkbox-group>
          </div>

          <!-- 影响因子 -->
          <div class="filter-group">
            <h3 class="filter-group-title">影响因子</h3>
            <el-checkbox-group v-model="impactFactors" class="filter-options">
              <div class="filter-option" v-for="item in impactFactorsOptions" :key="item.value">
                <el-checkbox :label="item.value">
                  <div class="filter-option-content">
                    <span>{{ item.label }}</span>
                    <div class="count-badge">{{ item.count }}</div>
                  </div>
                </el-checkbox>
              </div>
            </el-checkbox-group>
          </div>

          <!-- 语言 -->
          <div class="filter-group">
            <h3 class="filter-group-title">语言</h3>
            <el-checkbox-group v-model="languages" class="filter-options">
              <div class="filter-option" v-for="item in languagesOptions" :key="item.value">
                <el-checkbox :label="item.value">
                  <div class="filter-option-content">
                    <span>{{ item.label }}</span>
                    <div class="count-badge">{{ item.count }}</div>
                  </div>
                </el-checkbox>
              </div>
            </el-checkbox-group>
          </div>

          <!-- 基金支持 -->
          <div class="filter-group">
            <h3 class="filter-group-title">基金支持</h3>
            <el-checkbox-group v-model="fundingSupport" class="filter-options">
              <div class="filter-option" v-for="item in fundingSupportOptions" :key="item.value">
                <el-checkbox :label="item.value">
                  <div class="filter-option-content">
                    <span>{{ item.label }}</span>
                    <div class="count-badge">{{ item.count }}</div>
                  </div>
                </el-checkbox>
              </div>
            </el-checkbox-group>
          </div>

          <!-- JCR分区 -->
          <div class="filter-group">
            <h3 class="filter-group-title">JCR分区</h3>
            <el-checkbox-group v-model="jcrQuartiles" class="filter-options filter-grid">
              <div class="filter-option filter-grid-item" v-for="item in jcrQuartilesOptions" :key="item.value">
                <el-checkbox :label="item.value">
                  <div class="filter-option-content">
                    <span>{{ item.label }}</span>
                    <div class="count-badge">{{ item.count }}</div>
                  </div>
                </el-checkbox>
              </div>
            </el-checkbox-group>
          </div>

          <!-- 中科院分区 -->
          <div class="filter-group">
            <h3 class="filter-group-title">中科院分区</h3>
            <el-checkbox-group v-model="casQuartiles" class="filter-options filter-grid">
              <div class="filter-option filter-grid-item" v-for="item in casQuartilesOptions" :key="item.value">
                <el-checkbox :label="item.value">
                  <div class="filter-option-content">
                    <span>{{ item.label }}</span>
                    <div class="count-badge">{{ item.count }}</div>
                  </div>
                </el-checkbox>
              </div>
            </el-checkbox-group>
          </div>

          <!-- 显示更多按钮 -->
          <button class="show-more-btn" @click="showOtherFilters=!showOtherFilters">{{ showOtherFilters ? '收起更多条件' : '显示更多条件' }}</button>
          <!-- 基金支持 -->
          <div class="filter-group" v-show="showOtherFilters">
            <h3 class="filter-group-title">其他</h3>
            <el-checkbox-group v-model="otherSupport" class="filter-options">
              <div class="filter-option" v-for="item in otherOptions" :key="item.value">
                <el-checkbox :label="item.value">
                  <div class="filter-option-content">
                    <span>{{ item.label }}</span>
                  </div>
                </el-checkbox>
              </div>
            </el-checkbox-group>
          </div>
        </div>

        <!-- 右侧文献列表 -->
        <div class="literature-content">
          <div class="literature-header">
            <div class="literature-header-left">
              <div class="select-all">
                <input type="checkbox" id="select-all" />
                <label for="select-all">全选</label>
              </div>
              <div >找到 <span class="result-count">1,234</span>  条相关文献</div>
            </div>
            <div class="literature-header-right">
              <div class="sort-options">
                <span>排序：</span>
                <div class="sort-dropdown">
                  <el-select v-model="sort" class="field-select" popper-class="field-select-dropdown" style="width: 100px">
                    <el-option label="相关度" value="相关度" />
                    <el-option label="发布日期" value="发布日期" />
                    <el-option label="引用次数" value="引用次数" />
                    <el-option label="影响因子" value="影响因子" />
                  </el-select>
                </div>
              </div>
              <div class="page-size">
                <span>每页：</span>
                <div class="page-size-dropdown">
                  <el-select v-model="pageSize" class="field-select" popper-class="field-select-dropdown" style="width: 100px">
                    <el-option label="5" value="5" />
                    <el-option label="10" value="10" />
                    <el-option label="20" value="20" />
                    <el-option label="50" value="50" />
                    <el-option label="100" value="100" />
                  </el-select>
                </div>
              </div>
              <div class="action-button" @click="handleBatchFavorite">
                <el-icon><Star /></el-icon>
                <span>收藏</span>
              </div>
              <div class="action-button">
                <el-icon><Download /></el-icon>
                <el-dropdown placement="bottom">
                  <span class="export">导出</span>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item>导出元数据</el-dropdown-item>
                      <el-dropdown-item>导出文献清单</el-dropdown-item>
                      <el-dropdown-item>导出Cite</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>
          </div>

          <!-- 文献列表 -->
          <div class="article-list-container">
            <article-item
              v-for="(article, index) in articles"
              :key="index"
              :article="article"
            />
          </div>

          <!-- 分页 -->
          <div class="pagination">
            <el-pagination
              background
              layout="prev, pager, next"
              :total="100"
              :page-size="5"
            />
          </div>
        </div>
      </div>
    </section>
    <el-dialog
      v-model="literTypeDialog"
      title="文献类型"
      :width="isMobile ? '95%' : '800px'"
      draggable
      :top="isMobile ? '5vh' : '15vh'"
      class="literature-type-dialog"
    >
      <div class="literature-type-content">
        <el-checkbox-group v-model="moreLiterTypeList" class="literature-type-checkboxes">
          <el-checkbox
            v-for="item in moreLiterTypeList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
            class="literature-type-checkbox"
          />
        </el-checkbox-group>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="literTypeDialog = false" class="cancel-btn">取消</el-button>
          <el-button type="primary" @click="literTypeDialog = false" class="confirm-btn">
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 自定义范围对话框 -->
    <el-dialog
      v-model="customRangeDialog"
      title="自定义发布时间范围"
      :width="isMobile ? '90%' : '400px'"
      draggable
    >
      <div class="custom-range-content">
        <!-- 开始日期 -->
        <div class="date-section">
          <div class="date-label">开始日期</div>
          <div class="date-inputs">
            <el-input
              v-model="startYear"
              placeholder="YYYY"
              class="year-input"
              maxlength="4"
            />
            <el-input
              v-model="startMonth"
              placeholder="MM"
              class="month-input"
              maxlength="2"
            />
            <el-input
              v-model="startDay"
              placeholder="DD"
              class="day-input"
              maxlength="2"
            />
          </div>
        </div>

        <!-- 结束日期 -->
        <div class="date-section">
          <div class="date-label">结束日期</div>
          <div class="date-inputs">
            <el-input
              v-model="endYear"
              placeholder="YYYY"
              class="year-input"
              maxlength="4"
            />
            <el-input
              v-model="endMonth"
              placeholder="MM"
              class="month-input"
              maxlength="2"
            />
            <el-input
              v-model="endDay"
              placeholder="DD"
              class="day-input"
              maxlength="2"
            />
          </div>
        </div>

        <!-- 按钮区域 -->
        <div class="dialog-actions">
          <el-button @click="clearCustomRange" class="clear-btn">清空</el-button>
          <el-button type="primary" class="apply-btn">应用</el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 收藏弹框 -->
    <collection-modal
      v-model="showCollectionModal"
      :article="selectedArticle"
      @confirm="handleCollectionConfirm"
    />
  </div>
</template>

<script setup>
import {ref, computed, reactive, onMounted, onUnmounted, watch} from 'vue'
import { Search, ArrowDown, Star, Download, Close, Plus, CopyDocument, Filter } from '@element-plus/icons-vue'
import ArticleItem from '@/components/ArticleItem.vue'
import CollectionModal from '@/components/CollectionModal.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRoute } from 'vue-router'

const route = useRoute()

// 搜索关键词
// 搜索相关
const searchType = ref('所有字段')
const searchKeyword = ref('')
const searchOptions = [
  { label: '所有字段', value: '所有字段' },
  { label: '标题', value: '标题' },
  { label: '作者', value: '作者' },
  { label: 'DOI', value: 'DOI' },
  { label: 'PMID', value: 'PMID' }
]
const sort=ref('相关度')
const pageSize=ref('5')
const showOtherFilters=ref(false)
const literTypeDialog=ref(false)

// 收藏相关
const showCollectionModal = ref(false)
const selectedArticle = ref(null)

// 自定义范围相关
const customRangeDialog = ref(false)
const startYear = ref('')
const startMonth = ref('')
const startDay = ref('')
const endYear = ref('')
const endMonth = ref('')
const endDay = ref('')

// 移动端检测
const windowWidth = ref(window.innerWidth)
const isMobile = computed(() => windowWidth.value <= 768)
const showMobileFilters = ref(false)

const handleResize = () => {
  windowWidth.value = window.innerWidth
}

onMounted(() => {
  window.addEventListener('resize', handleResize)

  // 根据路由参数控制检索面板显示
  const searchMode = route.query.mode
  if (searchMode === 'advanced') {
    showAdvancedSearch.value = true
    showProblemSearch.value = false
  } else if (searchMode === 'semantic') {
    showProblemSearch.value = true
    showAdvancedSearch.value = false
  }
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

// 高级检索相关
const showAdvancedSearch = ref(false)
const showProblemSearch = ref(false)
const searchConditions = ref([
  { logic: 'AND', field: '所有字段', value: '' },
  { logic: 'AND', field: '所有字段', value: '' },
])
const searchQuery=ref('(title:"肿瘤免疫" OR keywords:"肿瘤免疫") AND (abstract:"CAR-T" OR keywords:"CAR-T") AND year:[2020 TO 2023] AND language:zh')
const problemSearch=ref('癌症最新研究进展')

// 示例检索历史
const searchHistory = ref([
  { query: 'title:"肿瘤免疫" AND keywords:"CAR-T"', count: 51946 },
  { query: 'author:"Zhang L" AND year:2023', count: 1234 }
])
const moreLiterTypeList=reactive([
    { label: 'Adaptive Clinical Trial', value: 'Adaptive Clinical Trial' },
    { label: 'Introductory Journal Article', value: 'Introductory Journal Article' },
    { label: 'Address', value: 'Address' },
    { label: 'Lecture', value: 'Lecture' },
    { label: 'Autobiography', value: 'Autobiography' },
    { label: 'Legal Case', value: 'Legal Case' },
    { label: 'Bibliography', value: 'Bibliography' },
    { label: 'Legislation', value: 'Legislation' },
    { label: 'Biography', value: 'Biography' },
    { label: 'Letter', value: 'Letter' },
    { label: 'Books and Documents', value: 'Books and Documents' },
    { label: 'Meta-Analysis', value: 'Meta-Analysis' },
    { label: 'Case Reports', value: 'Case Reports' },
    { label: 'Multicenter Study', value: 'Multicenter Study' },
    { label: 'Classical Article', value: 'Classical Article' },
    { label: 'Network Meta-Analysis', value: 'Network Meta-Analysis' },
    { label: 'Clinical Conference', value: 'Clinical Conference' },
    { label: 'News', value: 'News' },
    { label: 'Clinical Study', value: 'Clinical Study' },
    { label: 'Newspaper Article', value: 'Newspaper Article' },
    { label: 'Clinical Trial', value: 'Clinical Trial' },
    { label: 'Observational Study', value: 'Observational Study' },
    { label: 'Clinical Trial Protocol', value: 'Clinical Trial Protocol' },
    { label: 'Observational Study, Veterinary', value: 'Observational Study, Veterinary' },
    { label: 'Clinical Trial, Phase I', value: 'Clinical Trial, Phase I' },
    { label: 'Overall', value: 'Overall' },
    { label: 'Clinical Trial, Phase II', value: 'Clinical Trial, Phase II' },
    { label: 'Patient Education Handout', value: 'Patient Education Handout' },
    { label: 'Clinical Trial, Phase III', value: 'Clinical Trial, Phase III' },
    { label: 'Periodical Index', value: 'Periodical Index' },
    { label: 'Clinical Trial, Phase IV', value: 'Clinical Trial, Phase IV' },
    { label: 'Personal Narrative', value: 'Personal Narrative' },
    { label: 'Clinical Trial, Veterinary', value: 'Clinical Trial, Veterinary' },
    { label: 'Portrait', value: 'Portrait' },
    { label: 'Collected Work', value: 'Collected Work' },
    { label: 'Practice Guideline', value: 'Practice Guideline' },
    { label: 'Comment', value: 'Comment' },
    { label: 'Pragmatic Clinical Trial', value: 'Pragmatic Clinical Trial' },
    { label: 'Comparative Study', value: 'Comparative Study' },
    { label: 'Preprint', value: 'Preprint' },
    { label: 'Congress', value: 'Congress' },
    { label: 'Published Erratum', value: 'Published Erratum' },
    { label: 'Consensus Development Conference', value: 'Consensus Development Conference' },
    { label: 'Randomized Controlled Trial', value: 'Randomized Controlled Trial' },
    { label: 'Consensus Development Conference, NIH', value: 'Consensus Development Conference, NIH' },
    { label: 'Randomized Controlled Trial, Veterinary', value: 'Randomized Controlled Trial, Veterinary' },
    { label: 'Controlled Clinical Trial', value: 'Controlled Clinical Trial' },
    { label: 'Research Support, American Recovery and Reinvestment Act', value: 'Research Support, American Recovery and Reinvestment Act' },
    { label: 'Corrected and Republished Article', value: 'Corrected and Republished Article' },
    { label: 'Research Support, N.I.H., Extramural', value: 'Research Support, N.I.H., Extramural' },
    { label: 'Dataset', value: 'Dataset' },
    { label: 'Research Support, N.I.H., Intramural', value: 'Research Support, N.I.H., Intramural' },
    { label: 'Directory', value: 'Directory' },
    { label: 'Research Support, Non-U.S. Gov\'t', value: 'Research Support, Non-U.S. Gov\'t' },
    { label: 'Duplicate Publication', value: 'Duplicate Publicationt' },
    { label: 'Research Support, U.S. Gov\'t, Non-P.H.S.', value: 'Research Support, U.S. Gov\'t, Non-P.H.S.' },
    { label: 'Editorial', value: 'Editorial' },
    { label: 'Research Support, U.S. Gov\'t, P.H.S.', value: 'Research Support, U.S. Gov\'t, P.H.S.' },
    { label: 'Electronic Supplementary Materials', value: 'Electronic Supplementary Materials' },
    { label: 'Research Support, U.S. Gov\'t', value: 'Research Support, U.S. Gov\'t' },
    { label: 'English Abstract', value: 'English Abstract' },
    { label: 'Retracted Publication', value: 'Retracted Publication' },
    { label: 'Equivalence Trial', value: 'Equivalence Trial' },
    { label: 'Retraction of Publication', value: 'Retraction of Publication' },
    { label: 'Evaluation Study', value: 'Evaluation Study' },
    { label: 'Review', value: 'Review' },
    { label: 'Expression of Concern', value: 'Expression of Concern' },
    { label: 'Scientific Integrity Review', value: 'Scientific Integrity Review' },
    { label: 'Festschrift', value: 'Festschrift' },
    { label: 'Scoping Review', value: 'Scoping Review' },
    { label: 'Government Publication', value: 'Government Publication' },
    { label: 'Systematic Review', value: 'Systematic Review' },
    { label: 'Guideline', value: 'Guideline' },
    { label: 'Technical Report', value: 'Technical Report' },
    { label: 'Historical Article', value: 'Historical Article' },
    { label: 'Twin Study', value: 'Twin Study' },
    { label: 'Interactive Tutorial', value: 'Interactive Tutorial' },
    { label: 'Video-Audio Media', value: 'Video-Audio Media' },
    { label: 'Interview', value: 'Interview' },
    { label: 'Webcast', value: 'Webcast' },
])

// 编辑检索历史
const editSearchHistory = (index) => {
}

// 删除检索历史
const deleteSearchHistory = (index) => {
  ElMessageBox.confirm('确定要删除此检索历史记录吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    searchHistory.value.splice(index, 1)
    ElMessage({
      type: 'success',
      message: '删除成功'
    })
  }).catch(() => {
    // 取消删除操作
  })
}

// 切换高级检索显示状态
const toggleAdvancedSearch = () => {
  if (showAdvancedSearch.value) {
    showAdvancedSearch.value = false
  } else {
    showAdvancedSearch.value = true
    showProblemSearch.value = false
  }
}

const toggleProblemSearch = () => {
  if (showProblemSearch.value) {
    // 如果语义检索已经打开，则关闭它
    showProblemSearch.value = false
  } else {
    // 如果语义检索未打开，则打开它并关闭高级检索
    showProblemSearch.value = true
    showAdvancedSearch.value = false
  }
}

// 添加检索条件
const addSearchCondition = () => {
  searchConditions.value.push({ logic: 'AND', field: '所有字段', value: '' })
}

// 移除检索条件
const removeSearchCondition = (index) => {
  searchConditions.value.splice(index, 1)
}

// 清空检索条件
const clearSearchConditions = () => {
  searchConditions.value = [{ logic: 'AND', field: '所有字段', value: '' }]
}

// 复制查询语句
const copySearchQuery = () => {

}

// 处理高级检索
const handleAdvancedSearch = () => {
}

// 处理搜索
const handleSearch = () => {
  console.log('搜索关键词:', searchKeyword.value)
}

// 自定义范围相关方法
const openCustomRangeDialog = () => {
  customRangeDialog.value = true
}

const clearCustomRange = () => {
  startYear.value = ''
  startMonth.value = ''
  startDay.value = ''
  endYear.value = ''
  endMonth.value = ''
  endDay.value = ''
}


// 筛选相关
// 发布时间范围
const timeRange = ref('recent1year')
const timeRangeOptions = [
  { label: '最近1年', value: 'recent1year' },
  { label: '最近5年', value: 'recent5years' },
  { label: '最近10年', value: 'recent10years' }
]

// 可用文献
const availableTypes = ref(['abstract'])
const availableTypesOptions = [
  { label: '免费全文', value: 'freeFullText', count: 234 },
  { label: '摘要', value: 'abstract', count: 1218 },
  { label: '付费全文', value: 'paidFullText', count: 234 }
]

// 文献类型
const literatureTypes = ref(['researchPaper'])
const literatureTypesOptions = [
  { label: '综述', value: 'review', count: 234 },
  { label: '研究论文', value: 'researchPaper', count: 1218 },
  { label: '临床试验', value: 'clinicalTrial', count: 234 },
  { label: '病例报告', value: 'caseReport', count: 234 }
]

// 文章属性
const articleAttributes = ref(['relatedData'])
const articleAttributesOptions = [
  { label: '相关数据', value: 'relatedData', count: 1218 }
]

// 影响因子
const impactFactors = ref(['above10'])
const impactFactorsOptions = [
  { label: '5-10', value: '5to10', count: 294 },
  { label: '10以上', value: 'above10', count: 1218 },
  { label: '3-5', value: '3to5', count: 514 },
  { label: '3以下', value: 'below3', count: 231 }
]

// 语言
const languages = ref(['chinese'])
const languagesOptions = [
  { label: '英文', value: 'english', count: 294 },
  { label: '中文', value: 'chinese', count: 1218 }
]

// 基金支持
const fundingSupport = ref(['hasFunding'])
const fundingSupportOptions = [
  { label: '无基金支持', value: 'noFunding', count: 294 },
  { label: '有基金支持', value: 'hasFunding', count: 1218 }
]

// 其他
const otherSupport = ref(['hasFunding'])
const otherOptions = [
  { label: '排除预印本', value: '排除预印本' },
  { label: 'PubMed Central收录', value: 'PubMed Central收录'},
]

// JCR分区
const jcrQuartiles = ref(['Q1'])
const jcrQuartilesOptions = [
  { label: 'Q1', value: 'Q1', count: 294 },
  { label: 'Q2', value: 'Q2', count: 294 },
  { label: 'Q3', value: 'Q3', count: 294 },
  { label: 'Q4', value: 'Q4', count: 294 }
]

// 中科院分区
const casQuartiles = ref(['zone1'])
const casQuartilesOptions = [
  { label: '1区', value: 'zone1', count: 294 },
  { label: '2区', value: 'zone2', count: 294 },
  { label: '3区', value: 'zone3', count: 294 },
  { label: '4区', value: 'zone4', count: 294 }
]

// 模拟文献数据
const articles = ref([
  {
    id: 2,
    title: 'Targeting the SHOC2-RAS interaction in RAS-mutant cancers',
    authors: 'Zhang L, Wang Y, Li J, Wang R, Chen M, Sun K',
    journal: 'Journal of Immunology Research',
    date: '2023 Jun;15(6):1243-1258',
    doi: '10.1016/j.cger.2020.06.009',
    pmid: '33010902',
    impactFactor: 8.6,
    jcr: 'Q1',
    hasFullText: true,
    abstract: "Activating mutations in the rat sarcoma (RAS) genes HRAS, NRAS and KRAS collectively represent the most frequent oncogenic driver in human cancer1. They have previously been considered undruggable, but advances in the past few years have led to the clinical development of agents that target KRAS(G12C) and KRAS(G12D) mutants, yielding promises of therapeutic responses at tolerated doses2.",
    keywords: ['Tumor immunity', 'Macrophage polarization', 'Tumor microenvironment', 'Immunotherapy']
  },
  {
    id: 3,
    title: 'Twist-programmable superconductivity in spin-orbit-coupled bilayer graphene',
    authors: 'Zhang L, Wang Y, Li J, Wang R, Chen M, Sun K',
    journal: 'Journal of Immunology Research',
    date: '2023 Jun;15(6):1243-1258',
    doi: '10.1016/j.cger.2020.06.009',
    pmid: '33010902',
    impactFactor: 8.6,
    jcr: 'Q1',
    hasFullText: true,
    abstract: "The relative twist angle between layers of near-lattice-matched van der Waals materials is critical for the emergent phenomena associated with moiré flat bands1-3. However, the concept of angle rotation control is not exclusive to moiré superlattices in which electrons directly experience a twist-angle-dependent periodic potential. Instead, it can also be used to induce programmable",
    keywords: ['Tumor immunity', 'Macrophage polarization', 'Tumor microenvironment', 'Immunotherapy']
  },
  {
    id: 4,
    title: 'PLA2G15 is a BMP hydrolase and its targeting ameliorates lysosomal disease',
    authors: 'Zhang L, Wang Y, Li J, Wang R, Chen M, Sun K',
    journal: 'Journal of Immunology Research',
    date: '2023 Jun;15(6):1243-1258',
    doi: '10.1016/j.cger.2020.06.009',
    pmid: '33010902',
    impactFactor: 8.6,
    jcr: 'Q1',
    hasFullText: true,
    abstract: "Lysosomes catabolize lipids and other biological molecules, maintaining cellular and organismal homeostasis. Bis(monoacylglycero)phosphate (BMP), a major lipid constituent of intralysosomal vesicles, stimulates lipid-degrading enzymes and is altered in various human conditions, including neurodegenerative diseases1,2. Although lysosomal BMP synthase was recently discovered3,",
    keywords: ['Tumor immunity', 'Macrophage polarization', 'Tumor microenvironment', 'Immunotherapy']
  },
  {
    id: 5,
    title: 'Trends in the seasonal amplitude of atmospheric methane',
    authors: 'Zhang L, Wang Y, Li J, Wang R, Chen M, Sun K',
    journal: 'Journal of Immunology Research',
    date: '2023 Jun;15(6):1243-1258',
    doi: '10.1016/j.cger.2020.06.009',
    pmid: '33010902',
    impactFactor: 8.6,
    jcr: 'Q1',
    hasFullText: true,
    abstract: "Methane is an important greenhouse gas1 and its atmospheric concentration has almost tripled since pre-industrial times2-4. Atmospheric methane mixing ratios vary seasonally, with the seasonal cycle amplitude (SCA) having decreased in northern high latitudes and increased in the subtropics and tropics since the 1980s5,6. These opposing SCA trends can help understanding of long-term",
    keywords: ['Tumor immunity', 'Macrophage polarization', 'Tumor microenvironment', 'Immunotherapy']
  },
  {
    id: 6,
    title: 'Trends in the seasonal amplitude of atmospheric methane',
    authors: 'Zhang L, Wang Y, Li J, Wang R, Chen M, Sun K',
    journal: 'Journal of Immunology Research',
    date: '2023 Jun;15(6):1243-1258',
    doi: '10.1016/j.cger.2020.06.009',
    pmid: '33010902',
    impactFactor: 8.6,
    jcr: 'Q1',
    hasFullText: true,
    abstract: "Methane is an important greenhouse gas1 and its atmospheric concentration has almost tripled since pre-industrial times2-4. Atmospheric methane mixing ratios vary seasonally, with the seasonal cycle amplitude (SCA) having decreased in northern high latitudes and increased in the subtropics and tropics since the 1980s5,6. These opposing SCA trends can help understanding of long-term",
    keywords: ['Tumor immunity', 'Macrophage polarization', 'Tumor microenvironment', 'Immunotherapy']
  }
  ,
  {
    id: 7,
    title: 'Trends in the seasonal amplitude of atmospheric methane',
    authors: 'Zhang L, Wang Y, Li J, Wang R, Chen M, Sun K',
    journal: 'Journal of Immunology Research',
    date: '2023 Jun;15(6):1243-1258',
    doi: '10.1016/j.cger.2020.06.009',
    pmid: '33010902',
    impactFactor: 8.6,
    jcr: 'Q1',
    hasFullText: true,
    abstract: "Methane is an important greenhouse gas1 and its atmospheric concentration has almost tripled since pre-industrial times2-4. Atmospheric methane mixing ratios vary seasonally, with the seasonal cycle amplitude (SCA) having decreased in northern high latitudes and increased in the subtropics and tropics since the 1980s5,6. These opposing SCA trends can help understanding of long-term",
    keywords: ['Tumor immunity', 'Macrophage polarization', 'Tumor microenvironment', 'Immunotherapy']
  }
])

// 收藏相关方法
const handleBatchFavorite = () => {
    showCollectionModal.value = true
}

const handleCollectionConfirm = (data) => {
  console.log('收藏确认:', data)
}


</script>

<style lang="scss" scoped>
@import "@/assets/styles/variables";

.literature {
  background-color: #F5F5F5;
  min-height: calc(100vh - 520px);
}

// 搜索区域
.search-section {
  padding:36px 0 20px 0;

  @media (max-width: $breakpoint-md) {
    padding: $spacing-lg 0;
  }
}

.search-container {
  max-width: 893px;
  margin: 0 auto;

  @media (max-width: $breakpoint-md) {
    padding: 0 $spacing-md;
  }
}

.search-bar {
  display: flex;
  height: 60px;
  background-color: $white;
  border-radius: $border-radius-lg;
  box-shadow: $box-shadow;
  overflow: hidden;

  @media (max-width: $breakpoint-md) {
    height: 50px;
    flex-direction: row;
  }
}

.search-field {
  display: flex;
  align-items: center;
  flex: 1;
  padding: 0 $spacing-md 0 0;
  :deep(.el-select__wrapper){
    box-shadow: none!important;
    padding-left: 20px;
    font-weight: 600;
    width: 130px;
    &:hover{
      box-shadow: none!important;
    }
  }
  :deep(.el-select__selected-item ){
    color: rgba($primary-color, 1);
    font-size: $font-size-small;
  }
  :deep(.el-select__caret){
    color: $primary-color;
  }
}

.search-type {
  font-size: $font-size-large;
  font-weight: $font-weight-medium;
  color: $primary-color;
  padding-right: $spacing-md;
  white-space: nowrap;
  width: 130px;
}


.search-input {
  flex: 1;
  border: none;
  outline: none;
  font-size: $font-size-large;
  padding: $spacing-md;
  color: $primary-color;

  @media (max-width: $breakpoint-md) {
    font-size: $font-size-medium;
    padding: $spacing-sm $spacing-md;
  }

  &::placeholder {
    color: rgba($primary-color, 1);
    font-size: $font-size-small;

    @media (max-width: $breakpoint-md) {
      font-size: 14px;
    }
  }

  &:focus {
    outline: none;
  }
}

.search-button {
  width: 75px;
  height: 100%;
  background-color: $primary-color;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;

  .el-icon {
    font-size: 20px;
    color: $white;
  }
}

.search-options {
  display: flex;
  justify-content: center;
  margin-top: $spacing-lg;
  gap: $spacing-xxl;

  @media (max-width: $breakpoint-md) {
    gap: $spacing-lg;
    margin-top: $spacing-md;
  }
}

.search-option {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  color: $primary-color;
  font-size: $font-size-small;
  font-weight: $font-weight-medium;
  cursor: pointer;
  padding: 8px 16px;
  border-radius: $border-radius-md;
  transition: all 0.3s ease;

  .option-icon {
    width: 20px;
    height: 22px;
  }

  &:hover {
    background-color: rgba($primary-color, 0.1);
  }

  &.active {
    background-color:#DBE5EB;
    color: $primary-color;
    border: 1px solid rgba(10, 83, 144, 0.16);
  }
}

/* 高级检索样式 */
.advanced-search-container {
  margin-top: $spacing-lg;
  background-color: $white;
  border-radius: $border-radius-lg;
  box-shadow: $box-shadow;
  padding: $spacing-lg;
  width: 100%;
  overflow: hidden;
  transform-origin: top center;

  @media (max-width: $breakpoint-md) {
    margin-top: $spacing-md;
    padding: $spacing-md;
    border-radius: $border-radius-md;
  }

  &.v-enter-active,
  &.v-leave-active {
    transition: all 0.3s ease;
  }

  &.v-enter-from,
  &.v-leave-to {
    opacity: 0;
    transform: scaleY(0);
  }

  &.v-enter-to,
  &.v-leave-from {
    opacity: 1;
    transform: scaleY(1);
  }
}

.advanced-search-content {
  width: 100%;
}

.search-row {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  margin-bottom: $spacing-md;

  @media (max-width: $breakpoint-md) {
    flex-direction: column;
    gap: $spacing-sm;
    align-items: stretch;
  }

  :deep(.el-select__wrapper),:deep(.el-input__wrapper){
    border-radius: 8px;
  }
}

.logic-select {
  width: 100px;
}

.logic-placeholder {
  width: 100px;
}

.field-select {
  width: 150px;
}

.keyword-input {
  flex: 1;
}

.remove-condition-btn {
  color: $gray;
}

.add-condition-row {
  margin: $spacing-xs 0;
}

.add-condition-btn {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  color: $primary-color;
  padding: 0;
}

.search-query-container {
  margin-bottom: $spacing-lg;
  :deep(.el-textarea__inner){
    border-radius: 8px;
  }
}

.search-query-label {
  font-size: $font-size-small;
  color: rgb(55 65 81);
}

.search-query-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: $spacing-xs;
}

.search-query-text {
  font-family: monospace;
  color: $primary-color;
}

.copy-btn {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  color: $primary-color;
  font-size: 15px;
}

.search-history-container {
  margin-bottom: $spacing-lg;
}

.search-history-label {
  font-size: $font-size-small;
  color: rgb(55 65 81);
  margin-bottom: $spacing-xs;
}

.search-history-list {
  display: flex;
  flex-direction: column;
  gap: $spacing-xs;
}

.search-history-item {
  display: flex;
  align-items: center;
  padding: 2px $spacing-xxs;
  font-size: $font-size-small;
  background-color: #F9FAFB;
  border-radius: $border-radius-md;
}

.history-index {
  width: 40px;
  color: $gray;
}

.history-query {
  flex: 1;
  color: rgb(55 65 81);
  font-family: monospace;
}

.history-result-count {
  width: 120px;
  text-align: right;
  color: $gray;
}

.history-actions {
  display: flex;
  gap: $spacing-xs;
  margin-left: 22px;

  .edit-btn, .delete-btn {
    color: $primary-color;
    padding: 0;
    font-size: 15px;
  }
}

.search-actions {
  display: flex;
  justify-content: flex-end;
  gap: $spacing-md;
  margin-top: $spacing-lg;
}

.clear-btn {
  border-color: $gray;
  color: $gray;
}

.search-btn {
  min-width: 100px;
}

// 内容区域

.content-container {
  display: flex;
  gap: $spacing-lg;
  padding-bottom: 30px;

  @media (max-width: $breakpoint-lg) {
    width: 100%;
    flex-direction: row;
    gap: $spacing-md;
  }

  @media (max-width: $breakpoint-md) {
    flex-direction: column;
    gap: $spacing-md;
    padding: 0 $spacing-sm;
  }
}

// 左侧筛选条件
.filter-sidebar {
  width: 307px;
  background-color: $white;
  border-radius: $border-radius-lg;
  padding: $spacing-lg $spacing-lg $spacing-xxl;
  box-shadow: $box-shadow;

  @media (max-width: $breakpoint-lg) {
    width: 280px;
  }

  @media (max-width: $breakpoint-md) {
    width: 100%;
    padding: $spacing-md;
    margin-bottom: $spacing-md;

    &.mobile-hidden {
      display: none;
    }
  }
}

// 移动端筛选按钮
.mobile-filter-toggle {
  display: none;

  @media (max-width: $breakpoint-md) {
    display: flex;
    align-items: center;
    gap: $spacing-xs;
    background-color: $white;
    border-radius: $border-radius-md;
    padding: $spacing-sm $spacing-md;
    margin-bottom: $spacing-md;
    box-shadow: $box-shadow;
    cursor: pointer;
    font-size: $font-size-small;
    color: $primary-color;
    font-weight: $font-weight-medium;

    .toggle-icon {
      margin-left: auto;
      transition: transform 0.3s ease;

      &.rotated {
        transform: rotate(180deg);
      }
    }
  }
}

.filter-header {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  margin-bottom: $spacing-md;
  .filter-icon{
    width: 18px;
  }
}

.filter-title {
  font-size: 19px;
  font-weight: $font-weight-bold;
  color: $primary-color;
  margin: 0;
}

.filter-icon {
  width: 24px;
  height: 24px;
}

.filter-group {
  margin-bottom: $spacing-lg;

  @media (max-width: $breakpoint-md) {
    margin-bottom: $spacing-md;
  }
}

.filter-group-title {
  font-size: 17px;
  font-weight: $font-weight-medium;
  color: $primary-color;
  margin: $spacing-md 0 $spacing-sm;
}

.filter-options {
  display: flex;
  flex-direction: column;
  gap: 2px;
  align-content:flex-start
}

.filter-option {
  display: flex;
  align-items: center;
  font-size: 15px;
  color: $gray;

  :deep(.el-checkbox){
    flex: 1;
  }
  :deep(.el-checkbox__input) {
    margin-right: $spacing-sm;
  }

  :deep(.el-radio__input) {
    margin-right: $spacing-sm;
  }

  :deep(.el-checkbox__label),
  :deep(.el-radio__label) {
    padding-left: 0;
    font-size: $font-size-small;
    font-weight: $font-weight-regular;
    flex: 1;
  }
}

.filter-option-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.custom-range {
  margin-left: $spacing-xl;
  color: $primary-color;
  font-weight: $font-weight-medium;
  font-size: $font-size-small;
}

.count-badge {
  margin-left: $spacing-md;
  background-color: #F3F6F9;
  color: $gray;
  font-size: $font-size-small;
  padding: 2px 5px;
  border-radius: 9999px;
}

.more-types {
  color: $primary-color;
  font-size: $font-size-small;
  margin-top: $spacing-sm;
  margin-left: $spacing-xl;
  cursor: pointer;
}

.filter-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: $spacing-sm $spacing-lg;
}

.show-more-btn {
  width: 100%;
  height: 38px;
  background-color: $primary-color;
  color: $white;
  border: none;
  border-radius: $border-radius-md;
  font-size: $font-size-small;
  margin: $spacing-md 0;
  cursor: pointer;
}

// 右侧文献列表
.literature-content {
  flex: 1;
  background-color: $white;
  border-radius: $border-radius-lg;
  box-shadow: $box-shadow;
  padding: $spacing-lg;

  @media (max-width: $breakpoint-md) {
    padding: $spacing-md;
  }
}

.literature-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: $spacing-md;
  border-bottom: 1px solid #ECECEC;
  margin-bottom: $spacing-md;

  @media (max-width: $breakpoint-md) {
    flex-direction: column;
    gap: $spacing-md;
    align-items: flex-start;
  }
}

.literature-header-left {
  display: flex;
  align-items: center;
  gap: $spacing-lg;
  color: $gray;
  font-size: $font-size-small;

  @media (max-width: $breakpoint-md) {
    width: 100%;
    justify-content: space-between;
    gap: $spacing-sm;
  }
}

.select-all {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
}

.result-count {
  font-size: $font-size-medium;
  color: $primary-color;
}

.literature-header-right {
  display: flex;
  align-items: center;
  gap: $spacing-lg;

  @media (max-width: $breakpoint-md) {
    width: 100%;
    justify-content: space-between;
    gap: $spacing-sm;
    flex-wrap: wrap;
  }
}

.sort-options, .page-size {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  font-size: $font-size-small;
  color: $gray;

  @media (max-width: $breakpoint-md) {
    font-size: 14px;
    gap: 4px;
  }
}

.sort-dropdown, .page-size-dropdown {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  color: $primary-color;
  cursor: pointer;
}

.action-button {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  color: $primary-color;
  font-size: $font-size-small;
  cursor: pointer;

  @media (max-width: $breakpoint-md) {
    font-size: 14px;
    gap: 4px;
    padding: 4px 8px;
    border-radius: $border-radius-sm;
    background-color: rgba($primary-color, 0.1);
  }

  .export{
    font-size: $font-size-small;
    color: $primary-color;

    @media (max-width: $breakpoint-md) {
      font-size: 14px;
    }
  }
}

.article-list-container {
  margin-bottom: $spacing-lg;
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: $spacing-lg;

  @media (max-width: $breakpoint-md) {
    margin-top: $spacing-md;

    :deep(.el-pagination) {
      .el-pager li {
        min-width: 32px;
        height: 32px;
        line-height: 32px;
        font-size: 14px;
      }

      .btn-prev, .btn-next {
        min-width: 32px;
        height: 32px;
        line-height: 32px;
      }
    }
  }
}
:deep(.el-select__wrapper){
  border-radius: 8px;

  @media (max-width: $breakpoint-md) {
    min-height: 36px;
  }
}
// 文献类型对话框样式
.literature-type-dialog {
  .literature-type-content {
    max-height: 60vh;
    overflow-y: auto;

    @media (max-width: $breakpoint-md) {
      max-height: 50vh;
    }
  }

  .literature-type-checkboxes {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: $spacing-sm;

    @media (max-width: $breakpoint-md) {
      grid-template-columns: 1fr;
      gap: $spacing-xs;
    }
  }

  .literature-type-checkbox {
    :deep(.el-checkbox__label) {
      font-size: $font-size-small;
      line-height: 1.4;

      @media (max-width: $breakpoint-md) {
        font-size: 14px;
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: $spacing-md;

    @media (max-width: $breakpoint-md) {
      flex-direction: column-reverse;
      gap: $spacing-sm;

      .cancel-btn, .confirm-btn {
        width: 100%;
      }
    }
  }

  @media (max-width: $breakpoint-md) {
    :deep(.el-dialog__header) {
      padding: $spacing-md $spacing-md $spacing-sm;
    }

    :deep(.el-dialog__body) {
      padding: $spacing-sm $spacing-md;
    }

    :deep(.el-dialog__footer) {
      padding: $spacing-sm $spacing-md $spacing-md;
    }
  }
}

// 自定义范围对话框样式
.custom-range-content {
  padding: $spacing-md 0;

  @media (max-width: $breakpoint-md) {
    padding: $spacing-sm 0;
  }

  .range-option {
    margin-bottom: $spacing-lg;

    @media (max-width: $breakpoint-md) {
      margin-bottom: $spacing-md;
    }

    :deep(.el-radio) {
      .el-radio__label {
        color: $primary-color;
        font-weight: $font-weight-medium;
      }
    }
  }

  .date-section {
    margin-bottom: $spacing-lg;

    @media (max-width: $breakpoint-md) {
      margin-bottom: $spacing-md;
    }

    .date-label {
      font-size: $font-size-small;
      color: $gray;
      margin-bottom: $spacing-xs;
      text-transform: uppercase;
    }

    .date-inputs {
      display: flex;
      gap: $spacing-sm;

      @media (max-width: $breakpoint-md) {
        gap: $spacing-xs;
      }

      .year-input {
        flex: 2;
        :deep(.el-input__wrapper) {
          border-radius: $border-radius-md;
        }
      }

      .month-input, .day-input {
        flex: 1;
        :deep(.el-input__wrapper) {
          border-radius: $border-radius-md;
        }
      }
    }
  }

  .dialog-actions {
    display: flex;
    justify-content: flex-end;
    gap: $spacing-md;
    margin-top: $spacing-xl;

    @media (max-width: $breakpoint-md) {
      margin-top: $spacing-lg;
      gap: $spacing-sm;
    }

    .clear-btn {
      border-color: $gray;
      color: $gray;
    }

    .apply-btn {
      background-color: $primary-color;
      border-color: $primary-color;
      min-width: 80px;
    }
  }
}
</style>
