package org.biosino.lf.pds.article.service;

import org.biosino.lf.pds.article.custbean.dto.FileUploadDTO;
import org.biosino.lf.pds.article.domain.TbDdsFile;
import org.biosino.lf.pds.article.domain.TbDdsFileContent;
import org.biosino.lf.pds.common.enums.task.FileTypeEnum;

import java.util.Collection;
import java.util.List;

/**
 * 文件处理接口
 * (处理tb_dds_file表和tb_dds_file_content表数据)
 *
 * <AUTHOR>
 */
public interface ITbDdsFileService extends CommonService<TbDdsFile> {
    /**
     * 上传文件
     */
    TbDdsFile upload(FileUploadDTO uploadDTO);

    /**
     * 查询文件内容
     */
    TbDdsFileContent findContentById(final Long id);

    /**
     * 删除文件元数据和文件内容
     */
    boolean delById(final Long id);

    boolean delById(final Long id, TbDdsFile ddsFile);

    /**
     * 判断文件是否存在（元数据和内容同时存在）
     */
    boolean existsById(Long fileId);

    /**
     * 仅判断文件内容是否存在
     */
    boolean existsContentById(Long fileId);

    List<TbDdsFile> findByDocIdIn(Collection<Long> docIds);

    void deleteByDocIdAndFileType(Long docId, String name);

    boolean existsByDocIdAndMd5(Long id, String md5);

    List<TbDdsFile> findByDocIdAndType(Long docId, String name);

    List<TbDdsFile> findByDocIdsAndType(Collection<Long> docIds, FileTypeEnum typeEnum);
}
