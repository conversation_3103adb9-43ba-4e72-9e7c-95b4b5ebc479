<template>
  <div class="article-transmit-page">
    <div class="page-content">
      <!-- 搜索区域 -->

      <!-- 表格区域 -->
      <div class="table-section">
        <el-card class="table-card">
          <template #header>
            <div class="card-header">
              <h3 class="section-title">
                <el-icon><Document /></el-icon>
                我的传递文献
              </h3>
            </div>
          </template>
          <div class="search-section">
            <el-form :model="searchForm" class="search-form" :inline="true" label-width="90">
              <el-form-item label="文献编号" class="form-item">
                <el-input
                    v-model="searchForm.articleId"
                    placeholder="请输入文献编号"
                    clearable
                    class="search-input"
                />
              </el-form-item>

              <el-form-item label="状态" class="form-item">
                <el-select
                    v-model="searchForm.status"
                    placeholder="请选择状态"
                    clearable
                    class="search-input"
                >
                  <el-option label="全部" value="" />
                  <el-option label="成功" value="success" />
                  <el-option label="失败" value="failed" />
                </el-select>
              </el-form-item>

              <el-form-item label="请求时间" class="form-item date-range">
                <el-date-picker
                    v-model="searchForm.requestTimeRange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="起始时间"
                    end-placeholder="截止时间"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    class="search-input date-picker"
                />
              </el-form-item>

              <el-form-item class="form-item search-actions">
                <el-button type="primary" @click="handleSearch" class="search-btn">
                  <el-icon class="mr-1"><Search /></el-icon>
                  搜索
                </el-button>
                <el-button @click="handleReset" class="reset-btn">
                  <el-icon class="mr-1"><Refresh /></el-icon>
                  重置
                </el-button>
              </el-form-item>
            </el-form>
          </div>

          <el-table
            :data="tableData"
            v-loading="loading"
            class="transmit-table"
            stripe
            border
            empty-text="暂无数据"
          >
            <el-table-column
              prop="transmitId"
              label="传递编号"
              width="180"
              show-overflow-tooltip
            >
            </el-table-column>

            <el-table-column
              prop="articleId"
              label="文献编号"
              width="120"
              show-overflow-tooltip
            >
            </el-table-column>

            <el-table-column
              prop="title"
              label="文献标题"
              min-width="300"
              show-overflow-tooltip
            >
            </el-table-column>

            <el-table-column
              prop="requestTime"
              label="请求时间"
              width="120"
              sortable
            >
            </el-table-column>

            <el-table-column
              prop="status"
              label="状态"
              width="100"
              align="center"
            >
              <template #default="{ row }">
                <el-tag
                  :type="row.status === 'success' ? 'success' : 'danger'"
                  class="status-tag"
                >
                  {{ row.status === 'success' ? '成功' : '失败' }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column
              label="操作"
              width="100"
              align="center"
            >
              <template #default="{ row }">
                <el-button
                  type="primary"
                  size="small"
                  class="download-btn"
                  circle
                >
                  <el-icon><Download /></el-icon>
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="pagination.currentPage"
              v-model:page-size="pagination.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="total"
              layout="total, sizes, prev, pager, next, jumper"
              class="pagination"
            />
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import {
  Search,
  Refresh,
  Document,
  Download
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// 响应式数据
const loading = ref(false)
const total = ref(0)

// 搜索表单
const searchForm = reactive({
  articleId: '',
  status: '',
  requestTimeRange: null
})

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 20
})

// 表格数据
const tableData = ref([
  {
    id: 1,
    transmitId: 'D25CRQXp5lU6ueZq',
    articleId: '30715263',
    title: 'Resource utilization of electric arc furnace dust: Efficient wet desulfurization and valuable metal leaching kinetics investigation.',
    requestTime: '2025-02-12',
    status: 'success'
  },
  {
    id: 2,
    transmitId: 'A15BRTYm3kL8weRt',
    articleId: '30715264',
    title: 'Advanced materials for sustainable energy storage: A comprehensive review of lithium-ion battery technologies.',
    requestTime: '2025-02-11',
    status: 'success'
  },
  {
    id: 3,
    transmitId: 'C45FGHn7pQ9xvBnm',
    articleId: '30715265',
    title: 'Machine learning approaches in drug discovery: Current applications and future perspectives in pharmaceutical research.',
    requestTime: '2025-02-10',
    status: 'failed'
  },
  {
    id: 4,
    transmitId: 'E67IJKo9rS2yCdPq',
    articleId: '30715266',
    title: 'Climate change impacts on agricultural productivity: A global assessment using satellite data and machine learning.',
    requestTime: '2025-02-09',
    status: 'success'
  },
  {
    id: 5,
    transmitId: 'G89LMNp1tU4zEfRs',
    articleId: '30715267',
    title: 'Novel biomarkers for early detection of neurodegenerative diseases: A systematic review and meta-analysis.',
    requestTime: '2025-02-08',
    status: 'failed'
  },
  {
    id: 6,
    transmitId: 'I01OPQr3vW6aGhTu',
    articleId: '30715268',
    title: 'Quantum computing applications in cryptography: Current challenges and future opportunities for secure communications.',
    requestTime: '2025-02-07',
    status: 'success'
  },
  {
    id: 7,
    transmitId: 'I01OPQr3vW6aGhTu',
    articleId: '30715268',
    title: 'Quantum computing applications in cryptography: Current challenges and future opportunities for secure communications.',
    requestTime: '2025-02-07',
    status: 'success'
  },
  {
    id: 8,
    transmitId: 'I01OPQr3vW6aGhTu',
    articleId: '30715268',
    title: 'Quantum computing applications in cryptography: Current challenges and future opportunities for secure communications.',
    requestTime: '2025-02-07',
    status: 'success'
  },
  {
    id: 9,
    transmitId: 'I01OPQr3vW6aGhTu',
    articleId: '30715268',
    title: 'Quantum computing applications in cryptography: Current challenges and future opportunities for secure communications.',
    requestTime: '2025-02-07',
    status: 'success'
  },
  {
    id: 10,
    transmitId: 'I01OPQr3vW6aGhTu',
    articleId: '30715268',
    title: 'Quantum computing applications in cryptography: Current challenges and future opportunities for secure communications.',
    requestTime: '2025-02-07',
    status: 'success'
  }
])

// 方法

// 初始化
onMounted(() => {
  total.value = tableData.value.length
})
</script>

<style lang="scss" scoped>
@import "@/assets/styles/variables";

.article-transmit-page {
  padding: 0;
}

.page-content {
  display: flex;
  flex-direction: column;
  gap: $spacing-lg;
}

// 通用卡片样式
.search-card,
.table-card {
  background: white;
  border-radius: $border-radius-lg;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04), 0 1px 4px rgba(0, 0, 0, 0.02);
  border: 1px solid rgba(226, 232, 240, 0.5);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.06), 0 2px 8px rgba(0, 0, 0, 0.04);
    transform: translateY(-1px);
  }

  :deep(.el-card__header) {
    padding: $spacing-md $spacing-lg;
    border-bottom: 1px solid rgba(226, 232, 240, 0.6);
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.8), rgba(241, 245, 249, 0.6));
  }

  :deep(.el-card__body) {
    padding: $spacing-lg;
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .section-title {
    display: flex;
    align-items: center;
    gap: $spacing-xs;
    font-size: 18px;
    font-weight: $font-weight-bold;
    color: $primary-color;
    margin: 0;

    .el-icon {
      font-size: 28px;
      background: linear-gradient(135deg, $primary-color, #2563eb);
      color: white;
      padding: 6px;
      border-radius: 50%;
      box-shadow: 0 2px 8px rgba(4, 56, 115, 0.2);
    }
  }

  .table-info {
    font-size: $font-size-small;
    color: #6b7280;
    font-weight: $font-weight-medium;
  }
}

// 搜索表单样式
.search-form {
  .form-item {
    margin-bottom: $spacing-md;
    margin-right: $spacing-lg;

    :deep(.el-form-item__label) {
      font-weight: $font-weight-medium;
      color: $gray;
      font-size: $font-size-small;
      width: 80px;
      text-align: right;
      padding-right: $spacing-sm;
    }

    :deep(.el-form-item__content) {
      flex: 1;
      min-width: 200px;
    }

    &.date-range {
      :deep(.el-form-item__content) {
        min-width: 300px;
      }
    }

    &.search-actions {
      :deep(.el-form-item__label) {
        width: 0;
      }

      :deep(.el-form-item__content) {
        margin-left: 0;
      }
    }
  }

  .search-input {
    width: 100%;
  }

  .date-picker {
    width: 100%;
  }

  .search-btn {
    background:  $primary-color;
    border: none;
    color: white;
    font-weight: $font-weight-bold;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-1px);
    }
  }

  .reset-btn {
    background: white;
    border: 1px solid rgba(226, 232, 240, 0.8);
    color: $gray;
    font-weight: $font-weight-medium;
    transition: all 0.3s ease;

    &:hover {
      border-color: $primary-color;
      color: $primary-color;
      transform: translateY(-1px);
    }
  }
}

// 表格样式
.transmit-table {
  :deep(.el-table__header) {
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.8), rgba(241, 245, 249, 0.6));

    th {
      background: transparent;
      color: $gray;
      font-weight:500;
      font-size: $font-size-small;
      border-bottom: 2px solid rgba(226, 232, 240, 0.8);
    }
  }
}
:deep(.el-table .cell.el-tooltip){
  font-size: 16px;
}
:deep(.el-tag__content){
  font-size: 14px;
}
.download-btn{
  background: #2E4E73;
}
//分页
.pagination-wrapper{
  margin-top: 20px;
  .el-pagination{
    justify-content: center;

  }
}

// 响应式设计
@media (max-width: $breakpoint-lg) {
  .search-form {
    .form-item {
      margin-right: $spacing-md;

      :deep(.el-form-item__label) {
        width: 70px;
      }

      :deep(.el-form-item__content) {
        min-width: 180px;
      }

      &.date-range {
        :deep(.el-form-item__content) {
          min-width: 250px;
        }
      }
    }
  }

  .transmit-table {
    font-size: $font-size-small;

    :deep(.el-table__body) {
      tr td {
        padding: $spacing-xs $spacing-sm;
      }
    }
  }
}

@media (max-width: $breakpoint-md) {
  .page-content {
    gap: $spacing-md;
  }

  .search-card,
  .table-card {
    :deep(.el-card__body) {
      padding: $spacing-md;
    }
  }

  .search-form {
    :deep(.el-form--inline) {
      .el-form-item {
        display: block;
        margin-right: 0;
        margin-bottom: $spacing-sm;

        .el-form-item__label {
          width: 100%;
          text-align: left;
          padding-right: 0;
          margin-bottom: $spacing-xs;
        }

        .el-form-item__content {
          width: 100%;
          margin-left: 0;
        }
      }
    }

    .search-btn,
    .reset-btn {
      width: 100%;
      margin-bottom: $spacing-xs;
    }
  }

  .transmit-table {
    :deep(.el-table) {
      font-size: 12px;
    }

    .article-title {
      max-width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

// 加载状态样式
:deep(.el-loading-mask) {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(4px);
}

:deep(.el-loading-spinner) {
  .el-loading-text {
    color: $primary-color;
    font-weight: $font-weight-medium;
  }

  .circular {
    stroke: $primary-color;
  }
}
</style>