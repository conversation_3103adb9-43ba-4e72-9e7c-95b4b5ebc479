package org.biosino.lf.pds.common.exception;

import org.biosino.lf.pds.article.dto.PublisherValidationResult;

/**
 * 出版社验证异常
 *
 * <AUTHOR>
 */
public class PublisherValidationException extends ServiceException {

    private static final long serialVersionUID = 1L;

    /**
     * 验证结果
     */
    private final PublisherValidationResult validationResult;

    public PublisherValidationException(PublisherValidationResult validationResult) {
        super(validationResult.getErrorMessage());
        this.validationResult = validationResult;
    }

    public PublisherValidationException(String message, PublisherValidationResult validationResult) {
        super(message);
        this.validationResult = validationResult;
    }

    public PublisherValidationResult getValidationResult() {
        return validationResult;
    }
}
