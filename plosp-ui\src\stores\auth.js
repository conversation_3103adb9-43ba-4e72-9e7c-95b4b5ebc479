import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { login as loginApi, logout as logoutApi, getUserProfile } from '@/api/auth'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref(localStorage.getItem('token') || '')
  const userInfo = ref(JSON.parse(localStorage.getItem('userInfo') || 'null'))
  const isLoading = ref(false)

  // 计算属性
  const isLoggedIn = computed(() => !!token.value)
  const userName = computed(() => userInfo.value?.userName || '')
  const userEmail = computed(() => userInfo.value?.email || '')

  // 设置 token
  const setToken = (newToken) => {
    token.value = newToken
    if (newToken) {
      localStorage.setItem('token', newToken)
    } else {
      localStorage.removeItem('token')
    }
  }

  // 设置用户信息
  const setUserInfo = (info) => {
    userInfo.value = info
    if (info) {
      localStorage.setItem('userInfo', JSON.stringify(info))
    } else {
      localStorage.removeItem('userInfo')
    }
  }

  // 登录
  const login = async (loginForm) => {
    try {
      isLoading.value = true
      const response = await loginApi(loginForm)
      
      if (response.code === 200) {
        // 保存 token
        setToken(response.token)
        
        // 获取用户信息
        await fetchUserInfo()
        
        ElMessage.success('登录成功')
        return { success: true }
      } else {
        ElMessage.error(response.msg || '登录失败')
        return { success: false, message: response.msg }
      }
    } catch (error) {
      console.error('登录错误:', error)
      ElMessage.error('登录失败，请检查网络连接')
      return { success: false, message: '登录失败，请检查网络连接' }
    } finally {
      isLoading.value = false
    }
  }

  // 获取用户信息
  const fetchUserInfo = async () => {
    try {
      const response = await getUserProfile()
      if (response.code === 200) {
        setUserInfo(response.data)
        return response.data
      } else {
        console.error('获取用户信息失败:', response.msg)
        return null
      }
    } catch (error) {
      console.error('获取用户信息错误:', error)
      return null
    }
  }

  // 退出登录
  const logout = async () => {
    try {
      // 调用后端退出接口
      await logoutApi()
    } catch (error) {
      console.error('退出登录错误:', error)
    } finally {
      // 无论后端调用是否成功，都清除本地数据
      setToken('')
      setUserInfo(null)
      ElMessage.success('已退出登录')
    }
  }

  // 检查登录状态
  const checkAuth = async () => {
    if (!token.value) {
      return false
    }

    try {
      // 尝试获取用户信息来验证 token 是否有效
      const info = await fetchUserInfo()
      return !!info
    } catch (error) {
      // token 无效，清除本地数据
      setToken('')
      setUserInfo(null)
      return false
    }
  }

  // 初始化认证状态
  const initAuth = async () => {
    if (token.value) {
      const isValid = await checkAuth()
      if (!isValid) {
        // token 无效，清除数据
        setToken('')
        setUserInfo(null)
      }
    }
  }

  return {
    // 状态
    token,
    userInfo,
    isLoading,
    
    // 计算属性
    isLoggedIn,
    userName,
    userEmail,
    
    // 方法
    setToken,
    setUserInfo,
    login,
    logout,
    fetchUserInfo,
    checkAuth,
    initAuth
  }
}, {
  persist: {
    key: 'auth-store',
    storage: localStorage,
    paths: ['token', 'userInfo']
  }
})
