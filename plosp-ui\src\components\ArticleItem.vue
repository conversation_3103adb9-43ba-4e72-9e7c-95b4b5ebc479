<template>
  <div class="article-item">
    <div class="article-checkbox">
      <input type="checkbox" :id="`article-${article.id}`" />
    </div>
    <div class="article-content">
      <div class="article-header">
        <div class="source-badge">PubMed</div>
          <router-link to="./literatureDetail">
            <h3 class="article-title">
            {{ article.title }}
            </h3>
          </router-link>
      </div>
      <div class="article-authors">{{ article.authors }}</div>
      <div class="article-meta">
        <div class="meta-group">
          <div class="meta-item journal">
<!--            <el-icon></el-icon>-->
            <span>{{ article.journal }}</span>
          </div>
          <div class="meta-item">{{ article.date }}</div>
        </div>
        <div class="meta-group ml-1">
          <div class="meta-item">
            <span>PMID: </span>
            <a :href="`https://pubmed.ncbi.nlm.nih.gov/${article.pmid}`" target="_blank" class="meta-link">{{ article.pmid }}</a>
          </div>
        </div>
        <div class="meta-group ml-1">
          <div class="meta-item">
            <span>DOI: </span>
            <a :href="`https://doi.org/${article.doi}`" target="_blank" class="meta-link">{{ article.doi }}</a>
          </div>
        </div>

        <div class="meta-badges">
          <div class="meta-badge impact-factor">IF: {{ article.impactFactor }}</div>
          <div class="meta-badge jcr">JCR {{ article.jcr }}</div>
          <div class="meta-badge pdf">PDF</div>
        </div>
      </div>
      <div class="article-abstract">
        {{ article.abstract }}
      </div>
      <div class="article-keywords">
        <div class="keyword-badge" v-for="(keyword, index) in article.keywords" :key="index">
          {{ keyword }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  article: {
    type: Object,
    required: true
  }
})
</script>

<style lang="scss" scoped>
@import "@/assets/styles/variables";

.article-item {
  display: flex;
  gap: $spacing-md;
  border-bottom: 1px solid #ECECEC;
  padding: $spacing-md 0;

  @media (max-width: $breakpoint-md) {
    gap: $spacing-sm;
    padding: $spacing-sm 0;
    flex-direction: column;
  }

  &:last-child {
    border-bottom: none;
  }
}

.article-content {
  flex: 1;
}

.article-header {
  display: flex;
  gap: $spacing-xs;
  align-items: center;

  @media (max-width: $breakpoint-md) {
    flex-direction: column;
    align-items: flex-start;
    gap: $spacing-sm;
  }
}

.article-source {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
}

.source-badge {
  background-color: #DCFCE7;
  color: #166534;
  font-size: $font-size-small;
  padding: 4px 12px;
  border-radius: 9999px;
}

.article-title {
  font-size: $font-size-large;
  font-weight: $font-weight-bold;
  color: $primary-color;
  margin: 0;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;

  @media (max-width: $breakpoint-md) {
    font-size: $font-size-medium;
    line-height: 1.4;
    -webkit-line-clamp: 2;
  }
}

.article-authors {
  font-size: $font-size-small;
  color: $gray;
  margin: $spacing-xs 0;
}

.article-meta {
  display: flex;
  gap: $spacing-xs;
  margin-bottom: $spacing-xs;
  align-items: center;

  @media (max-width: $breakpoint-md) {
    flex-direction: column;
    align-items: flex-start;
    gap: $spacing-sm;
  }
}

.meta-group {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  font-size: $font-size-small;
  color: $gray;
}

.meta-icon {
  width: 18px;
  height: 18px;
  background-color: $gray;
  border-radius: 50%;
}

.meta-link {
  color: $primary-color;
  text-decoration: none;

  &:hover {
    text-decoration: underline;
  }
}

.meta-badges {
  display: flex;
  gap: $spacing-sm;
}

.meta-badge {
  font-size: 14px;
  padding: 2px 8px;
  border-radius: 12px;
}

.impact-factor {
  background-color: #EFF6FF;
  color: #1D4ED8;
}

.jcr {
  background-color: #EEF2FF;
  color: #4338CA;
}

.pdf {
  background-color: #FFFBEB;
  color: #B45309;
}

.article-abstract {
  font-size:17px;
  color: #374151;
  line-height: 1.3;
  margin: $spacing-xs 0;
  text-align: justify;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  width: 100%;
  padding-right: $spacing-sm;

  @media (max-width: $breakpoint-md) {
    font-size: $font-size-small;
    line-height: 1.4;
    -webkit-line-clamp: 3;
    padding-right: 0;
    text-align: left;
  }
}

.article-keywords {
  display: flex;
  flex-wrap: wrap;
  gap: $spacing-xs;
  margin-top: $spacing-sm;
}

.keyword-badge {
  background-color: #F2F7FB;
  color: $gray;
  font-size: $font-size-small;
  padding: 4px 12px;
  border-radius: 9999px;
}

@media (max-width: $breakpoint-md) {
  .article-checkbox {
    order: -1;
  }

  .meta-group {
    flex-direction: column;
    align-items: flex-start;
    gap: $spacing-xs;

    &.ml-1 {
      margin-left: 0!important;
    }
  }

  .meta-badges {
    flex-wrap: wrap;
    margin-top: $spacing-xs;
  }

  .meta-item {
    font-size: 14px;
  }

  .source-badge {
    font-size: 12px;
    padding: 2px 8px;
  }

  .keyword-badge {
    font-size: 12px;
    padding: 2px 8px;
  }
}
</style>
