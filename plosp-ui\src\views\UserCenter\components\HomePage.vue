<template>
  <div class="home-page">
    <div class="home-content">
      <!-- 左侧内容 -->
      <div class="left-content">
        <!-- 推荐文献 -->
        <div class="section-card">
          <div class="section-header">
            <h3 class="section-title">
              <el-icon><Reading /></el-icon>
              推荐文献
            </h3>
          </div>
          <div class="literature-list">
            <div
              v-for="article in recommendedArticles"
              :key="article.id"
              class="literature-item"
            >
              <div class="literature-content">
                <!-- 标题 -->
                <h4 class="literature-title">{{ article.title }}</h4>

                <!-- 作者 -->
                <div class="literature-authors">
                  {{ article.authors }}
                </div>

                <!-- 期刊信息和标识符在同一行 -->
                <div class="literature-meta-line">
                  <span class="journal-info">
                    <span class="journal-name">{{ article.journal }}</span>
                   
                    <span class="publication-year">{{ article.year }}</span>
                   
                    <span class="volume-info">{{ article.volume }}({{ article.issue }}):{{ article.pages }}</span>
                  </span>
                  <div class="literature-ids">
                    <span v-if="article.pmid" class="id-tag pmid-tag">
                      PMID: {{ article.pmid }}
                    </span>
                    <span v-if="article.doi" class="id-tag doi-tag">
                      DOI: {{ article.doi }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 最新浏览 -->
        <div class="section-card">
          <div class="section-header">
            <h3 class="section-title">
              <el-icon><Clock /></el-icon>
              最新浏览
            </h3>
            <router-link to="" class="view-more-btn">  更多
              <el-icon class="ml-1"><ArrowRight /></el-icon></router-link>
          </div>
          <div class="literature-list">
            <div
              v-for="article in recentArticles"
              :key="article.id"
              class="literature-item"
            >
              <div class="literature-content">
                <!-- 标题 -->
                <h4 class="literature-title">{{ article.title }}</h4>

                <!-- 作者 -->
                <div class="literature-authors">
                  {{ article.authors }}
                </div>

                <!-- 期刊信息和标识符在同一行 -->
                <div class="literature-meta-line">
                  <div class="journal-info">
                    <span class="journal-name">{{ article.journal }}</span>
                    <span class="publication-year">{{ article.year }}</span>
                   
                    <span class="volume-info">{{ article.volume }}({{ article.issue }}):{{ article.pages }}</span>
                  </div>
                  <div class="literature-ids">
                    <span v-if="article.pmid" class="id-tag pmid-tag">
                      PMID: {{ article.pmid }}
                    </span>
                    <span v-if="article.doi" class="id-tag doi-tag">
                      DOI: {{ article.doi }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧个人资料 -->
      <div class="right-content">
        <div class="section-card profile-card">
          <div class="section-header profile-header">
            <h3 class="section-title">
              <el-icon><User /></el-icon>
              个人资料
            </h3>
            <el-button type="primary" link size="small" class="edit-profile-btn" @click="goToEditProfile">
              <el-icon class="mr-1"><Edit /></el-icon>
              编辑
            </el-button>
          </div>
          <div class="profile-content">
            <div class="profile-item">
              <label class="profile-label">组织机构</label>
              <div class="profile-value">中科院生物物理研究所</div>
            </div>
            <div class="profile-item">
              <label class="profile-label">部门</label>
              <div class="profile-value">分子生物学实验室</div>
            </div>
            <div class="profile-item">
              <label class="profile-label">PI姓名</label>
              <div class="profile-value">李教授</div>
            </div>
            <div class="profile-item">
              <label class="profile-label">职位</label>
              <div class="profile-value">研究员</div>
            </div>
            <div class="profile-item">
              <label class="profile-label">电话</label>
              <div class="profile-value">138****8888</div>
            </div>
            <div class="profile-item">
              <label class="profile-label">国家/地区</label>
              <div class="profile-value">中国</div>
            </div>
            <div class="profile-item">
              <label class="profile-label">省/州</label>
              <div class="profile-value">北京市</div>
            </div>
            <div class="profile-item">
              <label class="profile-label">城市</label>
              <div class="profile-value">北京</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { Reading, Clock, User, Timer, ArrowRight, Edit } from '@element-plus/icons-vue'

const router = useRouter()

// 推荐文献数据
const recommendedArticles = ref([
  {
    id: 1,
    title: 'CRISPR-Cas9 gene editing for cancer immunotherapy: advances and challenges',
    authors: 'Zhang L, Wang X, Chen Y, Liu M, Kumar S, Patel R',
    journal: 'Nature Reviews Cancer',
    year: '2024',
    volume: '24',
    issue: '2',
    pages: '123-145',
    doi: '10.1038/s41568-024-00123-4',
    pmid: '38234567'
  },
  {
    id: 2,
    title: 'Machine learning approaches in drug discovery and development',
    authors: 'Rodriguez A, Kim JH, Nakamura T, Singh P, Mueller K',
    journal: 'Science',
    year: '2024',
    volume: '383',
    issue: '6630',
    pages: '567-582',
    doi: '10.1126/science.abcd1234',
    pmid: '38345678'
  },
  {
    id: 3,
    title: 'Single-cell RNA sequencing reveals tumor microenvironment heterogeneity',
    authors: 'Johnson R, Li W, Brown A, Davis K, Thompson M, White S',
    journal: 'Cell',
    year: '2023',
    volume: '186',
    issue: '25',
    pages: '5432-5448',
    doi: '10.1016/j.cell.2023.12.345',
    pmid: '38456789'
  },
  {
    id: 4,
    title: 'Artificial intelligence in medical imaging: current applications and future prospects',
    authors: 'Miller JD, Cohen AB, Park SY, Ahmed N, Rossi F',
    journal: 'The Lancet',
    year: '2024',
    volume: '403',
    issue: '10425',
    pages: '789-805',
    doi: '10.1016/S0140-6736(24)00567-8',
    pmid: '38567890'
  }
])

// 最新浏览数据
const recentArticles = ref([
  {
    id: 5,
    title: 'Immunotherapy resistance mechanisms in melanoma: insights from genomic analysis',
    authors: 'Smith JA, Johnson MB, Williams CD, Brown KL, Davis RM',
    journal: 'Nature Medicine',
    year: '2024',
    volume: '30',
    issue: '3',
    pages: '456-468',
    doi: '10.1038/s41591-024-00789-1',
    pmid: '38678901',
    browseTime: '2024-01-20 14:30'
  },
  {
    id: 6,
    title: 'Novel biomarkers for early detection of Alzheimer\'s disease',
    authors: 'Anderson PQ, Thompson RS, Garcia ML, Wilson JK',
    journal: 'New England Journal of Medicine',
    year: '2023',
    volume: '389',
    issue: '12',
    pages: '1123-1135',
    doi: '10.1056/NEJMoa2345678',
    pmid: '38789012',
    browseTime: '2024-01-19 16:45'
  },
  {
    id: 7,
    title: 'Precision medicine in oncology: current status and future directions',
    authors: 'Lee HY, Martinez AB, Taylor NC, Robinson DM, Clark EF',
    journal: 'Journal of Clinical Oncology',
    year: '2024',
    volume: '42',
    issue: '8',
    pages: '2789-2801',
    doi: '10.1200/JCO.23.01234',
    pmid: '38890123',
    browseTime: '2024-01-18 10:15'
  }
])

// 跳转到编辑个人资料页面
const goToEditProfile = () => {
  router.push('/edit-profile')
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/variables";

.home-page {
  padding: 0;
}

.home-content {
  display: flex;
  gap: $spacing-xl;
  align-items: flex-start;

  @media (max-width: $breakpoint-lg) {
    flex-direction: column;
    gap: $spacing-lg;
  }
}

.left-content {
  flex: 2;
  display: flex;
  flex-direction: column;
  gap: $spacing-lg;
}

.right-content {
  flex: 1;
  min-width: 300px;

  @media (max-width: $breakpoint-lg) {
    min-width: 100%;
  }
}

// 通用卡片样式
.section-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.9));
  border-radius: $border-radius-lg;
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.04),
    0 1px 4px rgba(0, 0, 0, 0.02);
  padding: $spacing-md $spacing-lg;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(226, 232, 240, 0.5);
  transition: all 0.3s ease;

  &:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.95));
    box-shadow:
      0 6px 25px rgba(0, 0, 0, 0.06),
      0 2px 8px rgba(0, 0, 0, 0.04);
    transform: translateY(-1px);
  }
}

.section-header {
  margin-bottom: $spacing-md;
  padding-bottom: $spacing-sm;
  border-bottom: 1px solid rgba(226, 232, 240, 0.6);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.view-more-btn {
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  padding: 4px 8px;
  font-size: 16px;
  font-weight: 600;
  color: #0066CD;
}

.section-title {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  font-size: $font-size-medium;
  font-weight: $font-weight-bold;
  color: $primary-color;
  margin: 0;
  position: relative;

  .el-icon {
    font-size:28px;
    background: linear-gradient(135deg, $primary-color, #2563eb);
    color: white;
    padding: 6px;
    border-radius: 50%;
    box-shadow: 0 2px 8px rgba(4, 56, 115, 0.2);
  }
}

// 文献列表样式
.literature-list {
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;
}

.literature-item {
  padding: $spacing-sm $spacing-md;
  border: 1px solid rgba(226, 232, 240, 0.6);
  border-radius: $border-radius-md;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.8));
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, $primary-color, #2563eb, #3b82f6);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

}

.literature-content {
  .literature-title {
    font-size: $font-size-medium;
    font-weight: $font-weight-bold;
    color: $primary-color;
    margin: 0 0 $spacing-xs 0;
    line-height: 1.5;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;

    &:hover {
      color: #2563eb;
      transform: translateX(2px);
    }

    &::before {
      content: '';
      position: absolute;
      left: -8px;
      top: 50%;
      transform: translateY(-50%);
      width: 3px;
      height: 0;
      background: linear-gradient(135deg, $primary-color, #2563eb);
      border-radius: 2px;
      transition: height 0.3s ease;
    }

    &:hover::before {
      height: 100%;
    }
  }

  // 作者信息样式
  .literature-authors {
    font-size: $font-size-small;
    color: #575757;
    margin-bottom: $spacing-xs;
    line-height: 1.4;
    font-weight: $font-weight-medium;
  }

  // 期刊信息和标识符同行布局
  .literature-meta-line {
    display: flex;
    align-items: center;
    gap: $spacing-md;
    flex-wrap: wrap;
    font-size: 14px;
    color: #374151;

    .journal-info {
      display: flex;
      align-items: center;
      gap: $spacing-xs;

      .journal-name {
        font-size: $font-size-small;
        white-space: nowrap;
      }

      .publication-year {
        font-size: $font-size-small;
        color: #6b7280;
        white-space: nowrap;
      }

      .volume-info {
        font-size: $font-size-small;
        color: #6b7280;
        white-space: nowrap;
      }

      .separator {
        font-size: $font-size-small;
        color: #d1d5db;
        margin: 0 4px;
        font-weight: bold;
      }
    }

    .literature-ids {
      display: flex;
      align-items: center;
      gap: $spacing-xs;
      flex-shrink: 0;

      .id-tag {
        display: inline-flex;
        align-items: center;
        padding: 3px 8px;
        border-radius: 12px;

        transition: all 0.3s ease;
        cursor: pointer;
        white-space: nowrap;

        &.pmid-tag {
          background: #F2F7FB;
          color:  #374151;

          &:hover {
            background: #dae8fa;
          }
        }

        &.doi-tag {
          background: #F2F7FB;
          color:  #374151;

          &:hover {
            background: #dae8fa;
          }
        }
      }

      .browse-time-tag {
        display: inline-flex;
        align-items: center;
        gap: 3px;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 11px;
        font-weight: $font-weight-bold;

        background: linear-gradient(135deg, rgba(245, 101, 101, 0.1), rgba(245, 101, 101, 0.05));
        color: #dc2626;
        border: 1px solid rgba(245, 101, 101, 0.2);
        transition: all 0.3s ease;
        white-space: nowrap;

        .time-icon {
          font-size: 10px;
          color: #ef4444;
        }

        &:hover {
          background: linear-gradient(135deg, rgba(245, 101, 101, 0.15), rgba(245, 101, 101, 0.08));
          border-color: rgba(245, 101, 101, 0.3);
          transform: translateY(-1px);
          box-shadow: 0 2px 6px rgba(245, 101, 101, 0.2);
        }
      }
    }
  }
}

// 个人资料卡片样式
.profile-card {
  position: sticky;
  top: 120px;

  @media (max-width: $breakpoint-lg) {
    position: static;
  }

  .edit-profile-btn {
    font-size: $font-size-small;
    padding: 6px 12px;
    color: #0066CD;
    border-radius: $border-radius-sm;

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }
}

.profile-content {
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;
}

.profile-item {
  display: flex;
  flex-direction: column;
  gap: $spacing-xxs;
  padding: $spacing-xs $spacing-sm;
  border-radius: $border-radius-md;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8), rgba(248, 250, 252, 0.6));
  border: 1px solid rgba(226, 232, 240, 0.4);
  transition: all 0.3s ease;

  &:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.8));
    border-color: rgba(148, 163, 184, 0.4);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  }

  .profile-label {
    font-size: $font-size-small;
    color: $gray;
    font-weight: $font-weight-medium;
    display: flex;
    align-items: center;
    gap: 4px;
    opacity: 0.8;
  }

  .profile-value {
    font-size: $font-size-medium;
    color: $black;
    padding: $spacing-xxs 0;
    font-weight: $font-weight-medium;

    &.points {
      color: $orange;
      font-weight: $font-weight-bold;
      font-size: $font-size-large;
      background: linear-gradient(135deg, rgba(255, 153, 0, 0.1), rgba(255, 153, 0, 0.05));
      padding: $spacing-xxs $spacing-xs;
      border-radius: $border-radius-sm;
      border: 1px solid rgba(255, 153, 0, 0.2);
      display: inline-block;
    }

    &.bio {
      line-height: 1.5;
      color: #666;
      background: linear-gradient(135deg, rgba(241, 245, 249, 0.8), rgba(248, 250, 252, 0.6));
      padding: $spacing-xs;
      border-radius: $border-radius-sm;
      border-left: 3px solid $primary-color;
      font-style: italic;
    }
  }
}

// 响应式调整
@media (max-width: $breakpoint-md) {
  .home-content {
    gap: $spacing-md;
  }

  .section-card {
    padding: $spacing-sm $spacing-md;
  }

  .literature-item {
    padding: $spacing-xs $spacing-sm;
  }

  .literature-list {
    gap: $spacing-xs;
  }

  .literature-content {
    .literature-title {
      font-size: $font-size-small;
    }

    .literature-authors {
      font-size: 11px;
      margin-bottom: $spacing-xxs;
    }

    .literature-meta-line {
      flex-direction: column;
      align-items: flex-start;
      gap: $spacing-xs;

      .journal-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 2px;

        .separator {
          font-size: 10px;
          margin: 0 2px;
        }
      }

      .literature-ids {
        gap: $spacing-xxs;
        flex-wrap: wrap;

        .id-tag {
          font-size: 9px;
          padding: 1px 6px;
        }

        .browse-time-tag {
          font-size: 9px;
          padding: 1px 6px;

          .time-icon {
            font-size: 8px;
          }
        }
      }
    }
  }

  .profile-item {
    .profile-value {
      font-size: $font-size-small;

      &.points {
        font-size: $font-size-medium;
      }
    }
  }
}

// VIP徽章样式（个人资料页面版本）
.vip-badge-profile {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
  color: #8B4513;
  padding: 6px 14px;
  border-radius: 20px;
   font-size: 14px;
  font-weight: $font-weight-bold;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow:
    0 2px 8px rgba(255, 215, 0, 0.25),
    0 1px 3px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  border: 1px solid rgba(255, 215, 0, 0.4);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;

  // 闪光效果
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: profileVipShine 4s ease-in-out infinite;
  }

  .vip-icon {
    font-size: 14px;
    color: #8B4513;
    filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.2));
  }

  .vip-text {
    font-weight: $font-weight-extrabold;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
  }

  &:hover {
    transform: scale(1.02);
    box-shadow:
      0 3px 10px rgba(255, 215, 0, 0.3),
      0 1px 4px rgba(0, 0, 0, 0.12),
      inset 0 1px 0 rgba(255, 255, 255, 0.4);
  }
}

@keyframes profileVipShine {
  0% {
    left: -100%;
  }
  50% {
    left: 100%;
  }
  100% {
    left: 100%;
  }
}
</style>