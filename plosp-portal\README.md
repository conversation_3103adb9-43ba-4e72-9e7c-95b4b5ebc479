# PLOSP 门户后端

个人科学论文库门户后端

## 概述

这是 PLOSP（个人科学论文库）门户的后端服务，旨在为 Vue 3 前端应用程序提供安全且可扩展的 API。该后端使用 Spring Boot 3.5.3 构建，并遵循与现有 `plosp-admin` 模块相同的架构模式。

## 技术栈

- **框架**：Spring Boot 3.5.3
- **安全**：Spring Security 支持 JWT 身份验证
- **数据库**：PostgreSQL 和 MyBatis-Plus 3.5.12 ORM
- **缓存**：Redis 用于会话管理和缓存
- **构建工具**：Maven 和 Java 17
- **文档**：SpringDoc OpenAPI (Swagger)
- **附加**：Druid 连接池，Elasticsearch 集成

## 功能

### 身份验证和授权
- 基于 JWT 的身份验证系统
- 通过邮箱验证进行用户注册
- 密码重置功能
- 基于角色的访问控制
- 通过令牌失效机制进行安全注销

### 用户管理
- 用户个人资料管理
- 邮箱验证系统
- 密码修改功能
- 用户积分系统集成
- 账户状态管理

### 安全功能
- 用于前端集成的 CORS 配置
- XSS 保护
- SQL 注入防护
- 速率限制（可配置）
- 安全标头配置

### API 文档
- Swagger UI 集成
- 全面的 API 文档
- 请求/响应示例
- 身份验证流程文档

## 项目结构

```
plosp-portal/
│ │ │ │ │ │ └── PlospPortalApplication.java # 主应用程序类
│ │ │ │ └── PortalSecurityConfig.java # 安全配置
│ │ └── web/
│ │ │ │ │ │ │ └── auth/ # 身份验证端点
│ │ │ │ │ └── user/ # 用户管理端点
│ │ │ │ └── dto/ # 数据传输对象
│ │ │ │ └── service/ # 业务逻辑服务
│ │ └── exception/ # 异常处理程序
│ │ │ └── src/main/resources/
│ │ ... ### 2. 配置

根据您的环境更新配置文件：

#### 开发环境 (`application-dev.yml`)
```yaml
spring:
数据源:
url: *************************************************
用户名: 您的用户名
密码: 您的密码
数据:
redis:
主机: 您的redis主机
端口: 6379
数据库: 13
```

#### 生产环境 (`application-prod.yml`)
```yaml
spring:
数据源:
url: ${DB_URL}
用户名: ${DB_USERNAME}
密码: ${DB_PASSWORD}
数据:
redis:
主机: ${REDIS_HOST}
端口: ${REDIS_PORT}
密码: ${REDIS_PASSWORD}

token:
secret: ${JWT_SECRET}
```

### 3. 构建并运行

```bash
# 导航到项目根目录
cd plosp-portal

# 构建项目
mvn clean compile

# 以开发模式运行
mvn spring-boot:run -Dspring-boot.run.profiles=dev

# 或者构建并运行 JAR 文件
mvn clean package
java -jar target/plosp-portal.jar --spring.profiles.active=dev
```

### 4. 访问应用

- **应用**: http://localhost:8082
- **API 文档**: http://localhost:8082/swagger-ui.html
- **健康检查**: http://localhost:8082/api/public/health
- **数据库监控**: http://localhost:8082/druid（仅限开发环境）

## API 端点

### 身份验证 API (`/api/auth`)
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/register` - 用户注册
- `POST /api/auth/logout` - 用户注销
- `GET /api/auth/profile` - 获取用户个人资料
- `PUT /api/auth/profile` - 更新用户个人资料
- `PUT /api/auth/change-password` - 修改密码
- `POST /api/auth/send-verification-code` - 发送验证码
- `POST /api/auth/verify-email` - 验证邮箱地址
- `POST /api/auth/forgot-password` - 请求重置密码
- `POST /api/auth/reset-passwor
