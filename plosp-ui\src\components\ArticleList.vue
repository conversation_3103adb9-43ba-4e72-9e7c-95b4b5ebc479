<template>
  <div class="article-list">
    <template v-if="articles && articles.length > 0">
      <div class="article-card" v-for="(article, index) in articles" >
        <h3 class="article-title" :title="article.title">{{ article.title }}</h3>
        <p class="article-authors">{{ article.authors }}</p>
        <div class="article-meta">
          <div class="meta-item">
            <img src="@/assets/images/journal.svg" class="meta-icon" />
            <span>{{ article.journal }}</span>
          </div>
          <div class="meta-item">
            <img src="@/assets/images/date.svg" class="meta-icon" />
            <span>{{ article.date }}</span>
          </div>
          <div class="meta-item">
            <img src="@/assets/images/thumbs.svg" class="meta-icon" />
            <span>{{ article.likes }}</span>
          </div>
        </div>
      </div>
    </template>
    <template v-else>
      <div class="article-list-empty">
        <el-icon><InfoFilled /></el-icon>
        <span>暂无文献数据</span>
      </div>
    </template>
  </div>
</template>

<script setup>
import { InfoFilled } from '@element-plus/icons-vue'

// 通过props接收文章列表数据
const props = defineProps({
  articles: {
    type: Array,
    required: true,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
})
</script>

<style lang="scss" scoped>
@import "@/assets/styles/variables";

.article-list {
  display: flex;
  flex-direction: column;
  gap: 18px;
}

.article-list-loading {
  padding: $spacing-md 0;
}

.article-list-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: $spacing-xl 0;
  color: $gray;
  text-align: center;

  .el-icon {
    font-size: 24px;
    margin-bottom: $spacing-sm;
  }

  span {
    font-size: $font-size-medium;
  }
}

.article-card {
  cursor: pointer;
  transition: all 0.2s ease;
}


.article-title {
  font-size: $font-size-medium;
  font-weight: $font-weight-bold;
  color: $primary-color;
  margin-bottom: $spacing-xxs;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  margin-top: 0;
  width: calc(100% - 10px);

  &:hover {
    color: $secondary-color;
  }
}

.article-authors {
  font-size: $font-size-small;
  color: $black;
  margin: 8px 0;
}

.article-meta {
  display: flex;
  flex-wrap: wrap;
  gap: $spacing-md;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  font-size: $font-size-small;
  color: $gray;
}

.meta-icon {
  width: 22px;
}
</style>
