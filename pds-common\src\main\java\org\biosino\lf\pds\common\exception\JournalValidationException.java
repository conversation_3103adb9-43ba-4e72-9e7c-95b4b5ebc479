package org.biosino.lf.pds.common.exception;

import org.biosino.lf.pds.article.dto.JournalValidationResult;

/**
 * 期刊验证异常
 *
 * <AUTHOR>
 */
public class JournalValidationException extends ServiceException {

    private static final long serialVersionUID = 1L;

    /**
     * 验证结果
     */
    private final JournalValidationResult validationResult;

    public JournalValidationException(JournalValidationResult validationResult) {
        super(validationResult.getErrorMessage());
        this.validationResult = validationResult;
    }

    public JournalValidationException(String message, JournalValidationResult validationResult) {
        super(message);
        this.validationResult = validationResult;
    }

    public JournalValidationResult getValidationResult() {
        return validationResult;
    }
}
